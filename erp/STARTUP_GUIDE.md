# ERP系统启动指南

## 系统概述

基于Vue 3 + Golang + Supabase的多租户ERP管理系统，包含完整的前后端实现。

## 技术栈

### 后端
- **框架**: Golang + Gin
- **ORM**: GORM
- **数据库**: Supabase (PostgreSQL)
- **认证**: JWT
- **端口**: 8081

### 前端
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **端口**: 5173

## 快速启动

### 1. 启动后端服务

```bash
cd backend

# 安装依赖
go mod tidy

# 启动Supabase后端服务
./start-supabase.sh
```

**后端服务地址**: http://localhost:8081

### 2. 启动前端服务

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

**前端应用地址**: http://localhost:5173

## 验证服务状态

### 检查后端健康状态
```bash
curl http://localhost:8081/health
```

预期响应：
```json
{
  "status": "healthy",
  "timestamp": "2025-06-26T14:51:44+08:00",
  "version": "1.0.0",
  "database": {
    "connected": true,
    "type": "supabase",
    "fallback_mode": true,
    "message": "使用REST API适配器"
  },
  "supabase": {
    "enabled": true,
    "url": "https://ibbvlzotljknwkrrnorl.supabase.co",
    "status": "connected"
  },
  "services": {
    "api": "healthy",
    "auth": "healthy"
  }
}
```

### 检查前端应用
访问 http://localhost:5173 应该看到主入口页面，提供两个登录入口选择：
- 管理员入口：系统管理员和租户管理员登录
- 用户入口：企业员工和普通用户登录

## 默认登录信息

### 系统管理员
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 系统级别管理权限

### 租户管理员
- **用户名**: `tenant_admin`
- **密码**: `tenant123`
- **权限**: 租户级别管理权限

### 普通用户
- **用户名**: `user`
- **密码**: `user123`
- **权限**: 基础用户权限

## 应用地址

- **前端应用**: http://localhost:5173
- **管理员登录**: http://localhost:5173/admin/login
- **用户登录**: http://localhost:5173/user/login
- **后端API**: http://localhost:8081
- **API文档**: http://localhost:8081/swagger/index.html

## 主要功能

### 0. 登录入口
- **双入口设计**: 分离管理员和用户登录入口
- **管理员入口**: 支持系统管理员和租户管理员登录
- **用户入口**: 支持企业员工和普通用户登录
- **桌面端优化**: 专为桌面端设计的宽屏布局

### 1. 用户管理
- 用户列表查看
- 用户信息编辑
- 用户状态管理
- 角色分配

### 2. 角色管理
- 角色创建和编辑
- 权限分配
- 角色用户查看

### 3. 租户管理
- 租户信息管理
- 租户状态控制
- 用户数量统计

### 4. 仪表板
- 系统状态监控
- 统计数据展示
- 快速操作入口

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/tenant/login` - 租户管理员登录
- `POST /api/auth/admin/login` - 系统管理员登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌

### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户
- `PUT /api/users/:id/status` - 更新用户状态

### 角色管理接口
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/:id` - 更新角色
- `DELETE /api/roles/:id` - 删除角色

### 租户管理接口
- `GET /api/tenants` - 获取租户列表
- `POST /api/tenants` - 创建租户
- `PUT /api/tenants/:id` - 更新租户
- `DELETE /api/tenants/:id` - 删除租户

## 故障排除

### 后端服务无法启动
1. **检查Go版本**
   ```bash
   go version  # 需要 >= 1.19
   ```

2. **检查端口占用**
   ```bash
   lsof -i :8081
   ```

3. **查看详细错误日志**
   ```bash
   cd backend && ./start-supabase.sh
   ```

### 前端应用无法访问
1. **检查Node.js版本**
   ```bash
   node --version  # 需要 >= 16
   ```

2. **检查端口占用**
   ```bash
   lsof -i :5173
   ```

3. **重新安装依赖**
   ```bash
   cd frontend && rm -rf node_modules && npm install
   ```

### 前后端连接问题
1. **检查API配置**
   - 确认 `frontend/.env` 中的 `VITE_API_BASE_URL=http://localhost:8081`

2. **检查CORS设置**
   - 后端已配置允许前端域名访问

3. **网络连接测试**
   ```bash
   curl http://localhost:8081/health
   ```

### 数据库连接问题
1. **Supabase配置检查**
   - 确认环境变量配置正确
   - 检查网络连接

2. **回退模式**
   - 系统会自动启用REST API适配器作为回退方案

## 开发建议

### 后端开发
1. 使用 `swag init` 生成API文档
2. 遵循RESTful API设计规范
3. 添加适当的中间件和错误处理

### 前端开发
1. 使用TypeScript提供类型安全
2. 遵循Vue 3 Composition API最佳实践
3. 使用Element Plus组件库保持UI一致性

## 部署说明

### 生产环境构建
```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd backend
go build -o main .
```

### 环境变量配置
确保生产环境中正确配置：
- Supabase连接信息
- JWT密钥
- CORS域名设置

## 支持与反馈

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查控制台错误日志
3. 提交Issue描述具体问题
