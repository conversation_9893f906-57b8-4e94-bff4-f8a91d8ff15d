-- ERP系统数据库初始化脚本

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建数据库（如果不存在）
-- 注意：在Docker初始化脚本中，数据库已经通过环境变量创建

-- 设置默认字符集
ALTER DATABASE erp_db SET timezone TO 'Asia/Shanghai';

-- 创建注释
COMMENT ON DATABASE erp_db IS 'ERP系统数据库';

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '数据库初始化完成';
    RAISE NOTICE '数据库名称: erp_db';
    RAISE NOTICE '字符集: UTF8';
    RAISE NOTICE '时区: Asia/Shanghai';
END $$;
