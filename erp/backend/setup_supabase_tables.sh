#!/bin/bash

# 通过Supabase API设置数据库表结构
echo "=== 通过Supabase API设置数据库表结构 ==="

# 加载环境变量
source ./start-supabase.sh

# 检查环境变量
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_SERVICE_KEY" ]; then
    echo "❌ 缺少必要的环境变量"
    exit 1
fi

echo "Supabase URL: $SUPABASE_URL"
echo "使用Service Key进行管理操作..."

# 创建租户表
echo ""
echo "1. 创建租户表..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "CREATE TABLE IF NOT EXISTS tenants (
      id BIGSERIAL PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      code VARCHAR(50) UNIQUE NOT NULL,
      status SMALLINT DEFAULT 1,
      settings JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );"
  }' 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 租户表创建成功"
else
    echo "❌ 租户表创建失败"
fi

# 创建用户表
echo ""
echo "2. 创建用户表..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "CREATE TABLE IF NOT EXISTS users (
      id BIGSERIAL PRIMARY KEY,
      tenant_id BIGINT REFERENCES tenants(id),
      username VARCHAR(50) UNIQUE NOT NULL,
      email VARCHAR(100) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      real_name VARCHAR(100),
      phone VARCHAR(20),
      avatar VARCHAR(255),
      user_type SMALLINT DEFAULT 1,
      status SMALLINT DEFAULT 1,
      last_login_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );"
  }' 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 用户表创建成功"
else
    echo "❌ 用户表创建失败"
fi

# 测试API连接
echo ""
echo "3. 测试API连接..."
response=$(curl -s -w "HTTP_%{http_code}" "$SUPABASE_URL/rest/v1/tenants?select=count" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY")

if [[ $response == *"HTTP_200"* ]]; then
    echo "✅ API连接正常"
else
    echo "❌ API连接失败: $response"
fi

# 创建示例租户
echo ""
echo "4. 创建示例租户..."
curl -X POST "$SUPABASE_URL/rest/v1/tenants" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -H "Prefer: return=representation" \
  -d '{
    "name": "示例企业",
    "code": "demo",
    "status": 1,
    "settings": {"theme": "default", "language": "zh-CN"}
  }' 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 示例租户创建成功"
else
    echo "❌ 示例租户创建失败"
fi

echo ""
echo "=== 数据库设置完成 ==="
echo ""
echo "接下来你可以："
echo "1. 访问 Supabase Dashboard: https://supabase.com/dashboard/project/ibbvlzotljknwkrrnorl"
echo "2. 查看表结构和数据"
echo "3. 配置Row Level Security (RLS)策略"
echo "4. 启动ERP应用程序"
