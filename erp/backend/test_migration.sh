#!/bin/bash

# 数据库迁移测试脚本

echo "=== ERP系统数据库迁移测试 ==="
echo ""

# 检查是否设置了Supabase模式
if [ "$SUPABASE_ENABLED" = "true" ]; then
    echo "🔧 使用Supabase模式进行测试"
    
    # 检查必要的环境变量
    if [ -z "$SUPABASE_DB_URL" ]; then
        echo "❌ 错误: SUPABASE_DB_URL未设置"
        exit 1
    fi
    
    echo "✅ Supabase配置检查通过"
else
    echo "🔧 使用传统PostgreSQL模式进行测试"
    
    # 检查传统数据库配置
    if [ -z "$DB_HOST" ] || [ -z "$DB_USER" ] || [ -z "$DB_NAME" ]; then
        echo "❌ 错误: 传统数据库配置不完整"
        exit 1
    fi
    
    echo "✅ PostgreSQL配置检查通过"
fi

echo ""

# 编译迁移工具
echo "1. 编译数据库迁移工具..."
if go build -o migrate ./cmd/migrate; then
    echo "✅ 迁移工具编译成功"
else
    echo "❌ 迁移工具编译失败"
    exit 1
fi

echo ""

# 运行数据库迁移
echo "2. 执行数据库迁移..."
if ./migrate; then
    echo "✅ 数据库迁移成功"
else
    echo "❌ 数据库迁移失败"
    exit 1
fi

echo ""

# 编译主应用程序
echo "3. 编译主应用程序..."
if go build -o main .; then
    echo "✅ 主应用程序编译成功"
else
    echo "❌ 主应用程序编译失败"
    exit 1
fi

echo ""

# 启动应用程序进行API测试
echo "4. 启动应用程序进行API测试..."

# 在后台启动应用程序
./main &
APP_PID=$!

# 等待应用程序启动
echo "等待应用程序启动..."
sleep 5

# 检查应用程序是否正在运行
if ! kill -0 $APP_PID 2>/dev/null; then
    echo "❌ 应用程序启动失败"
    exit 1
fi

echo "✅ 应用程序启动成功"
echo ""

# 测试API端点
echo "5. 测试API端点..."

# 测试健康检查
echo "测试健康检查端点..."
if curl -s -f http://localhost:8080/health > /dev/null; then
    echo "✅ 健康检查端点正常"
else
    echo "❌ 健康检查端点失败"
fi

# 测试用户注册API
echo "测试用户注册API..."
register_response=$(curl -s -w "%{http_code}" -o /tmp/register_test.json \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "tenant_name": "测试租户"
    }' \
    http://localhost:8080/api/v1/auth/register)

register_code="${register_response: -3}"

if [ "$register_code" = "200" ] || [ "$register_code" = "201" ]; then
    echo "✅ 用户注册API正常"
else
    echo "⚠️  用户注册API返回状态码: $register_code (可能用户已存在)"
fi

# 测试用户登录API
echo "测试用户登录API..."
login_response=$(curl -s -w "%{http_code}" -o /tmp/login_test.json \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "username": "testuser",
        "password": "testpassword123"
    }' \
    http://localhost:8080/api/v1/auth/login)

login_code="${login_response: -3}"

if [ "$login_code" = "200" ]; then
    echo "✅ 用户登录API正常"
    
    # 提取访问令牌
    if command -v jq &> /dev/null; then
        access_token=$(jq -r '.data.access_token' /tmp/login_test.json 2>/dev/null)
        if [ "$access_token" != "null" ] && [ -n "$access_token" ]; then
            echo "✅ 成功获取访问令牌"
            
            # 测试需要认证的API
            echo "测试用户信息API..."
            profile_response=$(curl -s -w "%{http_code}" -o /tmp/profile_test.json \
                -H "Authorization: Bearer $access_token" \
                http://localhost:8080/api/v1/users/profile)
            
            profile_code="${profile_response: -3}"
            
            if [ "$profile_code" = "200" ]; then
                echo "✅ 用户信息API正常"
            else
                echo "❌ 用户信息API失败 (状态码: $profile_code)"
            fi
        else
            echo "⚠️  无法提取访问令牌"
        fi
    else
        echo "⚠️  jq未安装，跳过令牌提取测试"
    fi
else
    echo "❌ 用户登录API失败 (状态码: $login_code)"
fi

echo ""

# 停止应用程序
echo "6. 停止应用程序..."
kill $APP_PID 2>/dev/null
wait $APP_PID 2>/dev/null
echo "✅ 应用程序已停止"

echo ""

# 清理临时文件
rm -f /tmp/register_test.json /tmp/login_test.json /tmp/profile_test.json

echo "=== 测试完成 ==="
echo ""

if [ "$SUPABASE_ENABLED" = "true" ]; then
    echo "🎉 Supabase模式下的所有测试完成！"
    echo ""
    echo "您的ERP系统已成功集成Supabase，可以正常使用。"
    echo ""
    echo "下一步您可以："
    echo "1. 在Supabase控制台中查看创建的数据表"
    echo "2. 设置Row Level Security (RLS) 策略"
    echo "3. 配置实时订阅功能"
    echo "4. 集成Supabase Storage用于文件上传"
else
    echo "🎉 传统PostgreSQL模式下的所有测试完成！"
    echo ""
    echo "您的ERP系统运行正常。"
fi

echo ""
echo "现在您可以使用以下命令启动生产环境："
if [ "$SUPABASE_ENABLED" = "true" ]; then
    echo "  ./start-supabase.sh"
else
    echo "  ./start.sh"
fi
