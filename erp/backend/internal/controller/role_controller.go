package controller

import (
	"strconv"

	"erp/backend/internal/dto"
	"erp/backend/internal/middleware"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"
	"github.com/gin-gonic/gin"
)

// RoleController 角色管理控制器
type RoleController struct {
	roleService *service.RoleService
}

// NewRoleController 创建角色管理控制器
func NewRoleController(roleService *service.RoleService) *RoleController {
	return &RoleController{
		roleService: roleService,
	}
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新角色，只有系统管理员可以操作
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param request body dto.CreateRoleRequest true "创建角色请求"
// @Success 200 {object} utils.Response{data=dto.RoleResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles [post]
// @Security BearerAuth
func (rc *RoleController) CreateRole(c *gin.Context) {
	var req dto.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以创建角色
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以创建角色")
		return
	}

	response, err := rc.roleService.CreateRole(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetRole 获取角色详情
// @Summary 获取角色详情
// @Description 根据角色ID获取角色详细信息
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} utils.Response{data=dto.RoleResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "角色不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/{id} [get]
// @Security BearerAuth
func (rc *RoleController) GetRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的角色ID")
		return
	}

	response, err := rc.roleService.GetRoleByID(roleID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeRoleNotFound, err.Error())
		return
	}

	// 权限检查：租户用户只能查看同租户的角色
	if !middleware.IsSystemAdminFromContext(c) {
		currentTenantID, exists := middleware.GetCurrentTenantIDFromContext(c)
		if !exists {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无法确定租户信息")
			return
		}
		
		if response.TenantID == nil || *response.TenantID != *currentTenantID {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无权查看该角色信息")
			return
		}
	}

	utils.Success(c, response)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色信息，只有系统管理员可以操作
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param request body dto.UpdateRoleRequest true "更新角色请求"
// @Success 200 {object} utils.Response{data=dto.RoleResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "角色不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/{id} [put]
// @Security BearerAuth
func (rc *RoleController) UpdateRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的角色ID")
		return
	}

	var req dto.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以更新角色
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以更新角色")
		return
	}

	response, err := rc.roleService.UpdateRole(roleID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色，只有系统管理员可以操作
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "角色不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/{id} [delete]
// @Security BearerAuth
func (rc *RoleController) DeleteRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的角色ID")
		return
	}

	// 检查权限：只有系统管理员可以删除角色
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以删除角色")
		return
	}

	err = rc.roleService.DeleteRole(roleID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, nil)
}

// GetRoleList 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页和搜索
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param tenant_id query int false "租户ID"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Success 200 {object} utils.Response{data=dto.RoleListResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles [get]
// @Security BearerAuth
func (rc *RoleController) GetRoleList(c *gin.Context) {
	var req dto.RoleListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 租户用户只能查看同租户的角色
	if !middleware.IsSystemAdminFromContext(c) {
		currentTenantID, exists := middleware.GetCurrentTenantIDFromContext(c)
		if !exists {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无法确定租户信息")
			return
		}
		req.TenantID = currentTenantID
	}

	response, err := rc.roleService.GetRoleList(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// AssignPermissions 分配权限给角色
// @Summary 分配权限给角色
// @Description 为角色分配权限，只有系统管理员可以操作
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param request body dto.AssignPermissionsRequest true "分配权限请求"
// @Success 200 {object} utils.Response "分配成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "角色不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/{id}/permissions [put]
// @Security BearerAuth
func (rc *RoleController) AssignPermissions(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的角色ID")
		return
	}

	var req dto.AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以分配权限
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以分配权限")
		return
	}

	err = rc.roleService.AssignPermissions(roleID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, nil)
}

// GetRoleStatistics 获取角色统计信息
// @Summary 获取角色统计信息
// @Description 获取角色统计信息，只有系统管理员可以查看
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=dto.RoleStatistics} "获取成功"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/statistics [get]
// @Security BearerAuth
func (rc *RoleController) GetRoleStatistics(c *gin.Context) {
	// 检查权限：只有系统管理员可以查看统计信息
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看统计信息")
		return
	}

	stats, err := rc.roleService.GetRoleStatistics()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, stats)
}

// GetRoleUsers 获取角色的用户列表
// @Summary 获取角色的用户列表
// @Description 获取指定角色的用户列表
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} utils.Response{data=dto.GetRoleUsersResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "角色不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/{id}/users [get]
// @Security BearerAuth
func (rc *RoleController) GetRoleUsers(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的角色ID")
		return
	}

	var req dto.GetRoleUsersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	response, err := rc.roleService.GetRoleUsers(roleID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// CheckRolePermission 检查角色权限
// @Summary 检查角色权限
// @Description 检查角色是否拥有指定权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param request body dto.CheckRolePermissionRequest true "检查权限请求"
// @Success 200 {object} utils.Response{data=dto.CheckRolePermissionResponse} "检查成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/roles/check-permission [post]
// @Security BearerAuth
func (rc *RoleController) CheckRolePermission(c *gin.Context) {
	var req dto.CheckRolePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	response, err := rc.roleService.CheckRolePermission(req.RoleID, req.Permission)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}
