package controller

import (
	"strconv"

	"erp/backend/internal/dto"
	"erp/backend/internal/middleware"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// TenantController 租户管理控制器
type TenantController struct {
	tenantService *service.TenantService
}

// NewTenantController 创建租户管理控制器
func NewTenantController(tenantService *service.TenantService) *TenantController {
	return &TenantController{
		tenantService: tenantService,
	}
}

// CreateTenant 创建租户
// @Summary 创建租户
// @Description 创建新租户，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.CreateTenantRequest true "创建租户请求"
// @Success 200 {object} utils.Response{data=dto.TenantResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants [post]
// @Security BearerAuth
func (tc *TenantController) CreateTenant(c *gin.Context) {
	var req dto.CreateTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以创建租户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以创建租户")
		return
	}

	response, err := tc.tenantService.CreateTenant(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetTenant 获取租户详情
// @Summary 获取租户详情
// @Description 根据租户ID获取租户详细信息
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path int true "租户ID"
// @Success 200 {object} utils.Response{data=dto.TenantResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "租户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/{id} [get]
// @Security BearerAuth
func (tc *TenantController) GetTenant(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的租户ID")
		return
	}

	// 检查权限：只有系统管理员可以查看任意租户信息
	if !middleware.IsSystemAdminFromContext(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看租户信息")
		return
	}

	response, err := tc.tenantService.GetTenantByID(tenantID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeTenantNotFound, err.Error())
		return
	}

	utils.Success(c, response)
}

// UpdateTenant 更新租户
// @Summary 更新租户
// @Description 更新租户信息，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path int true "租户ID"
// @Param request body dto.UpdateTenantRequest true "更新租户请求"
// @Success 200 {object} utils.Response{data=dto.TenantResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "租户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/{id} [put]
// @Security BearerAuth
func (tc *TenantController) UpdateTenant(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的租户ID")
		return
	}

	var req dto.UpdateTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以更新租户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以更新租户")
		return
	}

	response, err := tc.tenantService.UpdateTenant(tenantID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// DeleteTenant 删除租户
// @Summary 删除租户
// @Description 删除租户，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path int true "租户ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "租户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/{id} [delete]
// @Security BearerAuth
func (tc *TenantController) DeleteTenant(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的租户ID")
		return
	}

	// 检查权限：只有系统管理员可以删除租户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以删除租户")
		return
	}

	err = tc.tenantService.DeleteTenant(tenantID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, nil)
}

// GetTenantList 获取租户列表
// @Summary 获取租户列表
// @Description 获取租户列表，支持分页和搜索
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param status query int false "状态过滤"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Success 200 {object} utils.Response{data=dto.TenantListResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants [get]
// @Security BearerAuth
func (tc *TenantController) GetTenantList(c *gin.Context) {
	var req dto.TenantListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以查看租户列表
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看租户列表")
		return
	}

	response, err := tc.tenantService.GetTenantList(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// UpdateTenantStatus 更新租户状态
// @Summary 更新租户状态
// @Description 更新租户状态，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path int true "租户ID"
// @Param request body dto.UpdateTenantStatusRequest true "更新状态请求"
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "租户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/{id}/status [put]
// @Security BearerAuth
func (tc *TenantController) UpdateTenantStatus(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的租户ID")
		return
	}

	var req dto.UpdateTenantStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以更新租户状态
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以更新租户状态")
		return
	}

	err = tc.tenantService.UpdateTenantStatus(tenantID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, nil)
}

// GetTenantStatistics 获取租户统计信息
// @Summary 获取租户统计信息
// @Description 获取租户统计信息，只有系统管理员可以查看
// @Tags 租户管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=dto.TenantStatistics} "获取成功"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/statistics [get]
// @Security BearerAuth
func (tc *TenantController) GetTenantStatistics(c *gin.Context) {
	// 检查权限：只有系统管理员可以查看统计信息
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看统计信息")
		return
	}

	stats, err := tc.tenantService.GetTenantStatistics()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, stats)
}

// GetTenantUsers 获取租户用户列表
// @Summary 获取租户用户列表
// @Description 获取指定租户的用户列表
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path int true "租户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param status query int false "状态过滤"
// @Param user_type query int false "用户类型过滤"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Success 200 {object} utils.Response{data=dto.GetTenantUsersResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "租户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/{id}/users [get]
// @Security BearerAuth
func (tc *TenantController) GetTenantUsers(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的租户ID")
		return
	}

	var req dto.GetTenantUsersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以查看租户用户列表
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看租户用户列表")
		return
	}

	response, err := tc.tenantService.GetTenantUsers(tenantID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetTenantResourceUsage 获取租户资源使用情况
// @Summary 获取租户资源使用情况
// @Description 获取所有租户的资源使用情况统计
// @Tags 租户管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=dto.GetTenantResourceUsageResponse} "获取成功"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/resource-usage [get]
// @Security BearerAuth
func (tc *TenantController) GetTenantResourceUsage(c *gin.Context) {
	// 检查权限：只有系统管理员可以查看资源使用情况
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看资源使用情况")
		return
	}

	response, err := tc.tenantService.GetTenantResourceUsage()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// BatchUpdateTenants 批量更新租户
// @Summary 批量更新租户
// @Description 批量更新租户信息，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateTenantsRequest true "批量更新请求"
// @Success 200 {object} utils.Response{data=dto.BatchUpdateTenantsResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/batch-update [put]
// @Security BearerAuth
func (tc *TenantController) BatchUpdateTenants(c *gin.Context) {
	var req dto.BatchUpdateTenantsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以批量更新租户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以批量更新租户")
		return
	}

	response, err := tc.tenantService.BatchUpdateTenants(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// ImportTenants 导入租户
// @Summary 导入租户
// @Description 批量导入租户，只有系统管理员可以操作
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.ImportTenantsRequest true "导入租户请求"
// @Success 200 {object} utils.Response{data=dto.ImportTenantsResponse} "导入成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/tenants/import [post]
// @Security BearerAuth
func (tc *TenantController) ImportTenants(c *gin.Context) {
	var req dto.ImportTenantsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以导入租户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以导入租户")
		return
	}

	response, err := tc.tenantService.ImportTenants(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}
