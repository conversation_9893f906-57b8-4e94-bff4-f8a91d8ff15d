package controller

import (
	"erp/backend/internal/dto"
	"erp/backend/internal/middleware"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService *service.AuthService
}

// NewAuthController 创建认证控制器
func NewAuthController(authService *service.AuthService) *AuthController {
	return &AuthController{
		authService: authService,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录接口，支持租户用户和系统管理员登录
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.LoginRequest true "登录请求"
// @Success 200 {object} utils.Response{data=dto.LoginResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "认证失败"
// @Router /api/auth/login [post]
func (ac *AuthController) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 获取客户端信息
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()

	// 调用认证服务
	response, err := ac.authService.Login(&req, userAgent, ipAddress)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// TenantLogin 租户用户登录
// @Summary 租户用户登录
// @Description 租户用户专用登录接口
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.TenantLoginRequest true "租户登录请求"
// @Success 200 {object} utils.Response{data=dto.LoginResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "认证失败"
// @Router /api/auth/tenant/login [post]
func (ac *AuthController) TenantLogin(c *gin.Context) {
	var req dto.TenantLoginRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 转换为通用登录请求
	loginReq := &dto.LoginRequest{
		Username:   req.Username,
		Password:   req.Password,
		TenantCode: req.TenantCode,
		RememberMe: req.RememberMe,
	}

	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()

	response, err := ac.authService.Login(loginReq, userAgent, ipAddress)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// SystemAdminLogin 系统管理员登录
// @Summary 系统管理员登录
// @Description 系统管理员专用登录接口
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.SystemAdminLoginRequest true "系统管理员登录请求"
// @Success 200 {object} utils.Response{data=dto.LoginResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "认证失败"
// @Router /api/auth/admin/login [post]
func (ac *AuthController) SystemAdminLogin(c *gin.Context) {
	var req dto.SystemAdminLoginRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 转换为通用登录请求
	loginReq := &dto.LoginRequest{
		Username:   req.Username,
		Password:   req.Password,
		RememberMe: req.RememberMe,
	}

	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()

	response, err := ac.authService.Login(loginReq, userAgent, ipAddress)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// RefreshToken 刷新令牌
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.RefreshTokenRequest true "刷新令牌请求"
// @Success 200 {object} utils.Response{data=dto.RefreshTokenResponse} "刷新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "令牌无效"
// @Router /api/auth/refresh [post]
func (ac *AuthController) RefreshToken(c *gin.Context) {
	var req dto.RefreshTokenRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	response, err := ac.authService.RefreshToken(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeTokenInvalid, err.Error())
		return
	}

	utils.Success(c, response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出，使令牌失效
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.LogoutRequest false "登出请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "登出成功"
// @Failure 401 {object} utils.Response "未认证"
// @Router /api/auth/logout [post]
func (ac *AuthController) Logout(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		utils.Unauthorized(c)
		return
	}

	// 获取当前令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.BadRequest(c, "缺少Authorization头")
		return
	}

	tokenString := authHeader[7:] // 去掉 "Bearer " 前缀

	// 执行登出
	if err := ac.authService.Logout(userID, tokenString); err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, "登出失败")
		return
	}

	utils.Success(c, gin.H{"message": "登出成功"})
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 用户修改自己的密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.ChangePasswordRequest true "修改密码请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "修改成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Router /api/auth/change-password [post]
func (ac *AuthController) ChangePassword(c *gin.Context) {
	var req dto.ChangePasswordRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		utils.Unauthorized(c)
		return
	}

	// 修改密码
	if err := ac.authService.ChangePassword(userID, &req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "密码修改成功"})
}

// GetProfile 获取用户资料
// @Summary 获取当前用户资料
// @Description 获取当前登录用户的详细资料
// @Tags 认证
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserInfo} "获取成功"
// @Failure 401 {object} utils.Response "未认证"
// @Router /api/auth/profile [get]
func (ac *AuthController) GetProfile(c *gin.Context) {
	// 获取当前用户信息
	claims, exists := middleware.GetCurrentUser(c)
	if !exists {
		utils.Unauthorized(c)
		return
	}

	// 构建用户信息响应
	userInfo := &dto.UserInfo{
		ID:       claims.UserID,
		Username: claims.Username,
		UserType: claims.UserType,
		TenantID: claims.TenantID,
		Roles:    claims.Roles,
	}

	utils.Success(c, userInfo)
}

// ValidateToken 验证令牌
// @Summary 验证访问令牌
// @Description 验证访问令牌的有效性
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body dto.ValidateTokenRequest true "验证令牌请求"
// @Success 200 {object} utils.Response{data=dto.ValidateTokenResponse} "验证结果"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Router /api/auth/validate [post]
func (ac *AuthController) ValidateToken(c *gin.Context) {
	var req dto.ValidateTokenRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// TODO: 实现令牌验证逻辑
	// 这里可以调用JWT管理器来验证令牌

	response := &dto.ValidateTokenResponse{
		Valid: true, // 临时返回true，实际需要验证
	}

	utils.Success(c, response)
}

// GetAuthStatus 获取认证状态
// @Summary 获取当前认证状态
// @Description 获取当前用户的认证状态信息
// @Tags 认证
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.AuthStatus} "认证状态"
// @Router /api/auth/status [get]
func (ac *AuthController) GetAuthStatus(c *gin.Context) {
	// 获取当前用户信息
	claims, exists := middleware.GetCurrentUser(c)

	if !exists {
		// 未认证状态
		status := &dto.AuthStatus{
			IsAuthenticated: false,
		}
		utils.Success(c, status)
		return
	}

	// 已认证状态
	userInfo := &dto.UserInfo{
		ID:       claims.UserID,
		Username: claims.Username,
		UserType: claims.UserType,
		TenantID: claims.TenantID,
		Roles:    claims.Roles,
	}

	status := &dto.AuthStatus{
		IsAuthenticated: true,
		User:            userInfo,
		ExpiresAt:       &claims.RegisteredClaims.ExpiresAt.Time,
	}

	utils.Success(c, status)
}
