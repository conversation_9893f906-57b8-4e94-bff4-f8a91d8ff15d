package controller

import (
	"strconv"

	"erp/backend/internal/dto"
	"erp/backend/internal/middleware"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"
	"github.com/gin-gonic/gin"
)

// PermissionController 权限管理控制器
type PermissionController struct {
	permissionService *service.PermissionService
}

// NewPermissionController 创建权限管理控制器
func NewPermissionController(permissionService *service.PermissionService) *PermissionController {
	return &PermissionController{
		permissionService: permissionService,
	}
}

// CreatePermission 创建权限
// @Summary 创建权限
// @Description 创建新权限，只有系统管理员可以操作
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePermissionRequest true "创建权限请求"
// @Success 200 {object} utils.Response{data=dto.PermissionResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions [post]
// @Security BearerAuth
func (pc *PermissionController) CreatePermission(c *gin.Context) {
	var req dto.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以创建权限
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以创建权限")
		return
	}

	response, err := pc.permissionService.CreatePermission(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetPermission 获取权限详情
// @Summary 获取权限详情
// @Description 根据权限ID获取权限详细信息
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} utils.Response{data=dto.PermissionResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "权限不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/{id} [get]
// @Security BearerAuth
func (pc *PermissionController) GetPermission(c *gin.Context) {
	permissionIDStr := c.Param("id")
	permissionID, err := strconv.ParseUint(permissionIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的权限ID")
		return
	}

	response, err := pc.permissionService.GetPermissionByID(permissionID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodePermissionNotFound, err.Error())
		return
	}

	utils.Success(c, response)
}

// UpdatePermission 更新权限
// @Summary 更新权限
// @Description 更新权限信息，只有系统管理员可以操作
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Param request body dto.UpdatePermissionRequest true "更新权限请求"
// @Success 200 {object} utils.Response{data=dto.PermissionResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "权限不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/{id} [put]
// @Security BearerAuth
func (pc *PermissionController) UpdatePermission(c *gin.Context) {
	permissionIDStr := c.Param("id")
	permissionID, err := strconv.ParseUint(permissionIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的权限ID")
		return
	}

	var req dto.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 检查权限：只有系统管理员可以更新权限
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以更新权限")
		return
	}

	response, err := pc.permissionService.UpdatePermission(permissionID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// DeletePermission 删除权限
// @Summary 删除权限
// @Description 删除权限，只有系统管理员可以操作
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "权限不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/{id} [delete]
// @Security BearerAuth
func (pc *PermissionController) DeletePermission(c *gin.Context) {
	permissionIDStr := c.Param("id")
	permissionID, err := strconv.ParseUint(permissionIDStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的权限ID")
		return
	}

	// 检查权限：只有系统管理员可以删除权限
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以删除权限")
		return
	}

	err = pc.permissionService.DeletePermission(permissionID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, nil)
}

// GetPermissionList 获取权限列表
// @Summary 获取权限列表
// @Description 获取权限列表，支持分页和搜索
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param resource query string false "资源过滤"
// @Param action query string false "操作过滤"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Success 200 {object} utils.Response{data=dto.PermissionListResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions [get]
// @Security BearerAuth
func (pc *PermissionController) GetPermissionList(c *gin.Context) {
	var req dto.PermissionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	response, err := pc.permissionService.GetPermissionList(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetPermissionTree 获取权限树
// @Summary 获取权限树
// @Description 获取权限树结构，按资源分组
// @Tags 权限管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=dto.GetPermissionTreeResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/tree [get]
// @Security BearerAuth
func (pc *PermissionController) GetPermissionTree(c *gin.Context) {
	response, err := pc.permissionService.GetPermissionTree()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetRolePermissionMatrix 获取角色权限矩阵
// @Summary 获取角色权限矩阵
// @Description 获取角色权限矩阵，显示所有角色和权限的关系
// @Tags 权限管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=dto.GetRolePermissionMatrixResponse} "获取成功"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/role-matrix [get]
// @Security BearerAuth
func (pc *PermissionController) GetRolePermissionMatrix(c *gin.Context) {
	// 检查权限：只有系统管理员可以查看权限矩阵
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看权限矩阵")
		return
	}

	response, err := pc.permissionService.GetRolePermissionMatrix()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetPermissionsByResource 根据资源获取权限列表
// @Summary 根据资源获取权限列表
// @Description 根据资源名称获取该资源下的所有权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param resource path string true "资源名称"
// @Success 200 {object} utils.Response{data=[]dto.PermissionInfo} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/resource/{resource} [get]
// @Security BearerAuth
func (pc *PermissionController) GetPermissionsByResource(c *gin.Context) {
	resource := c.Param("resource")
	if resource == "" {
		utils.BadRequest(c, "资源名称不能为空")
		return
	}

	permissions, err := pc.permissionService.GetPermissionsByResource(resource)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, permissions)
}

// GetPermissionsByAction 根据操作获取权限列表
// @Summary 根据操作获取权限列表
// @Description 根据操作名称获取该操作的所有权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param action path string true "操作名称"
// @Success 200 {object} utils.Response{data=[]dto.PermissionInfo} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/action/{action} [get]
// @Security BearerAuth
func (pc *PermissionController) GetPermissionsByAction(c *gin.Context) {
	action := c.Param("action")
	if action == "" {
		utils.BadRequest(c, "操作名称不能为空")
		return
	}

	permissions, err := pc.permissionService.GetPermissionsByAction(action)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, permissions)
}

// SearchPermissions 搜索权限
// @Summary 搜索权限
// @Description 根据关键词搜索权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Success 200 {object} utils.Response{data=[]dto.PermissionInfo} "搜索成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/permissions/search [get]
// @Security BearerAuth
func (pc *PermissionController) SearchPermissions(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		utils.BadRequest(c, "搜索关键词不能为空")
		return
	}

	permissions, err := pc.permissionService.SearchPermissions(keyword)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, permissions)
}
