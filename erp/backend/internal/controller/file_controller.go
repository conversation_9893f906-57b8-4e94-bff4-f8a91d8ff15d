package controller

import (
	"strconv"

	"erp/backend/internal/service"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// FileController 文件管理控制器
type FileController struct {
	storageService *service.StorageService
}

// NewFileController 创建文件控制器实例
func NewFileController(storageService *service.StorageService) *FileController {
	return &FileController{
		storageService: storageService,
	}
}

// UploadFile 上传文件
// @Summary 上传文件
// @Description 上传文件到Supabase Storage
// @Tags 文件管理
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param file formData file true "要上传的文件"
// @Param bucket formData string false "存储桶名称" default(files)
// @Param folder formData string false "文件夹路径"
// @Success 200 {object} utils.Response{data=service.FileUploadResponse}
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/v1/files/upload [post]
func (fc *FileController) UploadFile(c *gin.Context) {
	// 检查存储服务是否启用
	if !fc.storageService.IsEnabled() {
		utils.ErrorWithMessage(c, utils.CodeServiceUnavailable, "文件存储服务未启用")
		return
	}

	// 获取当前用户信息
	userClaims, exists := c.Get("user")
	if !exists {
		utils.Unauthorized(c)
		return
	}

	claims := userClaims.(*utils.JWTClaims)

	// 解析上传请求
	var req service.FileUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 设置租户ID
	if claims.TenantID != nil {
		req.TenantID = uint(*claims.TenantID)
	}

	// 检查文件大小
	if req.File.Size > fc.storageService.GetMaxFileSize() {
		utils.BadRequest(c, "文件大小超过限制，最大允许10MB")
		return
	}

	// 检查文件类型（可选，这里允许常见的文件类型）
	allowedTypes := []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt"}
	if !fc.storageService.IsValidFileType(req.File.Filename, allowedTypes) {
		utils.BadRequest(c, "不支持的文件类型，允许的类型: "+joinStrings(allowedTypes))
		return
	}

	// 上传文件
	response, err := fc.storageService.UploadFile(c, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, "文件上传失败: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, "文件上传成功", response)
}

// GetFileURL 获取文件访问URL
// @Summary 获取文件URL
// @Description 获取文件的访问URL（公共或签名）
// @Tags 文件管理
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param bucket query string true "存储桶名称"
// @Param path query string true "文件路径"
// @Param expires_in query int false "签名URL过期时间（秒）" default(0)
// @Success 200 {object} utils.Response{data=map[string]string}
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/v1/files/url [get]
func (fc *FileController) GetFileURL(c *gin.Context) {
	// 检查存储服务是否启用
	if !fc.storageService.IsEnabled() {
		utils.ErrorWithMessage(c, utils.CodeServiceUnavailable, "文件存储服务未启用")
		return
	}

	// 获取参数
	bucket := c.Query("bucket")
	path := c.Query("path")
	expiresInStr := c.DefaultQuery("expires_in", "0")

	if bucket == "" || path == "" {
		utils.BadRequest(c, "缺少必要参数: bucket和path参数不能为空")
		return
	}

	expiresIn, err := strconv.Atoi(expiresInStr)
	if err != nil {
		utils.BadRequest(c, "expires_in参数格式错误: "+err.Error())
		return
	}

	// 获取文件URL
	url, err := fc.storageService.GetFileURL(bucket, path, expiresIn)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, "获取文件URL失败: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取文件URL成功", map[string]string{
		"url": url,
	})
}

// DeleteFile 删除文件
// @Summary 删除文件
// @Description 从存储中删除文件
// @Tags 文件管理
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param bucket query string true "存储桶名称"
// @Param path query string true "文件路径"
// @Success 200 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/v1/files [delete]
func (fc *FileController) DeleteFile(c *gin.Context) {
	// 检查存储服务是否启用
	if !fc.storageService.IsEnabled() {
		utils.ErrorWithMessage(c, utils.CodeServiceUnavailable, "文件存储服务未启用")
		return
	}

	// 获取参数
	bucket := c.Query("bucket")
	path := c.Query("path")

	if bucket == "" || path == "" {
		utils.BadRequest(c, "缺少必要参数: bucket和path参数不能为空")
		return
	}

	// 删除文件
	err := fc.storageService.DeleteFile(bucket, path)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, "删除文件失败: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, "文件删除成功", nil)
}

// ListFiles 列出文件
// @Summary 列出文件
// @Description 获取指定存储桶和文件夹下的文件列表
// @Tags 文件管理
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param bucket query string true "存储桶名称"
// @Param folder query string false "文件夹路径"
// @Param limit query int false "返回数量限制" default(50)
// @Success 200 {object} utils.Response{data=[]map[string]interface{}}
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/v1/files [get]
func (fc *FileController) ListFiles(c *gin.Context) {
	// 检查存储服务是否启用
	if !fc.storageService.IsEnabled() {
		utils.ErrorWithMessage(c, utils.CodeServiceUnavailable, "文件存储服务未启用")
		return
	}

	// 获取参数
	bucket := c.Query("bucket")
	folder := c.Query("folder")
	limitStr := c.DefaultQuery("limit", "50")

	if bucket == "" {
		utils.BadRequest(c, "缺少必要参数: bucket参数不能为空")
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		utils.BadRequest(c, "limit参数格式错误: "+err.Error())
		return
	}

	// 获取文件列表
	files, err := fc.storageService.ListFiles(bucket, folder, limit)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, "获取文件列表失败: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取文件列表成功", files)
}

// 辅助函数：连接字符串数组
func joinStrings(strs []string) string {
	result := ""
	for i, str := range strs {
		if i > 0 {
			result += ", "
		}
		result += str
	}
	return result
}
