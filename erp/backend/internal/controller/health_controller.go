package controller

import (
	"net/http"
	"os"
	"time"

	"erp/backend/internal/database"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// HealthController 健康检查控制器
type HealthController struct{}

// NewHealthController 创建健康检查控制器
func NewHealthController() *HealthController {
	return &HealthController{}
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Version   string            `json:"version"`
	Database  DatabaseStatus    `json:"database"`
	Supabase  SupabaseStatus    `json:"supabase"`
	Services  map[string]string `json:"services"`
}

// DatabaseStatus 数据库状态
type DatabaseStatus struct {
	Connected    bool   `json:"connected"`
	Type         string `json:"type"`
	FallbackMode bool   `json:"fallback_mode"`
	Message      string `json:"message"`
}

// SupabaseStatus Supabase状态
type SupabaseStatus struct {
	Enabled bool   `json:"enabled"`
	URL     string `json:"url"`
	Status  string `json:"status"`
}

// Health 健康检查
// @Summary 健康检查
// @Description 检查系统健康状态
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /health [get]
func (h *HealthController) Health(c *gin.Context) {
	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   "1.0.0",
		Services:  make(map[string]string),
	}

	// 检查数据库状态
	response.Database = h.checkDatabaseStatus()

	// 检查Supabase状态
	response.Supabase = h.checkSupabaseStatus()

	// 检查其他服务
	response.Services["api"] = "healthy"
	response.Services["auth"] = "healthy"

	// 如果数据库连接有问题但Supabase可用，仍然认为系统健康
	if !response.Database.Connected && !response.Supabase.Enabled {
		response.Status = "unhealthy"
		c.JSON(http.StatusServiceUnavailable, response)
		return
	}

	c.JSON(http.StatusOK, response)
}

// checkDatabaseStatus 检查数据库状态
func (h *HealthController) checkDatabaseStatus() DatabaseStatus {
	status := DatabaseStatus{
		Connected:    false,
		Type:         "unknown",
		FallbackMode: database.IsFallbackMode(),
		Message:      "未连接",
	}

	// 检查是否启用了Supabase
	if os.Getenv("SUPABASE_ENABLED") == "true" {
		status.Type = "supabase"

		if database.IsFallbackMode() {
			status.Connected = true
			status.Message = "使用REST API适配器"
		} else {
			// 这里可以添加实际的数据库连接检查
			status.Message = "PostgreSQL连接检查"
		}
	} else {
		status.Type = "postgresql"
		status.Message = "传统PostgreSQL模式"
	}

	return status
}

// checkSupabaseStatus 检查Supabase状态
func (h *HealthController) checkSupabaseStatus() SupabaseStatus {
	status := SupabaseStatus{
		Enabled: os.Getenv("SUPABASE_ENABLED") == "true",
		URL:     os.Getenv("SUPABASE_URL"),
		Status:  "unknown",
	}

	if status.Enabled {
		// 测试Supabase连接
		adapter := database.NewSupabaseAdapter()
		if err := adapter.TestConnection(); err != nil {
			status.Status = "error: " + err.Error()
		} else {
			status.Status = "connected"
		}
	} else {
		status.Status = "disabled"
	}

	return status
}

// Ready 就绪检查
// @Summary 就绪检查
// @Description 检查系统是否就绪
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Failure 503 {object} utils.Response
// @Router /ready [get]
func (h *HealthController) Ready(c *gin.Context) {
	// 检查关键服务是否就绪
	supabaseStatus := h.checkSupabaseStatus()

	if os.Getenv("SUPABASE_ENABLED") == "true" {
		if supabaseStatus.Status != "connected" {
			utils.ErrorWithMessage(c, utils.CodeServiceUnavailable, "Supabase服务未就绪")
			return
		}
	}

	utils.Success(c, map[string]interface{}{
		"ready":     true,
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   "系统就绪",
	})
}

// Version 版本信息
// @Summary 版本信息
// @Description 获取系统版本信息
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Router /version [get]
func (h *HealthController) Version(c *gin.Context) {
	utils.Success(c, map[string]interface{}{
		"version":     "1.0.0",
		"build_time":  "2025-06-26",
		"go_version":  "1.21+",
		"git_commit":  "latest",
		"environment": os.Getenv("SERVER_MODE"),
	})
}
