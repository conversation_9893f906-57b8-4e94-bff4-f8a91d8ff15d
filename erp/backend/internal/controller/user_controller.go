package controller

import (
	"strconv"

	"erp/backend/internal/dto"
	"erp/backend/internal/middleware"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// UserController 用户管理控制器
type UserController struct {
	userService *service.UserService
}

// NewUserController 创建用户管理控制器
func NewUserController(userService *service.UserService) *UserController {
	return &UserController{
		userService: userService,
	}
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户（需要系统管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.CreateUserRequest true "创建用户请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Router /api/users [post]
func (uc *UserController) CreateUser(c *gin.Context) {
	var req dto.CreateUserRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 检查权限：只有系统管理员可以创建用户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以创建用户")
		return
	}

	// 如果创建租户用户，需要验证租户权限
	if req.UserType == 1 { // 租户用户
		currentTenantID, exists := middleware.GetCurrentTenantIDFromContext(c)
		if !exists && !middleware.IsSystemAdminFromContext(c) {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无法确定租户信息")
			return
		}

		// 如果不是系统管理员，只能在自己的租户下创建用户
		if !middleware.IsSystemAdminFromContext(c) {
			if req.TenantID == nil || *req.TenantID != *currentTenantID {
				utils.AbortWithMessage(c, utils.CodePermissionDenied, "只能在自己的租户下创建用户")
				return
			}
		}
	}

	response, err := uc.userService.CreateUser(&req)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetUser 获取用户详情
// @Summary 获取用户详情
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Produce json
// @Param id path int true "用户ID"
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id} [get]
func (uc *UserController) GetUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	response, err := uc.userService.GetUserByID(userID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeUserNotFound, err.Error())
		return
	}

	// 权限检查：租户用户只能查看同租户的用户
	if !middleware.IsSystemAdminFromContext(c) {
		currentTenantID, exists := middleware.GetCurrentTenantIDFromContext(c)
		if !exists {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无法确定租户信息")
			return
		}

		if response.TenantID == nil || *response.TenantID != *currentTenantID {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无权查看该用户信息")
			return
		}
	}

	utils.Success(c, response)
}

// UpdateUser 更新用户
// @Summary 更新用户信息
// @Description 更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.UpdateUserRequest true "更新用户请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id} [put]
func (uc *UserController) UpdateUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	var req dto.UpdateUserRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 权限检查
	currentUserID, _ := middleware.GetCurrentUserID(c)
	isSystemAdmin := middleware.IsSystemAdminFromContext(c)

	// 用户只能更新自己的信息，或者系统管理员可以更新任何用户
	if !isSystemAdmin && currentUserID != userID {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只能更新自己的信息")
		return
	}

	response, err := uc.userService.UpdateUser(userID, &req)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户（需要系统管理员权限）
// @Tags 用户管理
// @Produce json
// @Param id path int true "用户ID"
// @Param force query bool false "是否强制删除（物理删除）"
// @Security BearerAuth
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id} [delete]
func (uc *UserController) DeleteUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只有系统管理员可以删除用户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以删除用户")
		return
	}

	// 不能删除自己
	currentUserID, _ := middleware.GetCurrentUserID(c)
	if currentUserID == userID {
		utils.BadRequest(c, "不能删除自己")
		return
	}

	force := c.Query("force") == "true"

	if err := uc.userService.DeleteUser(userID, force); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "用户删除成功"})
}

// GetUserList 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表（支持分页和筛选）
// @Tags 用户管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param user_type query int false "用户类型" Enums(1,2)
// @Param status query int false "用户状态" Enums(1,2,3)
// @Param tenant_id query int false "租户ID"
// @Param role_id query int false "角色ID"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserListResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Router /api/users [get]
func (uc *UserController) GetUserList(c *gin.Context) {
	var req dto.UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 租户用户只能查看同租户的用户
	if !middleware.IsSystemAdminFromContext(c) {
		currentTenantID, exists := middleware.GetCurrentTenantIDFromContext(c)
		if !exists {
			utils.AbortWithMessage(c, utils.CodePermissionDenied, "无法确定租户信息")
			return
		}
		req.TenantID = currentTenantID
	}

	response, err := uc.userService.GetUserList(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, response)
}

// ResetUserPassword 重置用户密码
// @Summary 重置用户密码
// @Description 管理员重置用户密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.ResetPasswordRequest true "重置密码请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "重置成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id}/reset-password [post]
func (uc *UserController) ResetUserPassword(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只有系统管理员可以重置密码
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以重置用户密码")
		return
	}

	// 从请求体获取新密码
	var req struct {
		NewPassword string `json:"new_password" binding:"required" validate:"password"`
	}
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	if err := uc.userService.ResetUserPassword(userID, req.NewPassword); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "密码重置成功"})
}

// UpdateUserStatus 更新用户状态
// @Summary 更新用户状态
// @Description 更新用户状态（启用/禁用/暂停）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.UpdateUserStatusRequest true "更新状态请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id}/status [put]
func (uc *UserController) UpdateUserStatus(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	var req dto.UpdateUserStatusRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 检查权限：只有系统管理员可以更新用户状态
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以更新用户状态")
		return
	}

	// 不能更新自己的状态
	currentUserID, _ := middleware.GetCurrentUserID(c)
	if currentUserID == userID {
		utils.BadRequest(c, "不能更新自己的状态")
		return
	}

	if err := uc.userService.UpdateUserStatus(userID, &req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "用户状态更新成功"})
}

// AssignRoles 分配角色给用户
// @Summary 分配角色给用户
// @Description 为用户分配角色（需要系统管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.AssignRolesRequest true "分配角色请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "分配成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /api/users/{id}/roles [put]
func (uc *UserController) AssignRoles(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		utils.BadRequest(c, "无效的用户ID")
		return
	}

	var req dto.AssignRolesRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 检查权限：只有系统管理员可以分配角色
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以分配角色")
		return
	}

	if err := uc.userService.AssignRoles(userID, &req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "角色分配成功"})
}

// GetUserStatistics 获取用户统计信息
// @Summary 获取用户统计信息
// @Description 获取用户统计信息（需要系统管理员权限）
// @Tags 用户管理
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=dto.UserStatistics} "获取成功"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Router /api/users/statistics [get]
func (uc *UserController) GetUserStatistics(c *gin.Context) {
	// 检查权限：只有系统管理员可以查看统计信息
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以查看统计信息")
		return
	}

	stats, err := uc.userService.GetUserStatistics()
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInternalServerError, err.Error())
		return
	}

	utils.Success(c, stats)
}

// BatchUpdateUsers 批量更新用户
// @Summary 批量更新用户
// @Description 批量更新用户信息（需要系统管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateUsersRequest true "批量更新请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Router /api/users/batch-update [put]
func (uc *UserController) BatchUpdateUsers(c *gin.Context) {
	var req dto.BatchUpdateUsersRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 检查权限：只有系统管理员可以批量更新用户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以批量更新用户")
		return
	}

	if err := uc.userService.BatchUpdateUsers(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "批量更新成功"})
}

// BatchDeleteUsers 批量删除用户
// @Summary 批量删除用户
// @Description 批量删除用户（需要系统管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.BatchDeleteUsersRequest true "批量删除请求"
// @Security BearerAuth
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未认证"
// @Failure 403 {object} utils.Response "权限不足"
// @Router /api/users/batch-delete [delete]
func (uc *UserController) BatchDeleteUsers(c *gin.Context) {
	var req dto.BatchDeleteUsersRequest
	if err := utils.BindAndValidate(c, &req); err != nil {
		return
	}

	// 检查权限：只有系统管理员可以批量删除用户
	if !middleware.IsSystemAdmin(c) {
		utils.AbortWithMessage(c, utils.CodePermissionDenied, "只有系统管理员可以批量删除用户")
		return
	}

	// 检查是否包含当前用户
	currentUserID, _ := middleware.GetCurrentUserID(c)
	for _, userID := range req.UserIDs {
		if userID == currentUserID {
			utils.BadRequest(c, "不能删除自己")
			return
		}
	}

	if err := uc.userService.BatchDeleteUsers(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.Success(c, gin.H{"message": "批量删除成功"})
}
