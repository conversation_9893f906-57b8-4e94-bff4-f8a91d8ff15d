package routes

import (
	"erp/backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupHealthRoutes 设置健康检查路由
func SetupHealthRoutes(router *gin.Engine) {
	healthController := controller.NewHealthController()

	// 健康检查路由（不需要认证）
	router.GET("/health", healthController.Health)
	router.GET("/ready", healthController.Ready)
	router.GET("/version", healthController.Version)

	// 兼容性路由
	router.GET("/api/health", healthController.Health)
	router.GET("/api/ready", healthController.Ready)
	router.GET("/api/version", healthController.Version)
}
