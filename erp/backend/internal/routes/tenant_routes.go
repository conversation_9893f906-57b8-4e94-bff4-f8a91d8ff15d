package routes

import (
	"erp/backend/internal/controller"
	"erp/backend/internal/middleware"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// RegisterTenantRoutes 注册租户管理路由
func RegisterTenantRoutes(router *gin.Engine, tenantController *controller.TenantController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) {
	// 租户管理路由组 - 需要认证和系统管理员权限
	tenantGroup := router.Group("/api/tenants")
	tenantGroup.Use(middleware.AuthMiddleware(jwtManager, blacklist))
	tenantGroup.Use(middleware.RequireSystemAdmin())
	{
		// 基础CRUD操作
		tenantGroup.POST("", tenantController.CreateTenant)       // 创建租户
		tenantGroup.GET("", tenantController.GetTenantList)       // 获取租户列表
		tenantGroup.GET("/:id", tenantController.GetTenant)       // 获取租户详情
		tenantGroup.PUT("/:id", tenantController.UpdateTenant)    // 更新租户
		tenantGroup.DELETE("/:id", tenantController.DeleteTenant) // 删除租户

		// 状态管理
		tenantGroup.PUT("/:id/status", tenantController.UpdateTenantStatus) // 更新租户状态

		// 统计信息
		tenantGroup.GET("/statistics", tenantController.GetTenantStatistics) // 获取租户统计

		// 资源使用情况
		tenantGroup.GET("/resource-usage", tenantController.GetTenantResourceUsage) // 获取资源使用情况

		// 租户用户管理
		tenantGroup.GET("/:id/users", tenantController.GetTenantUsers) // 获取租户用户列表

		// 批量操作
		tenantGroup.PUT("/batch-update", tenantController.BatchUpdateTenants) // 批量更新租户
		tenantGroup.POST("/import", tenantController.ImportTenants)           // 导入租户
	}
}
