package routes

import (
	"erp/backend/internal/controller"
	"erp/backend/internal/middleware"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// UserRoutes 用户管理路由配置
type UserRoutes struct {
	userController *controller.UserController
	jwtManager     *utils.JWTManager
	blacklist      utils.TokenBlacklist
}

// NewUserRoutes 创建用户管理路由
func NewUserRoutes(userController *controller.UserController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) *UserRoutes {
	return &UserRoutes{
		userController: userController,
		jwtManager:     jwtManager,
		blacklist:      blacklist,
	}
}

// RegisterRoutes 注册用户管理路由
func (ur *UserRoutes) RegisterRoutes(router *gin.Engine) {
	// 用户管理路由组
	userGroup := router.Group("/api/users")
	userGroup.Use(middleware.AuthMiddleware(ur.jwt<PERSON>anager, ur.blacklist))
	{
		// 用户CRUD操作
		userGroup.POST("", ur.userController.CreateUser)       // 创建用户
		userGroup.GET("", ur.userController.GetUserList)       // 获取用户列表
		userGroup.GET("/:id", ur.userController.GetUser)       // 获取用户详情
		userGroup.PUT("/:id", ur.userController.UpdateUser)    // 更新用户
		userGroup.DELETE("/:id", ur.userController.DeleteUser) // 删除用户

		// 用户状态管理
		userGroup.PUT("/:id/status", ur.userController.UpdateUserStatus) // 更新用户状态

		// 用户角色管理
		userGroup.PUT("/:id/roles", ur.userController.AssignRoles) // 分配角色

		// 密码管理
		userGroup.POST("/:id/reset-password", ur.userController.ResetUserPassword) // 重置密码

		// 批量操作
		userGroup.PUT("/batch-update", ur.userController.BatchUpdateUsers)    // 批量更新用户
		userGroup.DELETE("/batch-delete", ur.userController.BatchDeleteUsers) // 批量删除用户

		// 统计信息
		userGroup.GET("/statistics", ur.userController.GetUserStatistics) // 获取用户统计
	}
}

// RegisterTenantRoutes 注册租户用户管理路由
func (ur *UserRoutes) RegisterTenantRoutes(router *gin.Engine) {
	// 租户用户管理路由
	tenantUserGroup := router.Group("/api/tenant/users")
	tenantUserGroup.Use(middleware.TenantMiddleware())
	tenantUserGroup.Use(middleware.AuthMiddleware(ur.jwtManager, ur.blacklist))
	tenantUserGroup.Use(middleware.RequireTenantUser())
	{
		// 租户内用户管理（权限受限）
		tenantUserGroup.GET("", ur.userController.GetUserList)    // 获取租户用户列表
		tenantUserGroup.GET("/:id", ur.userController.GetUser)    // 获取用户详情
		tenantUserGroup.PUT("/:id", ur.userController.UpdateUser) // 更新用户（仅限自己）
	}
}

// RegisterAdminRoutes 注册系统管理员用户管理路由
func (ur *UserRoutes) RegisterAdminRoutes(router *gin.Engine) {
	// 系统管理员用户管理路由
	adminUserGroup := router.Group("/api/admin/users")
	adminUserGroup.Use(middleware.AuthMiddleware(ur.jwtManager, ur.blacklist))
	adminUserGroup.Use(middleware.RequireSystemAdmin())
	{
		// 完整的用户管理功能
		adminUserGroup.POST("", ur.userController.CreateUser)       // 创建用户
		adminUserGroup.GET("", ur.userController.GetUserList)       // 获取用户列表
		adminUserGroup.GET("/:id", ur.userController.GetUser)       // 获取用户详情
		adminUserGroup.PUT("/:id", ur.userController.UpdateUser)    // 更新用户
		adminUserGroup.DELETE("/:id", ur.userController.DeleteUser) // 删除用户

		// 用户状态管理
		adminUserGroup.PUT("/:id/status", ur.userController.UpdateUserStatus) // 更新用户状态

		// 用户角色管理
		adminUserGroup.PUT("/:id/roles", ur.userController.AssignRoles) // 分配角色

		// 密码管理
		adminUserGroup.POST("/:id/reset-password", ur.userController.ResetUserPassword) // 重置密码

		// 批量操作
		adminUserGroup.PUT("/batch-update", ur.userController.BatchUpdateUsers)    // 批量更新用户
		adminUserGroup.DELETE("/batch-delete", ur.userController.BatchDeleteUsers) // 批量删除用户

		// 统计信息
		adminUserGroup.GET("/statistics", ur.userController.GetUserStatistics) // 获取用户统计
	}
}

// RegisterPublicRoutes 注册公开用户路由（如果有的话）
func (ur *UserRoutes) RegisterPublicRoutes(router *gin.Engine) {
	// 公开用户路由（目前没有公开的用户管理接口）
	publicUserGroup := router.Group("/api/public/users")
	{
		// 可以添加一些公开的用户相关接口，比如用户名检查等
		_ = publicUserGroup // 暂时没有公开接口
	}
}

// RegisterAllRoutes 注册所有用户管理路由
func (ur *UserRoutes) RegisterAllRoutes(router *gin.Engine) {
	// 注册基本用户管理路由
	ur.RegisterRoutes(router)

	// 注册租户用户管理路由
	ur.RegisterTenantRoutes(router)

	// 注册管理员用户管理路由
	ur.RegisterAdminRoutes(router)

	// 注册公开用户路由
	ur.RegisterPublicRoutes(router)
}

// UserRouteConfig 用户路由配置
type UserRouteConfig struct {
	EnableTenantRoutes bool
	EnableAdminRoutes  bool
	EnablePublicRoutes bool
	EnableBatchOps     bool // 是否启用批量操作
	EnableStatistics   bool // 是否启用统计功能
}

// RegisterRoutesWithConfig 根据配置注册路由
func (ur *UserRoutes) RegisterRoutesWithConfig(router *gin.Engine, config *UserRouteConfig) {
	// 基本用户管理路由
	userGroup := router.Group("/api/users")
	userGroup.Use(middleware.AuthMiddleware(ur.jwtManager, ur.blacklist))
	{
		// 基本CRUD操作
		userGroup.POST("", ur.userController.CreateUser)
		userGroup.GET("", ur.userController.GetUserList)
		userGroup.GET("/:id", ur.userController.GetUser)
		userGroup.PUT("/:id", ur.userController.UpdateUser)
		userGroup.DELETE("/:id", ur.userController.DeleteUser)

		// 用户状态和角色管理
		userGroup.PUT("/:id/status", ur.userController.UpdateUserStatus)
		userGroup.PUT("/:id/roles", ur.userController.AssignRoles)
		userGroup.POST("/:id/reset-password", ur.userController.ResetUserPassword)

		// 根据配置启用批量操作
		if config.EnableBatchOps {
			userGroup.PUT("/batch-update", ur.userController.BatchUpdateUsers)
			userGroup.DELETE("/batch-delete", ur.userController.BatchDeleteUsers)
		}

		// 根据配置启用统计功能
		if config.EnableStatistics {
			userGroup.GET("/statistics", ur.userController.GetUserStatistics)
		}
	}

	// 根据配置注册其他路由
	if config.EnableTenantRoutes {
		ur.RegisterTenantRoutes(router)
	}

	if config.EnableAdminRoutes {
		ur.RegisterAdminRoutes(router)
	}

	if config.EnablePublicRoutes {
		ur.RegisterPublicRoutes(router)
	}
}

// GetUserMiddleware 获取用户相关中间件
func (ur *UserRoutes) GetUserMiddleware() gin.HandlerFunc {
	return middleware.AuthMiddleware(ur.jwtManager, ur.blacklist)
}

// GetTenantUserMiddleware 获取租户用户中间件
func (ur *UserRoutes) GetTenantUserMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		middleware.TenantMiddleware()(c)
		if c.IsAborted() {
			return
		}
		middleware.AuthMiddleware(ur.jwtManager, ur.blacklist)(c)
		if c.IsAborted() {
			return
		}
		middleware.RequireTenantUser()(c)
	})
}

// GetSystemAdminMiddleware 获取系统管理员中间件
func (ur *UserRoutes) GetSystemAdminMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		middleware.AuthMiddleware(ur.jwtManager, ur.blacklist)(c)
		if c.IsAborted() {
			return
		}
		middleware.RequireSystemAdmin()(c)
	})
}

// SetupUserRouteMiddleware 设置用户路由中间件
func (ur *UserRoutes) SetupUserRouteMiddleware(router *gin.Engine) {
	// 用户管理相关的全局中间件可以在这里设置
	// 比如用户操作日志记录等
}

// RegisterUserManagementRoutes 注册完整的用户管理路由系统
func (ur *UserRoutes) RegisterUserManagementRoutes(router *gin.Engine) {
	// 设置中间件
	ur.SetupUserRouteMiddleware(router)

	// 注册所有路由
	ur.RegisterAllRoutes(router)
}

// RegisterUserRoutes 注册用户路由（包级别函数）
func RegisterUserRoutes(router *gin.Engine, userController *controller.UserController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) {
	userRoutes := NewUserRoutes(userController, jwtManager, blacklist)
	userRoutes.RegisterAllRoutes(router)
}
