package routes

import (
	"erp/backend/internal/controller"
	"erp/backend/internal/middleware"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// AuthRoutes 认证路由配置
type AuthRoutes struct {
	authController *controller.AuthController
	jwtManager     *utils.JWTManager
	blacklist      utils.TokenBlacklist
}

// NewAuthRoutes 创建认证路由
func NewAuthRoutes(authController *controller.AuthController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) *AuthRoutes {
	return &AuthRoutes{
		authController: authController,
		jwtManager:     jwtManager,
		blacklist:      blacklist,
	}
}

// RegisterRoutes 注册认证相关路由
func (ar *AuthRoutes) RegisterRoutes(router *gin.Engine) {
	// 认证路由组
	authGroup := router.Group("/api/auth")
	{
		// 公开路由（不需要认证）
		authGroup.POST("/login", ar.authController.Login)
		authGroup.POST("/tenant/login", ar.authController.TenantLogin)
		authGroup.POST("/admin/login", ar.authController.SystemAdminLogin)
		authGroup.POST("/refresh", ar.authController.RefreshToken)
		authGroup.POST("/validate", ar.authController.ValidateToken)

		// 需要认证的路由
		authenticated := authGroup.Group("")
		authenticated.Use(middleware.AuthMiddleware(ar.jwtManager, ar.blacklist))
		{
			authenticated.POST("/logout", ar.authController.Logout)
			authenticated.POST("/change-password", ar.authController.ChangePassword)
			authenticated.GET("/profile", ar.authController.GetProfile)
			authenticated.GET("/status", ar.authController.GetAuthStatus)
		}
	}
}

// RegisterPublicRoutes 注册公开认证路由（不需要认证的路由）
func (ar *AuthRoutes) RegisterPublicRoutes(router *gin.Engine) {
	// 公开认证路由
	publicAuth := router.Group("/api/public/auth")
	{
		publicAuth.POST("/login", ar.authController.Login)
		publicAuth.POST("/tenant/login", ar.authController.TenantLogin)
		publicAuth.POST("/admin/login", ar.authController.SystemAdminLogin)
		publicAuth.POST("/refresh", ar.authController.RefreshToken)
		publicAuth.POST("/validate", ar.authController.ValidateToken)
	}
}

// RegisterProtectedRoutes 注册受保护的认证路由（需要认证）
func (ar *AuthRoutes) RegisterProtectedRoutes(router *gin.Engine) {
	// 受保护的认证路由
	protectedAuth := router.Group("/api/protected/auth")
	protectedAuth.Use(middleware.AuthMiddleware(ar.jwtManager, ar.blacklist))
	{
		protectedAuth.POST("/logout", ar.authController.Logout)
		protectedAuth.POST("/change-password", ar.authController.ChangePassword)
		protectedAuth.GET("/profile", ar.authController.GetProfile)
		protectedAuth.GET("/status", ar.authController.GetAuthStatus)
	}
}

// RegisterTenantRoutes 注册租户认证路由
func (ar *AuthRoutes) RegisterTenantRoutes(router *gin.Engine) {
	// 租户认证路由
	tenantAuth := router.Group("/api/tenant/auth")
	tenantAuth.Use(middleware.TenantMiddleware())
	{
		// 租户用户登录
		tenantAuth.POST("/login", ar.authController.TenantLogin)

		// 需要认证的租户路由
		authenticated := tenantAuth.Group("")
		authenticated.Use(middleware.AuthMiddleware(ar.jwtManager, ar.blacklist))
		authenticated.Use(middleware.RequireTenantUser())
		{
			authenticated.POST("/logout", ar.authController.Logout)
			authenticated.POST("/change-password", ar.authController.ChangePassword)
			authenticated.GET("/profile", ar.authController.GetProfile)
			authenticated.GET("/status", ar.authController.GetAuthStatus)
		}
	}
}

// RegisterAdminRoutes 注册系统管理员认证路由
func (ar *AuthRoutes) RegisterAdminRoutes(router *gin.Engine) {
	// 系统管理员认证路由
	adminAuth := router.Group("/api/admin/auth")
	{
		// 系统管理员登录
		adminAuth.POST("/login", ar.authController.SystemAdminLogin)

		// 需要认证的管理员路由
		authenticated := adminAuth.Group("")
		authenticated.Use(middleware.AuthMiddleware(ar.jwtManager, ar.blacklist))
		authenticated.Use(middleware.RequireSystemAdmin())
		{
			authenticated.POST("/logout", ar.authController.Logout)
			authenticated.POST("/change-password", ar.authController.ChangePassword)
			authenticated.GET("/profile", ar.authController.GetProfile)
			authenticated.GET("/status", ar.authController.GetAuthStatus)
		}
	}
}

// RegisterAllRoutes 注册所有认证路由
func (ar *AuthRoutes) RegisterAllRoutes(router *gin.Engine) {
	// 注册基本认证路由
	ar.RegisterRoutes(router)

	// 注册租户认证路由
	ar.RegisterTenantRoutes(router)

	// 注册管理员认证路由
	ar.RegisterAdminRoutes(router)
}

// SetupAuthMiddleware 设置认证中间件
func (ar *AuthRoutes) SetupAuthMiddleware(router *gin.Engine) {
	// 全局中间件
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.LoggerMiddleware())
	router.Use(middleware.RecoveryMiddleware())
	router.Use(middleware.SecurityHeadersMiddleware())

	// CORS中间件
	allowedOrigins := []string{"*"} // 生产环境应该配置具体的域名
	allowedMethods := []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	allowedHeaders := []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Request-ID"}
	router.Use(middleware.CORSMiddleware(allowedOrigins, allowedMethods, allowedHeaders))

	// 健康检查
	router.Use(middleware.HealthCheckMiddleware("/health"))

	// 内容类型验证
	allowedContentTypes := []string{"application/json", "multipart/form-data"}
	router.Use(middleware.ValidateContentTypeMiddleware(allowedContentTypes))
}

// AuthRouteConfig 认证路由配置
type AuthRouteConfig struct {
	EnableTenantRoutes bool
	EnableAdminRoutes  bool
	EnablePublicRoutes bool
	RateLimitEnabled   bool
	RateLimitRequests  int
	RateLimitWindow    int // 秒
}

// RegisterRoutesWithConfig 根据配置注册路由
func (ar *AuthRoutes) RegisterRoutesWithConfig(router *gin.Engine, config *AuthRouteConfig) {
	// 设置基础中间件
	ar.SetupAuthMiddleware(router)

	// 注册基本认证路由
	ar.RegisterRoutes(router)

	// 根据配置注册其他路由
	if config.EnablePublicRoutes {
		ar.RegisterPublicRoutes(router)
	}

	if config.EnableTenantRoutes {
		ar.RegisterTenantRoutes(router)
	}

	if config.EnableAdminRoutes {
		ar.RegisterAdminRoutes(router)
	}
}

// GetAuthMiddleware 获取认证中间件
func (ar *AuthRoutes) GetAuthMiddleware() gin.HandlerFunc {
	return middleware.AuthMiddleware(ar.jwtManager, ar.blacklist)
}

// GetOptionalAuthMiddleware 获取可选认证中间件
func (ar *AuthRoutes) GetOptionalAuthMiddleware() gin.HandlerFunc {
	return middleware.OptionalAuthMiddleware(ar.jwtManager, ar.blacklist)
}

// GetTenantMiddleware 获取租户中间件
func (ar *AuthRoutes) GetTenantMiddleware() gin.HandlerFunc {
	return middleware.TenantMiddleware()
}

// GetSystemAdminMiddleware 获取系统管理员中间件
func (ar *AuthRoutes) GetSystemAdminMiddleware() gin.HandlerFunc {
	return middleware.RequireSystemAdmin()
}

// GetTenantUserMiddleware 获取租户用户中间件
func (ar *AuthRoutes) GetTenantUserMiddleware() gin.HandlerFunc {
	return middleware.RequireTenantUser()
}

// RegisterAuthRoutes 注册认证路由（包级别函数）
func RegisterAuthRoutes(router *gin.Engine, authController *controller.AuthController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) {
	authRoutes := NewAuthRoutes(authController, jwtManager, blacklist)
	authRoutes.RegisterAllRoutes(router)
}
