package routes

import (
	"erp/backend/internal/controller"
	"erp/backend/internal/middleware"
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// RoleRoutes 角色管理路由配置
type RoleRoutes struct {
	roleController       *controller.RoleController
	permissionController *controller.PermissionController
	jwtManager           *utils.JWTManager
	blacklist            utils.TokenBlacklist
}

// NewRoleRoutes 创建角色管理路由
func NewRoleRoutes(
	roleController *controller.RoleController,
	permissionController *controller.PermissionController,
	jwtManager *utils.JWTManager,
	blacklist utils.TokenBlacklist,
) *RoleRoutes {
	return &RoleRoutes{
		roleController:       roleController,
		permissionController: permissionController,
		jwtManager:           jwtManager,
		blacklist:            blacklist,
	}
}

// RegisterRoutes 注册角色管理路由
func (rr *RoleRoutes) RegisterRoutes(router *gin.Engine) {
	// 角色管理路由组
	roleGroup := router.Group("/api/roles")
	roleGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	{
		// 角色CRUD操作
		roleGroup.POST("", rr.roleController.CreateRole)       // 创建角色
		roleGroup.GET("", rr.roleController.GetRoleList)       // 获取角色列表
		roleGroup.GET("/:id", rr.roleController.GetRole)       // 获取角色详情
		roleGroup.PUT("/:id", rr.roleController.UpdateRole)    // 更新角色
		roleGroup.DELETE("/:id", rr.roleController.DeleteRole) // 删除角色

		// 角色权限管理
		roleGroup.PUT("/:id/permissions", rr.roleController.AssignPermissions) // 分配权限

		// 角色用户管理
		roleGroup.GET("/:id/users", rr.roleController.GetRoleUsers) // 获取角色用户列表

		// 角色权限检查
		roleGroup.POST("/check-permission", rr.roleController.CheckRolePermission) // 检查角色权限

		// 统计信息
		roleGroup.GET("/statistics", rr.roleController.GetRoleStatistics) // 获取角色统计
	}

	// 权限管理路由组
	permissionGroup := router.Group("/api/permissions")
	permissionGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	{
		// 权限CRUD操作
		permissionGroup.POST("", rr.permissionController.CreatePermission)       // 创建权限
		permissionGroup.GET("", rr.permissionController.GetPermissionList)       // 获取权限列表
		permissionGroup.GET("/:id", rr.permissionController.GetPermission)       // 获取权限详情
		permissionGroup.PUT("/:id", rr.permissionController.UpdatePermission)    // 更新权限
		permissionGroup.DELETE("/:id", rr.permissionController.DeletePermission) // 删除权限

		// 权限树和矩阵
		permissionGroup.GET("/tree", rr.permissionController.GetPermissionTree)              // 获取权限树
		permissionGroup.GET("/role-matrix", rr.permissionController.GetRolePermissionMatrix) // 获取角色权限矩阵

		// 权限查询
		permissionGroup.GET("/resource/:resource", rr.permissionController.GetPermissionsByResource) // 根据资源获取权限
		permissionGroup.GET("/action/:action", rr.permissionController.GetPermissionsByAction)       // 根据操作获取权限
		permissionGroup.GET("/search", rr.permissionController.SearchPermissions)                    // 搜索权限
	}
}

// RegisterTenantRoutes 注册租户角色权限管理路由
func (rr *RoleRoutes) RegisterTenantRoutes(router *gin.Engine) {
	// 租户角色管理路由
	tenantRoleGroup := router.Group("/api/tenant/roles")
	tenantRoleGroup.Use(middleware.TenantMiddleware())
	tenantRoleGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	tenantRoleGroup.Use(middleware.RequireTenantUser())
	{
		// 租户内角色管理（权限受限）
		tenantRoleGroup.GET("", rr.roleController.GetRoleList)                           // 获取租户角色列表
		tenantRoleGroup.GET("/:id", rr.roleController.GetRole)                           // 获取角色详情
		tenantRoleGroup.GET("/:id/users", rr.roleController.GetRoleUsers)                // 获取角色用户列表
		tenantRoleGroup.POST("/check-permission", rr.roleController.CheckRolePermission) // 检查角色权限
	}

	// 租户权限管理路由
	tenantPermissionGroup := router.Group("/api/tenant/permissions")
	tenantPermissionGroup.Use(middleware.TenantMiddleware())
	tenantPermissionGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	tenantPermissionGroup.Use(middleware.RequireTenantUser())
	{
		// 租户内权限查看（只读）
		tenantPermissionGroup.GET("", rr.permissionController.GetPermissionList)        // 获取权限列表
		tenantPermissionGroup.GET("/:id", rr.permissionController.GetPermission)        // 获取权限详情
		tenantPermissionGroup.GET("/tree", rr.permissionController.GetPermissionTree)   // 获取权限树
		tenantPermissionGroup.GET("/search", rr.permissionController.SearchPermissions) // 搜索权限
	}
}

// RegisterAdminRoutes 注册系统管理员角色权限管理路由
func (rr *RoleRoutes) RegisterAdminRoutes(router *gin.Engine) {
	// 系统管理员角色管理路由
	adminRoleGroup := router.Group("/api/admin/roles")
	adminRoleGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	adminRoleGroup.Use(middleware.RequireSystemAdmin())
	{
		// 完整的角色管理功能
		adminRoleGroup.POST("", rr.roleController.CreateRole)       // 创建角色
		adminRoleGroup.GET("", rr.roleController.GetRoleList)       // 获取角色列表
		adminRoleGroup.GET("/:id", rr.roleController.GetRole)       // 获取角色详情
		adminRoleGroup.PUT("/:id", rr.roleController.UpdateRole)    // 更新角色
		adminRoleGroup.DELETE("/:id", rr.roleController.DeleteRole) // 删除角色

		// 角色权限管理
		adminRoleGroup.PUT("/:id/permissions", rr.roleController.AssignPermissions) // 分配权限

		// 角色用户管理
		adminRoleGroup.GET("/:id/users", rr.roleController.GetRoleUsers) // 获取角色用户列表

		// 角色权限检查
		adminRoleGroup.POST("/check-permission", rr.roleController.CheckRolePermission) // 检查角色权限

		// 统计信息
		adminRoleGroup.GET("/statistics", rr.roleController.GetRoleStatistics) // 获取角色统计
	}

	// 系统管理员权限管理路由
	adminPermissionGroup := router.Group("/api/admin/permissions")
	adminPermissionGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	adminPermissionGroup.Use(middleware.RequireSystemAdmin())
	{
		// 完整的权限管理功能
		adminPermissionGroup.POST("", rr.permissionController.CreatePermission)       // 创建权限
		adminPermissionGroup.GET("", rr.permissionController.GetPermissionList)       // 获取权限列表
		adminPermissionGroup.GET("/:id", rr.permissionController.GetPermission)       // 获取权限详情
		adminPermissionGroup.PUT("/:id", rr.permissionController.UpdatePermission)    // 更新权限
		adminPermissionGroup.DELETE("/:id", rr.permissionController.DeletePermission) // 删除权限

		// 权限树和矩阵
		adminPermissionGroup.GET("/tree", rr.permissionController.GetPermissionTree)              // 获取权限树
		adminPermissionGroup.GET("/role-matrix", rr.permissionController.GetRolePermissionMatrix) // 获取角色权限矩阵

		// 权限查询
		adminPermissionGroup.GET("/resource/:resource", rr.permissionController.GetPermissionsByResource) // 根据资源获取权限
		adminPermissionGroup.GET("/action/:action", rr.permissionController.GetPermissionsByAction)       // 根据操作获取权限
		adminPermissionGroup.GET("/search", rr.permissionController.SearchPermissions)                    // 搜索权限
	}
}

// RegisterPublicRoutes 注册公开角色权限路由（如果有的话）
func (rr *RoleRoutes) RegisterPublicRoutes(router *gin.Engine) {
	// 公开角色权限路由（目前没有公开的角色权限接口）
	publicRoleGroup := router.Group("/api/public/roles")
	{
		// 可以添加一些公开的角色权限相关接口
		_ = publicRoleGroup // 暂时没有公开接口
	}
}

// RegisterAllRoutes 注册所有角色权限管理路由
func (rr *RoleRoutes) RegisterAllRoutes(router *gin.Engine) {
	// 注册基本角色权限管理路由
	rr.RegisterRoutes(router)

	// 注册租户角色权限管理路由
	rr.RegisterTenantRoutes(router)

	// 注册管理员角色权限管理路由
	rr.RegisterAdminRoutes(router)

	// 注册公开角色权限路由
	rr.RegisterPublicRoutes(router)
}

// RoleRouteConfig 角色权限路由配置
type RoleRouteConfig struct {
	EnableTenantRoutes     bool
	EnableAdminRoutes      bool
	EnablePublicRoutes     bool
	EnableRoleStatistics   bool // 是否启用角色统计功能
	EnablePermissionMatrix bool // 是否启用权限矩阵功能
}

// RegisterRoutesWithConfig 根据配置注册路由
func (rr *RoleRoutes) RegisterRoutesWithConfig(router *gin.Engine, config *RoleRouteConfig) {
	// 基本角色管理路由
	roleGroup := router.Group("/api/roles")
	roleGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	{
		// 基本CRUD操作
		roleGroup.POST("", rr.roleController.CreateRole)
		roleGroup.GET("", rr.roleController.GetRoleList)
		roleGroup.GET("/:id", rr.roleController.GetRole)
		roleGroup.PUT("/:id", rr.roleController.UpdateRole)
		roleGroup.DELETE("/:id", rr.roleController.DeleteRole)

		// 角色权限管理
		roleGroup.PUT("/:id/permissions", rr.roleController.AssignPermissions)
		roleGroup.GET("/:id/users", rr.roleController.GetRoleUsers)
		roleGroup.POST("/check-permission", rr.roleController.CheckRolePermission)

		// 根据配置启用统计功能
		if config.EnableRoleStatistics {
			roleGroup.GET("/statistics", rr.roleController.GetRoleStatistics)
		}
	}

	// 基本权限管理路由
	permissionGroup := router.Group("/api/permissions")
	permissionGroup.Use(middleware.AuthMiddleware(rr.jwtManager, rr.blacklist))
	{
		// 基本CRUD操作
		permissionGroup.POST("", rr.permissionController.CreatePermission)
		permissionGroup.GET("", rr.permissionController.GetPermissionList)
		permissionGroup.GET("/:id", rr.permissionController.GetPermission)
		permissionGroup.PUT("/:id", rr.permissionController.UpdatePermission)
		permissionGroup.DELETE("/:id", rr.permissionController.DeletePermission)

		// 权限查询
		permissionGroup.GET("/tree", rr.permissionController.GetPermissionTree)
		permissionGroup.GET("/resource/:resource", rr.permissionController.GetPermissionsByResource)
		permissionGroup.GET("/action/:action", rr.permissionController.GetPermissionsByAction)
		permissionGroup.GET("/search", rr.permissionController.SearchPermissions)

		// 根据配置启用权限矩阵功能
		if config.EnablePermissionMatrix {
			permissionGroup.GET("/role-matrix", rr.permissionController.GetRolePermissionMatrix)
		}
	}

	// 根据配置注册其他路由
	if config.EnableTenantRoutes {
		rr.RegisterTenantRoutes(router)
	}

	if config.EnableAdminRoutes {
		rr.RegisterAdminRoutes(router)
	}

	if config.EnablePublicRoutes {
		rr.RegisterPublicRoutes(router)
	}
}

// GetRoleMiddleware 获取角色相关中间件
func (rr *RoleRoutes) GetRoleMiddleware() gin.HandlerFunc {
	return middleware.AuthMiddleware(rr.jwtManager, rr.blacklist)
}

// GetTenantRoleMiddleware 获取租户角色中间件
func (rr *RoleRoutes) GetTenantRoleMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		middleware.TenantMiddleware()(c)
		if c.IsAborted() {
			return
		}
		middleware.AuthMiddleware(rr.jwtManager, rr.blacklist)(c)
		if c.IsAborted() {
			return
		}
		middleware.RequireTenantUser()(c)
	})
}

// GetSystemAdminRoleMiddleware 获取系统管理员角色中间件
func (rr *RoleRoutes) GetSystemAdminRoleMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		middleware.AuthMiddleware(rr.jwtManager, rr.blacklist)(c)
		if c.IsAborted() {
			return
		}
		middleware.RequireSystemAdmin()(c)
	})
}

// SetupRoleRouteMiddleware 设置角色路由中间件
func (rr *RoleRoutes) SetupRoleRouteMiddleware(router *gin.Engine) {
	// 角色权限管理相关的全局中间件可以在这里设置
	// 比如角色权限操作日志记录等
}

// RegisterRoleManagementRoutes 注册完整的角色权限管理路由系统
func (rr *RoleRoutes) RegisterRoleManagementRoutes(router *gin.Engine) {
	// 设置中间件
	rr.SetupRoleRouteMiddleware(router)

	// 注册所有路由
	rr.RegisterAllRoutes(router)
}

// RegisterRoleRoutes 注册角色权限路由（包级别函数）
func RegisterRoleRoutes(router *gin.Engine, roleController *controller.RoleController, permissionController *controller.PermissionController, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) {
	roleRoutes := NewRoleRoutes(roleController, permissionController, jwtManager, blacklist)
	roleRoutes.RegisterAllRoutes(router)
}
