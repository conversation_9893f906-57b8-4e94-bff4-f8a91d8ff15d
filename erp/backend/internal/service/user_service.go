package service

import (
	"errors"
	"fmt"
	"math"
	"time"

	"erp/backend/internal/dto"
	"erp/backend/internal/models"
	"erp/backend/internal/utils"

	"gorm.io/gorm"
)

// UserService 用户管理服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户管理服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *dto.CreateUserRequest) (*dto.UserResponse, error) {
	// 检查用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("邮箱已存在")
	}

	// 如果是租户用户，检查租户是否存在
	if req.UserType == models.UserTypeTenant {
		if req.TenantID == nil {
			return nil, errors.New("租户用户必须指定租户ID")
		}

		var tenant models.Tenant
		if err := s.db.First(&tenant, *req.TenantID).Error; err != nil {
			return nil, errors.New("指定的租户不存在")
		}

		if tenant.Status != models.TenantStatusActive {
			return nil, errors.New("指定的租户未激活")
		}
	}

	// 哈希密码
	passwordHash, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}

	// 创建用户
	user := &models.User{
		Username:     req.Username,
		Email:        req.Email,
		Phone:        req.Phone,
		PasswordHash: passwordHash,
		RealName:     req.RealName,
		AvatarURL:    req.AvatarURL,
		UserType:     req.UserType,
		Status:       models.UserStatusActive,
		TenantID:     req.TenantID,
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户记录
	if err := tx.Create(user).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		for _, roleID := range req.RoleIDs {
			userRole := &models.UserRole{
				UserID: user.ID,
				RoleID: roleID,
			}
			if err := tx.Create(userRole).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("分配角色失败: %w", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回用户信息
	return s.GetUserByID(user.ID)
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(userID uint64) (*dto.UserResponse, error) {
	var user models.User
	if err := s.db.Preload("Tenant").First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 获取用户角色
	roles, err := s.getUserRoles(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	return s.buildUserResponse(&user, roles), nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(userID uint64, req *dto.UpdateUserRequest) (*dto.UserResponse, error) {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var existingUser models.User
		if err := s.db.Where("email = ? AND id != ?", req.Email, userID).First(&existingUser).Error; err == nil {
			return nil, errors.New("邮箱已被其他用户使用")
		}
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新用户信息
	updates := make(map[string]interface{})
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.RealName != "" {
		updates["real_name"] = req.RealName
	}
	if req.AvatarURL != "" {
		updates["avatar_url"] = req.AvatarURL
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	updates["updated_at"] = time.Now()

	if len(updates) > 0 {
		if err := tx.Model(&user).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新用户失败: %w", err)
		}
	}

	// 更新角色分配
	if req.RoleIDs != nil {
		// 删除现有角色分配
		if err := tx.Where("user_id = ?", userID).Delete(&models.UserRole{}).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("删除现有角色失败: %w", err)
		}

		// 添加新角色分配
		for _, roleID := range req.RoleIDs {
			userRole := &models.UserRole{
				UserID: userID,
				RoleID: roleID,
			}
			if err := tx.Create(userRole).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("分配角色失败: %w", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回更新后的用户信息
	return s.GetUserByID(userID)
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(userID uint64, force bool) error {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if force {
		// 物理删除：删除相关数据
		tx.Where("user_id = ?", userID).Delete(&models.UserRole{})
		tx.Where("user_id = ?", userID).Delete(&models.UserSession{})
		tx.Where("username = ?", user.Username).Delete(&models.LoginAttempt{})
		tx.Delete(&user)
	} else {
		// 软删除
		tx.Delete(&user)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// GetUserList 获取用户列表
func (s *UserService) GetUserList(req *dto.UserListRequest) (*dto.UserListResponse, error) {
	query := s.db.Model(&models.User{}).Preload("Tenant")

	// 应用过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("username LIKE ? OR email LIKE ? OR real_name LIKE ?", keyword, keyword, keyword)
	}

	if req.UserType != nil {
		query = query.Where("user_type = ?", *req.UserType)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.TenantID != nil {
		query = query.Where("tenant_id = ?", *req.TenantID)
	}

	if req.RoleID != nil {
		query = query.Joins("JOIN user_roles ur ON users.id = ur.user_id").
			Where("ur.role_id = ? AND ur.deleted_at IS NULL", *req.RoleID)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计用户数量失败: %w", err)
	}

	// 应用排序
	orderClause := req.SortBy
	if req.SortDesc {
		orderClause += " DESC"
	}
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询用户列表
	var users []models.User
	if err := query.Find(&users).Error; err != nil {
		return nil, fmt.Errorf("查询用户列表失败: %w", err)
	}

	// 构建响应
	userResponses := make([]dto.UserResponse, 0, len(users))
	for _, user := range users {
		roles, _ := s.getUserRoles(user.ID)
		userResponses = append(userResponses, *s.buildUserResponse(&user, roles))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.UserListResponse{
		Users:      userResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ResetUserPassword 重置用户密码（管理员操作）
func (s *UserService) ResetUserPassword(userID uint64, newPassword string) error {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 哈希新密码
	passwordHash, err := utils.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 更新密码
	if err := s.db.Model(&user).Updates(map[string]interface{}{
		"password_hash": passwordHash,
		"updated_at":    time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	// TODO: 如果需要发送邮件通知，在这里实现

	return nil
}

// UpdateUserStatus 更新用户状态
func (s *UserService) UpdateUserStatus(userID uint64, req *dto.UpdateUserStatusRequest) error {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 更新状态
	if err := s.db.Model(&user).Updates(map[string]interface{}{
		"status":     req.Status,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新用户状态失败: %w", err)
	}

	return nil
}

// getUserRoles 获取用户角色
func (s *UserService) getUserRoles(userID uint64) ([]dto.RoleInfo, error) {
	var roles []dto.RoleInfo

	err := s.db.Table("user_roles ur").
		Select("r.id, r.name, r.code, r.description, ur.expires_at").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND ur.deleted_at IS NULL AND r.deleted_at IS NULL", userID).
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now()).
		Scan(&roles).Error

	return roles, err
}

// buildUserResponse 构建用户响应
func (s *UserService) buildUserResponse(user *models.User, roles []dto.RoleInfo) *dto.UserResponse {
	response := &dto.UserResponse{
		ID:              user.ID,
		Username:        user.Username,
		Email:           user.Email,
		Phone:           user.Phone,
		RealName:        user.RealName,
		AvatarURL:       user.AvatarURL,
		UserType:        user.UserType,
		UserTypeText:    getUserTypeText(user.UserType),
		Status:          user.Status,
		StatusText:      getUserStatusText(user.Status),
		TenantID:        user.TenantID,
		Roles:           roles,
		LastLoginAt:     user.LastLoginAt,
		LastLoginIP:     user.LastLoginIP,
		EmailVerifiedAt: user.EmailVerifiedAt,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
	}

	if user.Tenant != nil {
		response.TenantName = user.Tenant.Name
	}

	return response
}

// getUserTypeText 获取用户类型文本
func getUserTypeText(userType int8) string {
	switch userType {
	case models.UserTypeTenant:
		return "租户用户"
	case models.UserTypeSystemAdmin:
		return "系统管理员"
	default:
		return "未知"
	}
}

// getUserStatusText 获取用户状态文本
func getUserStatusText(status int8) string {
	switch status {
	case models.UserStatusActive:
		return "正常"
	case models.UserStatusSuspended:
		return "暂停"
	case models.UserStatusDisabled:
		return "禁用"
	default:
		return "未知"
	}
}

// AssignRoles 分配角色给用户
func (s *UserService) AssignRoles(userID uint64, req *dto.AssignRolesRequest) error {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除现有角色分配
	if err := tx.Where("user_id = ?", userID).Delete(&models.UserRole{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除现有角色失败: %w", err)
	}

	// 添加新角色分配
	for _, roleID := range req.RoleIDs {
		userRole := &models.UserRole{
			UserID: userID,
			RoleID: roleID,
		}
		if err := tx.Create(userRole).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("分配角色失败: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetUserStatistics 获取用户统计信息
func (s *UserService) GetUserStatistics() (*dto.UserStatistics, error) {
	stats := &dto.UserStatistics{}

	// 总用户数
	s.db.Model(&models.User{}).Count(&stats.TotalUsers)

	// 按状态统计
	s.db.Model(&models.User{}).Where("status = ?", models.UserStatusActive).Count(&stats.ActiveUsers)
	s.db.Model(&models.User{}).Where("status = ?", models.UserStatusSuspended).Count(&stats.SuspendedUsers)
	s.db.Model(&models.User{}).Where("status = ?", models.UserStatusDisabled).Count(&stats.DisabledUsers)

	// 按类型统计
	s.db.Model(&models.User{}).Where("user_type = ?", models.UserTypeTenant).Count(&stats.TenantUsers)
	s.db.Model(&models.User{}).Where("user_type = ?", models.UserTypeSystemAdmin).Count(&stats.SystemAdmins)

	// 最近7天登录用户数
	weekAgo := time.Now().AddDate(0, 0, -7)
	s.db.Model(&models.User{}).Where("last_login_at > ?", weekAgo).Count(&stats.RecentLogins)

	// 本周新用户数
	s.db.Model(&models.User{}).Where("created_at > ?", weekAgo).Count(&stats.NewUsersThisWeek)

	return stats, nil
}

// BatchUpdateUsers 批量更新用户
func (s *UserService) BatchUpdateUsers(req *dto.BatchUpdateUsersRequest) error {
	// 验证用户ID
	var count int64
	s.db.Model(&models.User{}).Where("id IN ?", req.UserIDs).Count(&count)
	if count != int64(len(req.UserIDs)) {
		return errors.New("部分用户不存在")
	}

	// 批量更新
	req.Updates["updated_at"] = time.Now()
	if err := s.db.Model(&models.User{}).Where("id IN ?", req.UserIDs).Updates(req.Updates).Error; err != nil {
		return fmt.Errorf("批量更新用户失败: %w", err)
	}

	return nil
}

// BatchDeleteUsers 批量删除用户
func (s *UserService) BatchDeleteUsers(req *dto.BatchDeleteUsersRequest) error {
	// 验证用户ID
	var count int64
	s.db.Model(&models.User{}).Where("id IN ?", req.UserIDs).Count(&count)
	if count != int64(len(req.UserIDs)) {
		return errors.New("部分用户不存在")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if req.Force {
		// 物理删除：删除相关数据
		tx.Where("user_id IN ?", req.UserIDs).Delete(&models.UserRole{})
		tx.Where("user_id IN ?", req.UserIDs).Delete(&models.UserSession{})
		tx.Where("user_id IN ?", req.UserIDs).Delete(&models.LoginAttempt{})
		tx.Where("id IN ?", req.UserIDs).Unscoped().Delete(&models.User{})
	} else {
		// 软删除
		tx.Where("id IN ?", req.UserIDs).Delete(&models.User{})
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("批量删除用户失败: %w", err)
	}

	return nil
}
