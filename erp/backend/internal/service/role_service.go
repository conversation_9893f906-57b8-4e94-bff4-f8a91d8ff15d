package service

import (
	"errors"
	"fmt"
	"math"
	"time"

	"erp/backend/internal/dto"
	"erp/backend/internal/models"

	"gorm.io/gorm"
)

// RoleService 角色管理服务
type RoleService struct {
	db *gorm.DB
}

// NewRoleService 创建角色管理服务
func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{
		db: db,
	}
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(req *dto.CreateRoleRequest) (*dto.RoleResponse, error) {
	// 检查角色代码是否已存在
	var existingRole models.Role
	query := s.db.Where("code = ?", req.Code)
	if req.TenantID != nil {
		query = query.Where("tenant_id = ?", *req.TenantID)
	} else {
		query = query.Where("tenant_id IS NULL")
	}

	if err := query.First(&existingRole).Error; err == nil {
		return nil, errors.New("角色代码已存在")
	}

	// 如果是租户角色，检查租户是否存在
	if req.TenantID != nil {
		var tenant models.Tenant
		if err := s.db.First(&tenant, *req.TenantID).Error; err != nil {
			return nil, errors.New("指定的租户不存在")
		}

		if tenant.Status != models.TenantStatusActive {
			return nil, errors.New("指定的租户未激活")
		}
	}

	// 创建角色
	role := &models.Role{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		TenantID:    req.TenantID,
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建角色记录
	if err := tx.Create(role).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建角色失败: %w", err)
	}

	// 分配权限
	if len(req.PermissionIDs) > 0 {
		for _, permissionID := range req.PermissionIDs {
			rolePermission := &models.RolePermission{
				RoleID:       role.ID,
				PermissionID: permissionID,
			}
			if err := tx.Create(rolePermission).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("分配权限失败: %w", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回角色信息
	return s.GetRoleByID(role.ID)
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(roleID uint64) (*dto.RoleResponse, error) {
	var role models.Role
	if err := s.db.Preload("Tenant").First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 获取角色权限
	permissions, err := s.getRolePermissions(roleID)
	if err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	// 获取用户数量
	var userCount int64
	s.db.Model(&models.UserRole{}).Where("role_id = ?", roleID).Count(&userCount)

	return s.buildRoleResponse(&role, permissions, userCount), nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(roleID uint64, req *dto.UpdateRoleRequest) (*dto.RoleResponse, error) {
	// 查找角色
	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新角色信息
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	updates["updated_at"] = time.Now()

	if len(updates) > 0 {
		if err := tx.Model(&role).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新角色失败: %w", err)
		}
	}

	// 更新权限分配
	if req.PermissionIDs != nil {
		// 删除现有权限分配
		if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("删除现有权限失败: %w", err)
		}

		// 添加新权限分配
		for _, permissionID := range req.PermissionIDs {
			rolePermission := &models.RolePermission{
				RoleID:       roleID,
				PermissionID: permissionID,
			}
			if err := tx.Create(rolePermission).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("分配权限失败: %w", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回更新后的角色信息
	return s.GetRoleByID(roleID)
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(roleID uint64) error {
	// 查找角色
	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("查询角色失败: %w", err)
	}

	// 检查是否有用户使用此角色
	var userCount int64
	s.db.Model(&models.UserRole{}).Where("role_id = ?", roleID).Count(&userCount)
	if userCount > 0 {
		return errors.New("角色正在被用户使用，无法删除")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除角色权限关联
	if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除角色权限关联失败: %w", err)
	}

	// 删除角色
	if err := tx.Delete(&role).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除角色失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetRoleList 获取角色列表
func (s *RoleService) GetRoleList(req *dto.RoleListRequest) (*dto.RoleListResponse, error) {
	query := s.db.Model(&models.Role{}).Preload("Tenant")

	// 应用过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR code LIKE ?", keyword, keyword)
	}

	if req.TenantID != nil {
		query = query.Where("tenant_id = ?", *req.TenantID)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计角色数量失败: %w", err)
	}

	// 应用排序
	orderClause := req.SortBy
	if req.SortDesc {
		orderClause += " DESC"
	}
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询角色列表
	var roles []models.Role
	if err := query.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("查询角色列表失败: %w", err)
	}

	// 构建响应
	roleResponses := make([]dto.RoleResponse, 0, len(roles))
	for _, role := range roles {
		permissions, _ := s.getRolePermissions(role.ID)

		// 获取用户数量
		var userCount int64
		s.db.Model(&models.UserRole{}).Where("role_id = ?", role.ID).Count(&userCount)

		roleResponses = append(roleResponses, *s.buildRoleResponse(&role, permissions, userCount))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.RoleListResponse{
		Roles:      roleResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// AssignPermissions 分配权限给角色
func (s *RoleService) AssignPermissions(roleID uint64, req *dto.AssignPermissionsRequest) error {
	// 查找角色
	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("查询角色失败: %w", err)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除现有权限分配
	if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除现有权限失败: %w", err)
	}

	// 添加新权限分配
	for _, permissionID := range req.PermissionIDs {
		rolePermission := &models.RolePermission{
			RoleID:       roleID,
			PermissionID: permissionID,
		}
		if err := tx.Create(rolePermission).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("分配权限失败: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// getRolePermissions 获取角色权限
func (s *RoleService) getRolePermissions(roleID uint64) ([]dto.PermissionInfo, error) {
	var permissions []dto.PermissionInfo

	err := s.db.Table("role_permissions rp").
		Select("p.id, p.name, p.code, p.description, p.resource, p.action").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id = ? AND rp.deleted_at IS NULL AND p.deleted_at IS NULL", roleID).
		Scan(&permissions).Error

	return permissions, err
}

// buildRoleResponse 构建角色响应
func (s *RoleService) buildRoleResponse(role *models.Role, permissions []dto.PermissionInfo, userCount int64) *dto.RoleResponse {
	response := &dto.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		Code:        role.Code,
		Description: role.Description,
		TenantID:    role.TenantID,
		Permissions: permissions,
		UserCount:   userCount,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	if role.Tenant != nil {
		response.TenantName = role.Tenant.Name
	}

	return response
}

// GetRoleStatistics 获取角色统计信息
func (s *RoleService) GetRoleStatistics() (*dto.RoleStatistics, error) {
	stats := &dto.RoleStatistics{}

	// 总角色数
	s.db.Model(&models.Role{}).Count(&stats.TotalRoles)

	// 系统角色和租户角色
	s.db.Model(&models.Role{}).Where("tenant_id IS NULL").Count(&stats.SystemRoles)
	s.db.Model(&models.Role{}).Where("tenant_id IS NOT NULL").Count(&stats.TenantRoles)

	// 总权限数
	s.db.Model(&models.Permission{}).Count(&stats.TotalPermissions)

	// 有用户的角色数
	s.db.Table("roles r").
		Joins("JOIN user_roles ur ON r.id = ur.role_id").
		Where("ur.deleted_at IS NULL").
		Group("r.id").
		Count(&stats.ActiveRoles)

	// 没有用户的角色数
	stats.UnusedRoles = stats.TotalRoles - stats.ActiveRoles

	return stats, nil
}

// GetRoleUsers 获取角色的用户列表
func (s *RoleService) GetRoleUsers(roleID uint64, req *dto.GetRoleUsersRequest) (*dto.GetRoleUsersResponse, error) {
	// 检查角色是否存在
	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 统计总数
	var total int64
	s.db.Table("user_roles ur").
		Joins("JOIN users u ON ur.user_id = u.id").
		Where("ur.role_id = ? AND ur.deleted_at IS NULL AND u.deleted_at IS NULL", roleID).
		Count(&total)

	// 查询用户列表
	var users []dto.UserRoleInfo
	offset := (req.Page - 1) * req.PageSize

	err := s.db.Table("user_roles ur").
		Select("u.id as user_id, u.username, u.real_name, r.id as role_id, r.name as role_name, r.code as role_code, ur.expires_at, ur.created_at").
		Joins("JOIN users u ON ur.user_id = u.id").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.role_id = ? AND ur.deleted_at IS NULL AND u.deleted_at IS NULL", roleID).
		Order("ur.created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&users).Error

	if err != nil {
		return nil, fmt.Errorf("查询角色用户失败: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.GetRoleUsersResponse{
		Users:      users,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CheckRolePermission 检查角色是否有指定权限
func (s *RoleService) CheckRolePermission(roleID uint64, permissionCode string) (*dto.CheckRolePermissionResponse, error) {
	var count int64
	err := s.db.Table("role_permissions rp").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id = ? AND p.code = ? AND rp.deleted_at IS NULL AND p.deleted_at IS NULL", roleID, permissionCode).
		Count(&count).Error

	if err != nil {
		return nil, fmt.Errorf("检查角色权限失败: %w", err)
	}

	response := &dto.CheckRolePermissionResponse{
		HasPermission: count > 0,
	}

	if !response.HasPermission {
		response.Reason = "角色没有此权限"
	}

	return response, nil
}
