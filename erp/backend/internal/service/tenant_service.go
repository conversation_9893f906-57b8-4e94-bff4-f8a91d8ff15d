package service

import (
	"errors"
	"fmt"
	"math"
	"time"

	"erp/backend/internal/dto"
	"erp/backend/internal/models"

	"gorm.io/gorm"
)

// TenantService 租户管理服务
type TenantService struct {
	db *gorm.DB
}

// NewTenantService 创建租户管理服务
func NewTenantService(db *gorm.DB) *TenantService {
	return &TenantService{
		db: db,
	}
}

// CreateTenant 创建租户
func (s *TenantService) CreateTenant(req *dto.CreateTenantRequest) (*dto.TenantResponse, error) {
	// 检查租户代码是否已存在
	var existingTenant models.Tenant
	if err := s.db.Where("code = ?", req.Code).First(&existingTenant).Error; err == nil {
		return nil, errors.New("租户代码已存在")
	}

	// 创建租户
	tenant := &models.Tenant{
		Name:         req.Name,
		Code:         req.Code,
		ContactPhone: req.ContactPhone,
		ContactEmail: req.ContactEmail,
		Address:      req.Address,
		Status:       models.TenantStatusActive, // 默认激活状态
		MaxUsers:     req.MaxUsers,
		ExpiredAt:    req.ExpiresAt,
	}

	// 设置默认值
	if tenant.MaxUsers == 0 {
		tenant.MaxUsers = 100 // 默认最大用户数
	}

	if err := s.db.Create(tenant).Error; err != nil {
		return nil, fmt.Errorf("创建租户失败: %w", err)
	}

	return s.GetTenantByID(tenant.ID)
}

// GetTenantByID 根据ID获取租户
func (s *TenantService) GetTenantByID(tenantID uint64) (*dto.TenantResponse, error) {
	var tenant models.Tenant
	if err := s.db.First(&tenant, tenantID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("租户不存在")
		}
		return nil, fmt.Errorf("查询租户失败: %w", err)
	}

	// 获取当前用户数量
	var currentUsers int64
	s.db.Model(&models.User{}).Where("tenant_id = ?", tenantID).Count(&currentUsers)

	return s.buildTenantResponse(&tenant, currentUsers), nil
}

// UpdateTenant 更新租户
func (s *TenantService) UpdateTenant(tenantID uint64, req *dto.UpdateTenantRequest) (*dto.TenantResponse, error) {
	// 查找租户
	var tenant models.Tenant
	if err := s.db.First(&tenant, tenantID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("租户不存在")
		}
		return nil, fmt.Errorf("查询租户失败: %w", err)
	}

	// 更新租户信息
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.ContactPhone != "" {
		updates["contact_phone"] = req.ContactPhone
	}
	if req.ContactEmail != "" {
		updates["contact_email"] = req.ContactEmail
	}
	if req.Address != "" {
		updates["address"] = req.Address
	}
	if req.MaxUsers != nil {
		updates["max_users"] = *req.MaxUsers
	}
	if req.ExpiresAt != nil {
		updates["expired_at"] = *req.ExpiresAt
	}
	updates["updated_at"] = time.Now()

	if len(updates) > 0 {
		if err := s.db.Model(&tenant).Updates(updates).Error; err != nil {
			return nil, fmt.Errorf("更新租户失败: %w", err)
		}
	}

	// 返回更新后的租户信息
	return s.GetTenantByID(tenantID)
}

// DeleteTenant 删除租户
func (s *TenantService) DeleteTenant(tenantID uint64) error {
	// 查找租户
	var tenant models.Tenant
	if err := s.db.First(&tenant, tenantID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("租户不存在")
		}
		return fmt.Errorf("查询租户失败: %w", err)
	}

	// 检查是否有用户
	var userCount int64
	s.db.Model(&models.User{}).Where("tenant_id = ?", tenantID).Count(&userCount)
	if userCount > 0 {
		return errors.New("租户下还有用户，无法删除")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除租户相关的角色
	if err := tx.Where("tenant_id = ?", tenantID).Delete(&models.Role{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除租户角色失败: %w", err)
	}

	// 删除租户
	if err := tx.Delete(&tenant).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除租户失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetTenantList 获取租户列表
func (s *TenantService) GetTenantList(req *dto.TenantListRequest) (*dto.TenantListResponse, error) {
	query := s.db.Model(&models.Tenant{})

	// 应用过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR code LIKE ?", keyword, keyword)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计租户数量失败: %w", err)
	}

	// 应用排序
	orderClause := req.SortBy
	if req.SortDesc {
		orderClause += " DESC"
	}
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询租户列表
	var tenants []models.Tenant
	if err := query.Find(&tenants).Error; err != nil {
		return nil, fmt.Errorf("查询租户列表失败: %w", err)
	}

	// 构建响应
	tenantResponses := make([]dto.TenantResponse, 0, len(tenants))
	for _, tenant := range tenants {
		// 获取当前用户数量
		var currentUsers int64
		s.db.Model(&models.User{}).Where("tenant_id = ?", tenant.ID).Count(&currentUsers)

		tenantResponses = append(tenantResponses, *s.buildTenantResponse(&tenant, currentUsers))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.TenantListResponse{
		Tenants:    tenantResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// UpdateTenantStatus 更新租户状态
func (s *TenantService) UpdateTenantStatus(tenantID uint64, req *dto.UpdateTenantStatusRequest) error {
	// 查找租户
	var tenant models.Tenant
	if err := s.db.First(&tenant, tenantID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("租户不存在")
		}
		return fmt.Errorf("查询租户失败: %w", err)
	}

	// 更新状态
	updates := map[string]interface{}{
		"status":     req.Status,
		"updated_at": time.Now(),
	}

	if err := s.db.Model(&tenant).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新租户状态失败: %w", err)
	}

	return nil
}

// GetTenantStatistics 获取租户统计信息
func (s *TenantService) GetTenantStatistics() (*dto.TenantStatistics, error) {
	stats := &dto.TenantStatistics{}

	// 总租户数
	s.db.Model(&models.Tenant{}).Count(&stats.TotalTenants)

	// 各状态租户数
	s.db.Model(&models.Tenant{}).Where("status = ?", models.TenantStatusActive).Count(&stats.ActiveTenants)
	s.db.Model(&models.Tenant{}).Where("status = ?", models.TenantStatusDisabled).Count(&stats.DisabledTenants)
	s.db.Model(&models.Tenant{}).Where("status = ?", models.TenantStatusSuspended).Count(&stats.ExpiredTenants)

	// 总用户数
	s.db.Model(&models.User{}).Where("tenant_id IS NOT NULL").Count(&stats.TotalUsers)

	// 平均用户数
	if stats.TotalTenants > 0 {
		stats.AverageUsers = float64(stats.TotalUsers) / float64(stats.TotalTenants)
	}

	// 最大用户限制总和
	var maxUsersSum int64
	s.db.Model(&models.Tenant{}).Select("COALESCE(SUM(max_users), 0)").Scan(&maxUsersSum)
	stats.MaxUsersLimit = maxUsersSum

	return stats, nil
}

// GetTenantUsers 获取租户用户列表
func (s *TenantService) GetTenantUsers(tenantID uint64, req *dto.GetTenantUsersRequest) (*dto.GetTenantUsersResponse, error) {
	// 检查租户是否存在
	var tenant models.Tenant
	if err := s.db.First(&tenant, tenantID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("租户不存在")
		}
		return nil, fmt.Errorf("查询租户失败: %w", err)
	}

	query := s.db.Model(&models.User{}).Where("tenant_id = ?", tenantID)

	// 应用过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?", keyword, keyword, keyword)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.UserType != nil {
		query = query.Where("user_type = ?", *req.UserType)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计用户数量失败: %w", err)
	}

	// 应用排序
	orderClause := req.SortBy
	if req.SortDesc {
		orderClause += " DESC"
	}
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询用户列表
	var users []models.User
	if err := query.Find(&users).Error; err != nil {
		return nil, fmt.Errorf("查询用户列表失败: %w", err)
	}

	// 构建响应
	userInfos := make([]dto.TenantUserInfo, 0, len(users))
	for _, user := range users {
		userInfos = append(userInfos, dto.TenantUserInfo{
			UserID:       user.ID,
			Username:     user.Username,
			RealName:     user.RealName,
			Email:        user.Email,
			Phone:        user.Phone,
			Status:       int(user.Status),
			StatusText:   s.getUserStatusText(int(user.Status)),
			UserType:     int(user.UserType),
			UserTypeText: s.getUserTypeText(int(user.UserType)),
			CreatedAt:    user.CreatedAt,
			LastLoginAt:  user.LastLoginAt,
		})
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.GetTenantUsersResponse{
		Users:      userInfos,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// buildTenantResponse 构建租户响应
func (s *TenantService) buildTenantResponse(tenant *models.Tenant, currentUsers int64) *dto.TenantResponse {
	return &dto.TenantResponse{
		ID:           tenant.ID,
		Name:         tenant.Name,
		Code:         tenant.Code,
		Description:  "", // 租户模型中没有Description字段
		ContactName:  "", // 租户模型中没有ContactName字段
		ContactPhone: tenant.ContactPhone,
		ContactEmail: tenant.ContactEmail,
		Address:      tenant.Address,
		Status:       int(tenant.Status),
		StatusText:   s.getTenantStatusText(int(tenant.Status)),
		MaxUsers:     tenant.MaxUsers,
		CurrentUsers: currentUsers,
		ExpiresAt:    tenant.ExpiredAt,
		CreatedAt:    tenant.CreatedAt,
		UpdatedAt:    tenant.UpdatedAt,
	}
}

// getTenantStatusText 获取租户状态文本
func (s *TenantService) getTenantStatusText(status int) string {
	switch status {
	case models.TenantStatusDisabled:
		return "禁用"
	case models.TenantStatusActive:
		return "正常"
	case models.TenantStatusSuspended:
		return "暂停"
	default:
		return "未知"
	}
}

// getUserStatusText 获取用户状态文本
func (s *TenantService) getUserStatusText(status int) string {
	switch status {
	case models.UserStatusDisabled:
		return "禁用"
	case models.UserStatusActive:
		return "正常"
	case models.UserStatusSuspended:
		return "暂停"
	default:
		return "未知"
	}
}

// getUserTypeText 获取用户类型文本
func (s *TenantService) getUserTypeText(userType int) string {
	switch userType {
	case models.UserTypeSystemAdmin:
		return "系统管理员"
	case models.UserTypeTenant:
		return "租户用户"
	default:
		return "未知"
	}
}

// GetTenantResourceUsage 获取租户资源使用情况
func (s *TenantService) GetTenantResourceUsage() (*dto.GetTenantResourceUsageResponse, error) {
	var tenants []models.Tenant
	if err := s.db.Find(&tenants).Error; err != nil {
		return nil, fmt.Errorf("查询租户列表失败: %w", err)
	}

	usage := make([]dto.TenantResourceUsage, 0, len(tenants))
	for _, tenant := range tenants {
		// 获取用户数量
		var userCount int64
		s.db.Model(&models.User{}).Where("tenant_id = ?", tenant.ID).Count(&userCount)

		// 获取角色数量
		var roleCount int64
		s.db.Model(&models.Role{}).Where("tenant_id = ?", tenant.ID).Count(&roleCount)

		// 计算用户使用率
		var userUsage float64
		if tenant.MaxUsers > 0 {
			userUsage = float64(userCount) / float64(tenant.MaxUsers) * 100
		}

		usage = append(usage, dto.TenantResourceUsage{
			TenantID:     tenant.ID,
			TenantName:   tenant.Name,
			UserCount:    userCount,
			MaxUsers:     tenant.MaxUsers,
			UserUsage:    userUsage,
			RoleCount:    roleCount,
			StorageUsed:  0, // TODO: 实现存储使用量统计
			StorageLimit: 0, // TODO: 实现存储限制
			StorageUsage: 0, // TODO: 实现存储使用率
		})
	}

	return &dto.GetTenantResourceUsageResponse{
		Usage: usage,
	}, nil
}

// BatchUpdateTenants 批量更新租户
func (s *TenantService) BatchUpdateTenants(req *dto.BatchUpdateTenantsRequest) (*dto.BatchUpdateTenantsResponse, error) {
	response := &dto.BatchUpdateTenantsResponse{
		Failures: make([]dto.BatchFailureDetail, 0),
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, tenantID := range req.TenantIDs {
		// 查找租户
		var tenant models.Tenant
		if err := tx.First(&tenant, tenantID).Error; err != nil {
			response.FailureCount++
			response.Failures = append(response.Failures, dto.BatchFailureDetail{
				ID:     tenantID,
				Reason: "租户不存在",
			})
			continue
		}

		// 构建更新数据
		updates := make(map[string]interface{})
		if req.Updates.Status != nil {
			updates["status"] = *req.Updates.Status
		}
		if req.Updates.MaxUsers != nil {
			updates["max_users"] = *req.Updates.MaxUsers
		}
		if req.Updates.ExpiresAt != nil {
			updates["expires_at"] = *req.Updates.ExpiresAt
		}
		updates["updated_at"] = time.Now()

		// 执行更新
		if err := tx.Model(&tenant).Updates(updates).Error; err != nil {
			response.FailureCount++
			response.Failures = append(response.Failures, dto.BatchFailureDetail{
				ID:     tenantID,
				Reason: fmt.Sprintf("更新失败: %v", err),
			})
			continue
		}

		response.SuccessCount++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return response, nil
}

// ImportTenants 导入租户
func (s *TenantService) ImportTenants(req *dto.ImportTenantsRequest) (*dto.ImportTenantsResponse, error) {
	response := &dto.ImportTenantsResponse{
		Failures: make([]dto.ImportFailureDetail, 0),
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for i, tenantReq := range req.Tenants {
		// 检查租户代码是否已存在
		var existingTenant models.Tenant
		if err := tx.Where("code = ?", tenantReq.Code).First(&existingTenant).Error; err == nil {
			response.FailureCount++
			response.Failures = append(response.Failures, dto.ImportFailureDetail{
				Index:  i + 1,
				Reason: "租户代码已存在",
				Data:   tenantReq,
			})
			continue
		}

		// 创建租户
		tenant := &models.Tenant{
			Name:         tenantReq.Name,
			Code:         tenantReq.Code,
			ContactPhone: tenantReq.ContactPhone,
			ContactEmail: tenantReq.ContactEmail,
			Address:      tenantReq.Address,
			Status:       models.TenantStatusActive,
			MaxUsers:     tenantReq.MaxUsers,
			ExpiredAt:    tenantReq.ExpiresAt,
		}

		// 设置默认值
		if tenant.MaxUsers == 0 {
			tenant.MaxUsers = 100
		}

		if err := tx.Create(tenant).Error; err != nil {
			response.FailureCount++
			response.Failures = append(response.Failures, dto.ImportFailureDetail{
				Index:  i + 1,
				Reason: fmt.Sprintf("创建失败: %v", err),
				Data:   tenantReq,
			})
			continue
		}

		response.SuccessCount++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return response, nil
}
