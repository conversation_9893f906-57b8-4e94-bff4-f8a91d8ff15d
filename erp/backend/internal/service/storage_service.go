package service

import (
	"fmt"
	"log"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"erp/backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// StorageService 文件存储服务
type StorageService struct {
	supabaseManager *config.SupabaseManager
	enabled         bool
}

// NewStorageService 创建存储服务实例
func NewStorageService(cfg *config.Config) *StorageService {
	service := &StorageService{
		enabled: cfg.Supabase.Enabled,
	}

	if cfg.Supabase.Enabled {
		supabaseManager, err := config.NewSupabaseManager(&cfg.Supabase)
		if err != nil {
			log.Printf("警告: Supabase存储服务初始化失败: %v", err)
			service.enabled = false
		} else {
			service.supabaseManager = supabaseManager
			log.Println("✅ Supabase存储服务初始化成功")
		}
	}

	return service
}

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	File     *multipart.FileHeader `form:"file" binding:"required"`
	Bucket   string                `form:"bucket"`
	Folder   string                `form:"folder"`
	TenantID uint                  `form:"tenant_id"`
}

// FileUploadResponse 文件上传响应
type FileUploadResponse struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Size     int64     `json:"size"`
	MimeType string    `json:"mime_type"`
	URL      string    `json:"url"`
	Bucket   string    `json:"bucket"`
	Path     string    `json:"path"`
	TenantID uint      `json:"tenant_id"`
	UploadAt time.Time `json:"upload_at"`
}

// UploadFile 上传文件到Supabase Storage
func (s *StorageService) UploadFile(c *gin.Context, req *FileUploadRequest) (*FileUploadResponse, error) {
	if !s.enabled {
		return nil, fmt.Errorf("文件存储服务未启用")
	}

	// 生成唯一文件名
	fileID := uuid.New().String()
	fileExt := filepath.Ext(req.File.Filename)
	fileName := fmt.Sprintf("%s%s", fileID, fileExt)

	// 构建存储路径
	var storagePath string
	if req.Folder != "" {
		storagePath = fmt.Sprintf("%s/%d/%s", req.Folder, req.TenantID, fileName)
	} else {
		storagePath = fmt.Sprintf("%d/%s", req.TenantID, fileName)
	}

	// 设置默认存储桶
	bucket := req.Bucket
	if bucket == "" {
		bucket = "files" // 默认存储桶
	}

	// TODO: 实现实际的Supabase存储上传
	// 这里需要根据具体的Supabase Go客户端版本实现
	log.Printf("模拟上传文件到Supabase: %s/%s", bucket, storagePath)

	// 构建模拟的公共URL
	publicURL := fmt.Sprintf("https://example.supabase.co/storage/v1/object/public/%s/%s", bucket, storagePath)

	// 构建响应
	response := &FileUploadResponse{
		ID:       fileID,
		Name:     req.File.Filename,
		Size:     req.File.Size,
		MimeType: req.File.Header.Get("Content-Type"),
		URL:      publicURL,
		Bucket:   bucket,
		Path:     storagePath,
		TenantID: req.TenantID,
		UploadAt: time.Now(),
	}

	log.Printf("文件上传成功: %s -> %s", req.File.Filename, storagePath)
	return response, nil
}

// DeleteFile 删除文件
func (s *StorageService) DeleteFile(bucket, path string) error {
	if !s.enabled {
		return fmt.Errorf("文件存储服务未启用")
	}

	// TODO: 实现实际的Supabase存储删除
	log.Printf("模拟删除文件: %s/%s", bucket, path)
	return nil
}

// GetFileURL 获取文件的签名URL
func (s *StorageService) GetFileURL(bucket, path string, expiresIn int) (string, error) {
	if !s.enabled {
		return "", fmt.Errorf("文件存储服务未启用")
	}

	// TODO: 实现实际的Supabase URL生成
	// 构建模拟URL
	if expiresIn == 0 {
		return fmt.Sprintf("https://example.supabase.co/storage/v1/object/public/%s/%s", bucket, path), nil
	}

	return fmt.Sprintf("https://example.supabase.co/storage/v1/object/sign/%s/%s?expires=%d", bucket, path, expiresIn), nil
}

// ListFiles 列出文件
func (s *StorageService) ListFiles(bucket, folder string, limit int) ([]map[string]interface{}, error) {
	if !s.enabled {
		return nil, fmt.Errorf("文件存储服务未启用")
	}

	// TODO: 实现实际的Supabase文件列表获取
	log.Printf("模拟获取文件列表: bucket=%s, folder=%s, limit=%d", bucket, folder, limit)

	// 返回模拟的文件列表
	files := []map[string]interface{}{
		{
			"name":             "example.pdf",
			"id":               "123e4567-e89b-12d3-a456-426614174000",
			"updated_at":       time.Now().Format(time.RFC3339),
			"created_at":       time.Now().Format(time.RFC3339),
			"last_accessed_at": time.Now().Format(time.RFC3339),
			"metadata": map[string]interface{}{
				"size":     1024,
				"mimetype": "application/pdf",
			},
		},
	}

	return files, nil
}

// IsValidFileType 检查文件类型是否允许
func (s *StorageService) IsValidFileType(filename string, allowedTypes []string) bool {
	if len(allowedTypes) == 0 {
		return true // 如果没有限制，允许所有类型
	}

	ext := strings.ToLower(filepath.Ext(filename))
	for _, allowedType := range allowedTypes {
		if ext == strings.ToLower(allowedType) {
			return true
		}
	}

	return false
}

// GetMaxFileSize 获取最大文件大小限制（字节）
func (s *StorageService) GetMaxFileSize() int64 {
	// 默认限制为10MB
	return 10 * 1024 * 1024
}

// IsEnabled 检查存储服务是否启用
func (s *StorageService) IsEnabled() bool {
	return s.enabled
}
