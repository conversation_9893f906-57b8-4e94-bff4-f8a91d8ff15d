package service

import (
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"erp/backend/internal/dto"
	"erp/backend/internal/models"
	"gorm.io/gorm"
)

// PermissionService 权限管理服务
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService 创建权限管理服务
func NewPermissionService(db *gorm.DB) *PermissionService {
	return &PermissionService{
		db: db,
	}
}

// CreatePermission 创建权限
func (s *PermissionService) CreatePermission(req *dto.CreatePermissionRequest) (*dto.PermissionResponse, error) {
	// 检查权限代码是否已存在
	var existingPermission models.Permission
	if err := s.db.Where("code = ?", req.Code).First(&existingPermission).Error; err == nil {
		return nil, errors.New("权限代码已存在")
	}

	// 创建权限
	permission := &models.Permission{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
	}

	if err := s.db.Create(permission).Error; err != nil {
		return nil, fmt.Errorf("创建权限失败: %w", err)
	}

	return s.GetPermissionByID(permission.ID)
}

// GetPermissionByID 根据ID获取权限
func (s *PermissionService) GetPermissionByID(permissionID uint64) (*dto.PermissionResponse, error) {
	var permission models.Permission
	if err := s.db.First(&permission, permissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("权限不存在")
		}
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	// 获取拥有此权限的角色数量
	var roleCount int64
	s.db.Model(&models.RolePermission{}).Where("permission_id = ?", permissionID).Count(&roleCount)

	return s.buildPermissionResponse(&permission, roleCount), nil
}

// UpdatePermission 更新权限
func (s *PermissionService) UpdatePermission(permissionID uint64, req *dto.UpdatePermissionRequest) (*dto.PermissionResponse, error) {
	// 查找权限
	var permission models.Permission
	if err := s.db.First(&permission, permissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("权限不存在")
		}
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	// 更新权限信息
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Resource != "" {
		updates["resource"] = req.Resource
	}
	if req.Action != "" {
		updates["action"] = req.Action
	}
	updates["updated_at"] = time.Now()

	if len(updates) > 0 {
		if err := s.db.Model(&permission).Updates(updates).Error; err != nil {
			return nil, fmt.Errorf("更新权限失败: %w", err)
		}
	}

	// 返回更新后的权限信息
	return s.GetPermissionByID(permissionID)
}

// DeletePermission 删除权限
func (s *PermissionService) DeletePermission(permissionID uint64) error {
	// 查找权限
	var permission models.Permission
	if err := s.db.First(&permission, permissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("权限不存在")
		}
		return fmt.Errorf("查询权限失败: %w", err)
	}

	// 检查是否有角色使用此权限
	var roleCount int64
	s.db.Model(&models.RolePermission{}).Where("permission_id = ?", permissionID).Count(&roleCount)
	if roleCount > 0 {
		return errors.New("权限正在被角色使用，无法删除")
	}

	// 删除权限
	if err := s.db.Delete(&permission).Error; err != nil {
		return fmt.Errorf("删除权限失败: %w", err)
	}

	return nil
}

// GetPermissionList 获取权限列表
func (s *PermissionService) GetPermissionList(req *dto.PermissionListRequest) (*dto.PermissionListResponse, error) {
	query := s.db.Model(&models.Permission{})

	// 应用过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", keyword, keyword, keyword)
	}

	if req.Resource != "" {
		query = query.Where("resource = ?", req.Resource)
	}

	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计权限数量失败: %w", err)
	}

	// 应用排序
	orderClause := req.SortBy
	if req.SortDesc {
		orderClause += " DESC"
	}
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询权限列表
	var permissions []models.Permission
	if err := query.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("查询权限列表失败: %w", err)
	}

	// 构建响应
	permissionResponses := make([]dto.PermissionResponse, 0, len(permissions))
	for _, permission := range permissions {
		// 获取角色数量
		var roleCount int64
		s.db.Model(&models.RolePermission{}).Where("permission_id = ?", permission.ID).Count(&roleCount)
		
		permissionResponses = append(permissionResponses, *s.buildPermissionResponse(&permission, roleCount))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &dto.PermissionListResponse{
		Permissions: permissionResponses,
		Total:       total,
		Page:        req.Page,
		PageSize:    req.PageSize,
		TotalPages:  totalPages,
	}, nil
}

// GetPermissionTree 获取权限树
func (s *PermissionService) GetPermissionTree() (*dto.GetPermissionTreeResponse, error) {
	var permissions []models.Permission
	if err := s.db.Order("resource, action").Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	// 按资源分组构建树结构
	resourceMap := make(map[string][]*dto.PermissionTreeNode)
	
	for _, permission := range permissions {
		node := &dto.PermissionTreeNode{
			ID:          permission.ID,
			Name:        permission.Name,
			Code:        permission.Code,
			Resource:    permission.Resource,
			Action:      permission.Action,
			Description: permission.Description,
		}
		
		resourceMap[permission.Resource] = append(resourceMap[permission.Resource], node)
	}

	// 构建树结构
	var tree []*dto.PermissionTreeNode
	for resource, nodes := range resourceMap {
		resourceNode := &dto.PermissionTreeNode{
			Name:     resource,
			Code:     resource,
			Resource: resource,
			Children: nodes,
		}
		tree = append(tree, resourceNode)
	}

	return &dto.GetPermissionTreeResponse{
		Tree: tree,
	}, nil
}

// GetRolePermissionMatrix 获取角色权限矩阵
func (s *PermissionService) GetRolePermissionMatrix() (*dto.GetRolePermissionMatrixResponse, error) {
	// 获取所有角色
	var roles []models.Role
	if err := s.db.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 获取所有权限
	var permissions []models.Permission
	if err := s.db.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	// 构建权限信息
	permissionInfos := make([]dto.PermissionInfo, 0, len(permissions))
	for _, permission := range permissions {
		permissionInfos = append(permissionInfos, dto.PermissionInfo{
			ID:          permission.ID,
			Name:        permission.Name,
			Code:        permission.Code,
			Description: permission.Description,
			Resource:    permission.Resource,
			Action:      permission.Action,
		})
	}

	// 构建角色权限矩阵
	matrix := make([]dto.RolePermissionMatrix, 0, len(roles))
	for _, role := range roles {
		// 获取角色的权限ID列表
		var permissionIDs []uint64
		s.db.Model(&models.RolePermission{}).
			Where("role_id = ?", role.ID).
			Pluck("permission_id", &permissionIDs)

		matrix = append(matrix, dto.RolePermissionMatrix{
			RoleID:        role.ID,
			RoleName:      role.Name,
			RoleCode:      role.Code,
			PermissionIDs: permissionIDs,
		})
	}

	return &dto.GetRolePermissionMatrixResponse{
		Matrix:      matrix,
		Permissions: permissionInfos,
	}, nil
}

// buildPermissionResponse 构建权限响应
func (s *PermissionService) buildPermissionResponse(permission *models.Permission, roleCount int64) *dto.PermissionResponse {
	return &dto.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		Code:        permission.Code,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		RoleCount:   roleCount,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}
}

// GetPermissionsByResource 根据资源获取权限列表
func (s *PermissionService) GetPermissionsByResource(resource string) ([]dto.PermissionInfo, error) {
	var permissions []dto.PermissionInfo
	
	err := s.db.Model(&models.Permission{}).
		Select("id, name, code, description, resource, action").
		Where("resource = ?", resource).
		Order("action").
		Scan(&permissions).Error
	
	return permissions, err
}

// GetPermissionsByAction 根据操作获取权限列表
func (s *PermissionService) GetPermissionsByAction(action string) ([]dto.PermissionInfo, error) {
	var permissions []dto.PermissionInfo
	
	err := s.db.Model(&models.Permission{}).
		Select("id, name, code, description, resource, action").
		Where("action = ?", action).
		Order("resource").
		Scan(&permissions).Error
	
	return permissions, err
}

// SearchPermissions 搜索权限
func (s *PermissionService) SearchPermissions(keyword string) ([]dto.PermissionInfo, error) {
	var permissions []dto.PermissionInfo
	
	keyword = "%" + strings.TrimSpace(keyword) + "%"
	
	err := s.db.Model(&models.Permission{}).
		Select("id, name, code, description, resource, action").
		Where("name LIKE ? OR code LIKE ? OR description LIKE ? OR resource LIKE ? OR action LIKE ?", 
			keyword, keyword, keyword, keyword, keyword).
		Order("resource, action").
		Scan(&permissions).Error
	
	return permissions, err
}
