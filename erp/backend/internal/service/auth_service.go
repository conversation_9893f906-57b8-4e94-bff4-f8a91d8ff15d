package service

import (
	"errors"
	"fmt"
	"time"

	"erp/backend/internal/dto"
	"erp/backend/internal/models"
	"erp/backend/internal/utils"

	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db         *gorm.DB
	jwtManager *utils.JWTManager
	blacklist  utils.TokenBlacklist
	config     *AuthConfig
}

// AuthConfig 认证配置
type AuthConfig struct {
	MaxLoginAttempts      int
	LockoutDuration       time.Duration
	SessionTimeout        time.Duration
	MaxConcurrentSessions int
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist, config *AuthConfig) *AuthService {
	return &AuthService{
		db:         db,
		jwtManager: jwtManager,
		blacklist:  blacklist,
		config:     config,
	}
}

// Login 用户登录
func (s *AuthService) Login(req *dto.LoginRequest, userAgent, ipAddress string) (*dto.LoginResponse, error) {
	// 记录登录尝试
	attempt := &models.LoginAttempt{
		Username:  req.Username,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Success:   false,
	}

	// 查找用户
	var user models.User
	var tenant *models.Tenant

	// 如果提供了租户代码，先查找租户
	if req.TenantCode != "" {
		if err := s.db.Where("code = ? AND status = ?", req.TenantCode, models.TenantStatusActive).First(&tenant).Error; err != nil {
			attempt.FailReason = "租户不存在或已禁用"
			s.db.Create(attempt)
			return nil, errors.New("租户不存在或已禁用")
		}

		// 查找租户用户
		if err := s.db.Where("username = ? AND tenant_id = ? AND user_type = ?",
			req.Username, tenant.ID, models.UserTypeTenant).First(&user).Error; err != nil {
			attempt.FailReason = "用户不存在"
			s.db.Create(attempt)
			return nil, errors.New("用户名或密码错误")
		}
	} else {
		// 查找系统管理员
		if err := s.db.Where("username = ? AND user_type = ?",
			req.Username, models.UserTypeSystemAdmin).First(&user).Error; err != nil {
			attempt.FailReason = "用户不存在"
			s.db.Create(attempt)
			return nil, errors.New("用户名或密码错误")
		}
	}

	// 检查账户状态
	if user.Status != models.UserStatusActive {
		attempt.FailReason = "账户已禁用"
		s.db.Create(attempt)
		return nil, errors.New("账户已禁用")
	}

	// 检查账户是否被锁定
	if s.isAccountLocked(user.ID) {
		attempt.FailReason = "账户已锁定"
		s.db.Create(attempt)
		return nil, errors.New("账户已锁定，请稍后再试")
	}

	// 验证密码
	if !utils.CheckPassword(req.Password, user.PasswordHash) {
		attempt.FailReason = "密码错误"
		s.db.Create(attempt)

		// 增加失败次数
		s.incrementFailedAttempts(user.ID)
		return nil, errors.New("用户名或密码错误")
	}

	// 登录成功
	attempt.Success = true
	s.db.Create(attempt)

	// 重置失败次数
	s.resetFailedAttempts(user.ID)

	// 获取用户角色
	roles, err := s.getUserRoles(user.ID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 生成令牌
	accessToken, err := s.jwtManager.GenerateToken(
		user.ID, user.TenantID, user.Username, user.UserType, roles)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID, user.Username)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 创建会话记录
	session := &models.UserSession{
		UserID:    user.ID,
		TokenID:   utils.GenerateUUID(),
		UserAgent: userAgent,
		IPAddress: ipAddress,
		ExpiresAt: time.Now().Add(s.config.SessionTimeout),
	}
	if err := s.db.Create(session).Error; err != nil {
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}

	// 更新用户最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.db.Save(&user)

	// 构建用户信息
	userInfo := &dto.UserInfo{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Phone:       user.Phone,
		RealName:    user.RealName,
		Avatar:      user.AvatarURL,
		UserType:    user.UserType,
		Status:      user.Status,
		TenantID:    user.TenantID,
		Roles:       roles,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   user.CreatedAt,
	}

	if tenant != nil {
		userInfo.TenantName = tenant.Name
	}

	return &dto.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(s.config.SessionTimeout.Seconds()),
		User:         userInfo,
	}, nil
}

// RefreshToken 刷新令牌
func (s *AuthService) RefreshToken(req *dto.RefreshTokenRequest) (*dto.RefreshTokenResponse, error) {
	// 解析刷新令牌
	claims, err := s.jwtManager.ParseToken(req.RefreshToken)
	if err != nil {
		return nil, errors.New("无效的刷新令牌")
	}

	// 查找用户
	var user models.User
	if err := s.db.First(&user, claims.UserID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		return nil, errors.New("账户已禁用")
	}

	// 获取用户角色
	roles, err := s.getUserRoles(user.ID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 生成新的令牌
	newAccessToken, newRefreshToken, err := s.jwtManager.RefreshToken(
		req.RefreshToken, user.ID, user.TenantID, user.Username, user.UserType, roles)
	if err != nil {
		return nil, fmt.Errorf("刷新令牌失败: %w", err)
	}

	return &dto.RefreshTokenResponse{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(s.config.SessionTimeout.Seconds()),
	}, nil
}

// Logout 用户登出
func (s *AuthService) Logout(userID uint64, tokenString string) error {
	// 获取令牌ID
	tokenID, err := s.jwtManager.GetTokenID(tokenString)
	if err != nil {
		return err
	}

	// 将令牌加入黑名单
	claims, err := s.jwtManager.ParseToken(tokenString)
	if err == nil && claims.RegisteredClaims.ExpiresAt != nil {
		s.blacklist.AddToBlacklist(tokenID, claims.RegisteredClaims.ExpiresAt.Time)
	}

	// 删除会话记录（或者可以添加一个状态字段来标记为无效）
	s.db.Where("user_id = ? AND token_id = ?", userID, tokenID).
		Delete(&models.UserSession{})

	return nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uint64, req *dto.ChangePasswordRequest) error {
	// 查找用户
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if !utils.CheckPassword(req.OldPassword, user.PasswordHash) {
		return errors.New("原密码错误")
	}

	// 哈希新密码
	newPasswordHash, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = newPasswordHash
	user.UpdatedAt = time.Now()

	return s.db.Save(&user).Error
}

// isAccountLocked 检查账户是否被锁定
func (s *AuthService) isAccountLocked(userID uint64) bool {
	var count int64
	s.db.Model(&models.LoginAttempt{}).
		Where("username = (SELECT username FROM users WHERE id = ?) AND success = false AND created_at > ?",
			userID, time.Now().Add(-s.config.LockoutDuration)).
		Count(&count)

	return count >= int64(s.config.MaxLoginAttempts)
}

// incrementFailedAttempts 增加失败尝试次数
func (s *AuthService) incrementFailedAttempts(userID uint64) {
	// 这里可以实现更复杂的失败次数跟踪逻辑
	// 目前通过LoginAttempt表来跟踪
}

// resetFailedAttempts 重置失败尝试次数
func (s *AuthService) resetFailedAttempts(userID uint64) {
	// 删除最近的失败尝试记录
	s.db.Where("username = (SELECT username FROM users WHERE id = ?) AND success = false AND created_at > ?",
		userID, time.Now().Add(-s.config.LockoutDuration)).
		Delete(&models.LoginAttempt{})
}

// getUserRoles 获取用户角色
func (s *AuthService) getUserRoles(userID uint64) ([]string, error) {
	var roles []string

	err := s.db.Table("user_roles ur").
		Select("r.code").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND ur.deleted_at IS NULL AND r.deleted_at IS NULL", userID).
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now()).
		Pluck("r.code", &roles).Error

	return roles, err
}
