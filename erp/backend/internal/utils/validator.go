package utils

import (
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// CustomValidator 自定义验证器
type CustomValidator struct {
	validator *validator.Validate
}

// NewCustomValidator 创建自定义验证器
func NewCustomValidator() *CustomValidator {
	v := validator.New()

	// 注册自定义验证规则
	v.RegisterValidation("username", validateUsername)
	v.RegisterValidation("password", validatePassword)
	v.RegisterValidation("phone", validatePhone)
	v.RegisterValidation("tenant_code", validateTenantCode)

	// 注册自定义标签名称函数
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	return &CustomValidator{validator: v}
}

// ValidateStruct 验证结构体
func (cv *CustomValidator) ValidateStruct(obj interface{}) error {
	if err := cv.validator.Struct(obj); err != nil {
		return cv.formatValidationError(err)
	}
	return nil
}

// formatValidationError 格式化验证错误
func (cv *CustomValidator) formatValidationError(err error) error {
	var validationErrors validator.ValidationErrors
	if errors.As(err, &validationErrors) {
		var messages []string
		for _, e := range validationErrors {
			message := cv.getErrorMessage(e)
			messages = append(messages, message)
		}
		return errors.New(strings.Join(messages, "; "))
	}
	return err
}

// getErrorMessage 获取错误消息
func (cv *CustomValidator) getErrorMessage(e validator.FieldError) string {
	field := e.Field()
	tag := e.Tag()
	param := e.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("%s是必填字段", field)
	case "email":
		return fmt.Sprintf("%s必须是有效的邮箱地址", field)
	case "min":
		return fmt.Sprintf("%s长度不能少于%s个字符", field, param)
	case "max":
		return fmt.Sprintf("%s长度不能超过%s个字符", field, param)
	case "len":
		return fmt.Sprintf("%s长度必须是%s个字符", field, param)
	case "gte":
		return fmt.Sprintf("%s必须大于或等于%s", field, param)
	case "lte":
		return fmt.Sprintf("%s必须小于或等于%s", field, param)
	case "gt":
		return fmt.Sprintf("%s必须大于%s", field, param)
	case "lt":
		return fmt.Sprintf("%s必须小于%s", field, param)
	case "oneof":
		return fmt.Sprintf("%s必须是以下值之一: %s", field, param)
	case "username":
		return fmt.Sprintf("%s只能包含字母、数字、下划线，长度3-20位", field)
	case "password":
		return fmt.Sprintf("%s至少8位，必须包含字母和数字", field)
	case "phone":
		return fmt.Sprintf("%s必须是有效的手机号码", field)
	case "tenant_code":
		return fmt.Sprintf("%s只能包含字母、数字、下划线，长度2-20位", field)
	default:
		return fmt.Sprintf("%s验证失败", field)
	}
}

// validateUsername 验证用户名
func validateUsername(fl validator.FieldLevel) bool {
	username := fl.Field().String()
	return IsValidUsername(username)
}

// validatePassword 验证密码
func validatePassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	return IsValidPassword(password)
}

// validatePhone 验证手机号
func validatePhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	return IsValidPhone(phone)
}

// validateTenantCode 验证租户代码
func validateTenantCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	// 租户代码只能包含字母、数字、下划线，长度2-20位
	if len(code) < 2 || len(code) > 20 {
		return false
	}
	for _, r := range code {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_') {
			return false
		}
	}
	return true
}

// BindAndValidate 绑定并验证请求数据
func BindAndValidate(c *gin.Context, obj interface{}) error {
	// 绑定请求数据
	if err := c.ShouldBindJSON(obj); err != nil {
		return fmt.Errorf("请求数据格式错误: %w", err)
	}

	// 验证数据
	validator := NewCustomValidator()
	if err := validator.ValidateStruct(obj); err != nil {
		return err
	}

	return nil
}

// ValidateID 验证ID参数
func ValidateID(idStr string) (uint64, error) {
	if idStr == "" {
		return 0, errors.New("ID不能为空")
	}

	id, err := StringToUint64(idStr)
	if err != nil {
		return 0, errors.New("ID格式错误")
	}

	if id == 0 {
		return 0, errors.New("ID不能为0")
	}

	return id, nil
}

// ValidatePage 验证分页参数
func ValidatePage(pageStr, sizeStr string) (int, int, error) {
	page := 1
	size := 10

	if pageStr != "" {
		p, err := StringToInt(pageStr)
		if err != nil || p < 1 {
			return 0, 0, errors.New("页码必须是大于0的整数")
		}
		page = p
	}

	if sizeStr != "" {
		s, err := StringToInt(sizeStr)
		if err != nil || s < 1 || s > 100 {
			return 0, 0, errors.New("每页大小必须是1-100之间的整数")
		}
		size = s
	}

	return page, size, nil
}

// ValidateSort 验证排序参数
func ValidateSort(sortStr string, allowedFields []string) (string, string, error) {
	if sortStr == "" {
		return "", "", nil
	}

	parts := strings.Split(sortStr, ",")
	if len(parts) != 2 {
		return "", "", errors.New("排序格式错误，应为: field,order")
	}

	field := strings.TrimSpace(parts[0])
	order := strings.TrimSpace(parts[1])

	// 验证字段名
	if !Contains(allowedFields, field) {
		return "", "", fmt.Errorf("不支持的排序字段: %s", field)
	}

	// 验证排序方向
	if order != "asc" && order != "desc" {
		return "", "", errors.New("排序方向必须是asc或desc")
	}

	return field, order, nil
}

// ValidateStatus 验证状态参数
func ValidateStatus(statusStr string, allowedStatuses []int8) (int8, error) {
	if statusStr == "" {
		return 0, errors.New("状态不能为空")
	}

	status, err := strconv.ParseInt(statusStr, 10, 8)
	if err != nil {
		return 0, errors.New("状态格式错误")
	}

	statusInt8 := int8(status)
	found := false
	for _, s := range allowedStatuses {
		if s == statusInt8 {
			found = true
			break
		}
	}

	if !found {
		return 0, fmt.Errorf("无效的状态值: %d", statusInt8)
	}

	return statusInt8, nil
}

// ValidateUserType 验证用户类型
func ValidateUserType(userTypeStr string) (int8, error) {
	allowedTypes := []int8{1, 2} // 1: 租户用户, 2: 系统管理员
	return ValidateStatus(userTypeStr, allowedTypes)
}

// ValidateRoleType 验证角色类型
func ValidateRoleType(roleTypeStr string) (int8, error) {
	allowedTypes := []int8{1, 2} // 1: 租户角色, 2: 系统角色
	return ValidateStatus(roleTypeStr, allowedTypes)
}

// ValidatePermissionType 验证权限类型
func ValidatePermissionType(permissionTypeStr string) (int8, error) {
	allowedTypes := []int8{1, 2, 3} // 1: 菜单权限, 2: 按钮权限, 3: 数据权限
	return ValidateStatus(permissionTypeStr, allowedTypes)
}

// ValidateTimeRange 验证时间范围
func ValidateTimeRange(startTimeStr, endTimeStr string) (*time.Time, *time.Time, error) {
	var startTime, endTime *time.Time

	if startTimeStr != "" {
		t, err := ParseTime(startTimeStr)
		if err != nil {
			return nil, nil, fmt.Errorf("开始时间格式错误: %w", err)
		}
		startTime = &t
	}

	if endTimeStr != "" {
		t, err := ParseTime(endTimeStr)
		if err != nil {
			return nil, nil, fmt.Errorf("结束时间格式错误: %w", err)
		}
		endTime = &t
	}

	if startTime != nil && endTime != nil && startTime.After(*endTime) {
		return nil, nil, errors.New("开始时间不能晚于结束时间")
	}

	return startTime, endTime, nil
}

// init 初始化验证器
func init() {
	// 替换gin的默认验证器
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("username", validateUsername)
		v.RegisterValidation("password", validatePassword)
		v.RegisterValidation("phone", validatePhone)
		v.RegisterValidation("tenant_code", validateTenantCode)
	}
}
