package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Page    *PageInfo   `json:"page,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// PageInfo 分页信息
type PageInfo struct {
	Current  int   `json:"current"`  // 当前页码
	Size     int   `json:"size"`     // 每页大小
	Total    int64 `json:"total"`    // 总记录数
	Pages    int   `json:"pages"`    // 总页数
	HasNext  bool  `json:"has_next"` // 是否有下一页
	HasPrev  bool  `json:"has_prev"` // 是否有上一页
}

// 响应状态码常量
const (
	CodeSuccess             = 200  // 成功
	CodeBadRequest          = 400  // 请求参数错误
	CodeUnauthorized        = 401  // 未认证
	CodeForbidden           = 403  // 无权限
	CodeNotFound            = 404  // 资源不存在
	CodeMethodNotAllowed    = 405  // 方法不允许
	CodeConflict            = 409  // 资源冲突
	CodeValidationFailed    = 422  // 验证失败
	CodeTooManyRequests     = 429  // 请求过多
	CodeInternalServerError = 500  // 服务器内部错误
	CodeServiceUnavailable  = 503  // 服务不可用

	// 业务错误码
	CodeUserNotFound        = 1001 // 用户不存在
	CodeUserDisabled        = 1002 // 用户已禁用
	CodeInvalidCredentials  = 1003 // 凭据无效
	CodeTokenExpired        = 1004 // 令牌过期
	CodeTokenInvalid        = 1005 // 令牌无效
	CodePermissionDenied    = 1006 // 权限不足
	CodeTenantNotFound      = 1007 // 租户不存在
	CodeTenantDisabled      = 1008 // 租户已禁用
	CodeRoleNotFound        = 1009 // 角色不存在
	CodePermissionNotFound  = 1010 // 权限不存在
	CodeDuplicateResource   = 1011 // 资源重复
	CodeResourceInUse       = 1012 // 资源正在使用中
)

// 响应消息映射
var codeMessages = map[int]string{
	CodeSuccess:             "操作成功",
	CodeBadRequest:          "请求参数错误",
	CodeUnauthorized:        "未认证",
	CodeForbidden:           "无权限",
	CodeNotFound:            "资源不存在",
	CodeMethodNotAllowed:    "方法不允许",
	CodeConflict:            "资源冲突",
	CodeValidationFailed:    "验证失败",
	CodeTooManyRequests:     "请求过多",
	CodeInternalServerError: "服务器内部错误",
	CodeServiceUnavailable:  "服务不可用",

	CodeUserNotFound:        "用户不存在",
	CodeUserDisabled:        "用户已禁用",
	CodeInvalidCredentials:  "用户名或密码错误",
	CodeTokenExpired:        "令牌已过期",
	CodeTokenInvalid:        "令牌无效",
	CodePermissionDenied:    "权限不足",
	CodeTenantNotFound:      "租户不存在",
	CodeTenantDisabled:      "租户已禁用",
	CodeRoleNotFound:        "角色不存在",
	CodePermissionNotFound:  "权限不存在",
	CodeDuplicateResource:   "资源已存在",
	CodeResourceInUse:       "资源正在使用中，无法删除",
}

// GetMessage 获取状态码对应的消息
func GetMessage(code int) string {
	if msg, ok := codeMessages[code]; ok {
		return msg
	}
	return "未知错误"
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: GetMessage(CodeSuccess),
		Data:    data,
	})
}

// SuccessWithMessage 成功响应（自定义消息）
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: GetMessage(code),
	})
}

// ErrorWithMessage 错误响应（自定义消息）
func ErrorWithMessage(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
	})
}

// ErrorWithError 错误响应（包含错误详情）
func ErrorWithError(c *gin.Context, code int, err error) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: GetMessage(code),
		Error:   err.Error(),
	})
}

// BadRequest 请求参数错误
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    CodeBadRequest,
		Message: message,
	})
}

// Unauthorized 未认证
func Unauthorized(c *gin.Context) {
	c.JSON(http.StatusUnauthorized, Response{
		Code:    CodeUnauthorized,
		Message: GetMessage(CodeUnauthorized),
	})
}

// Forbidden 无权限
func Forbidden(c *gin.Context) {
	c.JSON(http.StatusForbidden, Response{
		Code:    CodeForbidden,
		Message: GetMessage(CodeForbidden),
	})
}

// NotFound 资源不存在
func NotFound(c *gin.Context) {
	c.JSON(http.StatusNotFound, Response{
		Code:    CodeNotFound,
		Message: GetMessage(CodeNotFound),
	})
}

// InternalServerError 服务器内部错误
func InternalServerError(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, Response{
		Code:    CodeInternalServerError,
		Message: GetMessage(CodeInternalServerError),
	})
}

// ValidationError 验证错误
func ValidationError(c *gin.Context, err error) {
	c.JSON(http.StatusUnprocessableEntity, Response{
		Code:    CodeValidationFailed,
		Message: GetMessage(CodeValidationFailed),
		Error:   err.Error(),
	})
}

// PageSuccess 分页成功响应
func PageSuccess(c *gin.Context, data interface{}, page *PageInfo) {
	c.JSON(http.StatusOK, PageResponse{
		Code:    CodeSuccess,
		Message: GetMessage(CodeSuccess),
		Data:    data,
		Page:    page,
	})
}

// NewPageInfo 创建分页信息
func NewPageInfo(current, size int, total int64) *PageInfo {
	pages := int((total + int64(size) - 1) / int64(size))
	if pages == 0 {
		pages = 1
	}

	return &PageInfo{
		Current: current,
		Size:    size,
		Total:   total,
		Pages:   pages,
		HasNext: current < pages,
		HasPrev: current > 1,
	}
}

// getHTTPStatus 根据业务状态码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch code {
	case CodeSuccess:
		return http.StatusOK
	case CodeBadRequest, CodeValidationFailed:
		return http.StatusBadRequest
	case CodeUnauthorized, CodeTokenExpired, CodeTokenInvalid:
		return http.StatusUnauthorized
	case CodeForbidden, CodePermissionDenied:
		return http.StatusForbidden
	case CodeNotFound, CodeUserNotFound, CodeTenantNotFound, CodeRoleNotFound, CodePermissionNotFound:
		return http.StatusNotFound
	case CodeMethodNotAllowed:
		return http.StatusMethodNotAllowed
	case CodeConflict, CodeDuplicateResource, CodeResourceInUse:
		return http.StatusConflict
	case CodeTooManyRequests:
		return http.StatusTooManyRequests
	case CodeServiceUnavailable:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// AbortWithError 中断请求并返回错误
func AbortWithError(c *gin.Context, code int) {
	httpStatus := getHTTPStatus(code)
	c.AbortWithStatusJSON(httpStatus, Response{
		Code:    code,
		Message: GetMessage(code),
	})
}

// AbortWithMessage 中断请求并返回自定义消息
func AbortWithMessage(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.AbortWithStatusJSON(httpStatus, Response{
		Code:    code,
		Message: message,
	})
}
