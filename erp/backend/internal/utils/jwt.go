package utils

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   uint64   `json:"user_id"`
	TenantID *uint64  `json:"tenant_id"`
	Username string   `json:"username"`
	UserType int8     `json:"user_type"`
	Roles    []string `json:"roles"`
	jwt.RegisteredClaims
}

// JWTManager JWT管理器
type JWTManager struct {
	secret                string
	expireDuration        time.Duration
	refreshExpireDuration time.Duration
	issuer                string
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secret string, expireSeconds, refreshExpireSeconds int, issuer string) *JWTManager {
	return &JWTManager{
		secret:                secret,
		expireDuration:        time.Duration(expireSeconds) * time.Second,
		refreshExpireDuration: time.Duration(refreshExpireSeconds) * time.Second,
		issuer:                issuer,
	}
}

// GenerateToken 生成访问令牌
func (j *JWTManager) GenerateToken(userID uint64, tenantID *uint64, username string, userType int8, roles []string) (string, error) {
	now := time.Now()
	claims := &JWTClaims{
		UserID:   userID,
		TenantID: tenantID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   username,
			Audience:  []string{"erp-system"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expireDuration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        GenerateUUID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secret))
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTManager) GenerateRefreshToken(userID uint64, username string) (string, error) {
	now := time.Now()
	claims := &JWTClaims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   username,
			Audience:  []string{"erp-system-refresh"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshExpireDuration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        GenerateUUID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secret))
}

// ParseToken 解析令牌
func (j *JWTManager) ParseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return []byte(j.secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// ValidateToken 验证令牌
func (j *JWTManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("token has expired")
	}

	// 检查令牌是否还未生效
	if claims.NotBefore != nil && claims.NotBefore.Time.After(time.Now()) {
		return nil, errors.New("token not valid yet")
	}

	return claims, nil
}

// RefreshToken 刷新令牌
func (j *JWTManager) RefreshToken(refreshTokenString string, userID uint64, tenantID *uint64, username string, userType int8, roles []string) (string, string, error) {
	// 验证刷新令牌
	refreshClaims, err := j.ParseToken(refreshTokenString)
	if err != nil {
		return "", "", errors.New("invalid refresh token")
	}

	// 检查是否为刷新令牌
	if len(refreshClaims.Audience) == 0 || refreshClaims.Audience[0] != "erp-system-refresh" {
		return "", "", errors.New("not a refresh token")
	}

	// 检查用户ID是否匹配
	if refreshClaims.UserID != userID {
		return "", "", errors.New("user ID mismatch")
	}

	// 生成新的访问令牌和刷新令牌
	newAccessToken, err := j.GenerateToken(userID, tenantID, username, userType, roles)
	if err != nil {
		return "", "", err
	}

	newRefreshToken, err := j.GenerateRefreshToken(userID, username)
	if err != nil {
		return "", "", err
	}

	return newAccessToken, newRefreshToken, nil
}

// GetTokenID 获取令牌ID
func (j *JWTManager) GetTokenID(tokenString string) (string, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.ID, nil
}

// IsSystemAdmin 检查是否为系统管理员
func (claims *JWTClaims) IsSystemAdmin() bool {
	return claims.UserType == 2 // UserTypeSystemAdmin
}

// IsTenantUser 检查是否为租户用户
func (claims *JWTClaims) IsTenantUser() bool {
	return claims.UserType == 1 && claims.TenantID != nil // UserTypeTenant
}

// HasRole 检查是否拥有指定角色
func (claims *JWTClaims) HasRole(role string) bool {
	for _, r := range claims.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole 检查是否拥有任意一个指定角色
func (claims *JWTClaims) HasAnyRole(roles []string) bool {
	for _, role := range roles {
		if claims.HasRole(role) {
			return true
		}
	}
	return false
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// NewTokenPair 创建令牌对
func NewTokenPair(accessToken, refreshToken string, expiresIn time.Duration) *TokenPair {
	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(expiresIn.Seconds()),
	}
}

// TokenBlacklist 令牌黑名单接口
type TokenBlacklist interface {
	// AddToBlacklist 添加令牌到黑名单
	AddToBlacklist(tokenID string, expireAt time.Time) error

	// IsBlacklisted 检查令牌是否在黑名单中
	IsBlacklisted(tokenID string) (bool, error)

	// CleanExpired 清理过期的黑名单令牌
	CleanExpired() error
}

// MemoryTokenBlacklist 内存令牌黑名单实现
type MemoryTokenBlacklist struct {
	blacklist map[string]time.Time
}

// NewMemoryTokenBlacklist 创建内存令牌黑名单
func NewMemoryTokenBlacklist() *MemoryTokenBlacklist {
	return &MemoryTokenBlacklist{
		blacklist: make(map[string]time.Time),
	}
}

// AddToBlacklist 添加令牌到黑名单
func (m *MemoryTokenBlacklist) AddToBlacklist(tokenID string, expireAt time.Time) error {
	m.blacklist[tokenID] = expireAt
	return nil
}

// IsBlacklisted 检查令牌是否在黑名单中
func (m *MemoryTokenBlacklist) IsBlacklisted(tokenID string) (bool, error) {
	expireAt, exists := m.blacklist[tokenID]
	if !exists {
		return false, nil
	}

	// 如果令牌已过期，从黑名单中移除
	if expireAt.Before(time.Now()) {
		delete(m.blacklist, tokenID)
		return false, nil
	}

	return true, nil
}

// CleanExpired 清理过期的黑名单令牌
func (m *MemoryTokenBlacklist) CleanExpired() error {
	now := time.Now()
	for tokenID, expireAt := range m.blacklist {
		if expireAt.Before(now) {
			delete(m.blacklist, tokenID)
		}
	}
	return nil
}
