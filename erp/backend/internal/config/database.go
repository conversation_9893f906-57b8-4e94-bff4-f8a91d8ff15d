package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string        `json:"host" mapstructure:"host"`
	Port            int           `json:"port" mapstructure:"port"`
	Username        string        `json:"username" mapstructure:"username"`
	Password        string        `json:"password" mapstructure:"password"`
	Database        string        `json:"database" mapstructure:"database"`
	SSLMode         string        `json:"ssl_mode" mapstructure:"ssl_mode"`
	Timezone        string        `json:"timezone" mapstructure:"timezone"`
	MaxIdleConns    int           `json:"max_idle_conns" mapstructure:"max_idle_conns"`
	MaxOpenConns    int           `json:"max_open_conns" mapstructure:"max_open_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	LogLevel        string        `json:"log_level" mapstructure:"log_level"`
}

// DefaultDatabaseConfig 默认数据库配置
func DefaultDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Host:            "localhost",
		Port:            5432,
		Username:        "postgres",
		Password:        "password",
		Database:        "erp_db",
		SSLMode:         "disable",
		Timezone:        "Asia/Shanghai",
		MaxIdleConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: time.Hour,
		LogLevel:        "info",
	}
}

// LoadDatabaseConfigFromEnv 从环境变量加载数据库配置
func LoadDatabaseConfigFromEnv() *DatabaseConfig {
	config := DefaultDatabaseConfig()

	if host := os.Getenv("DB_HOST"); host != "" {
		config.Host = host
	}

	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if username := os.Getenv("DB_USERNAME"); username != "" {
		config.Username = username
	}

	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Password = password
	}

	if database := os.Getenv("DB_DATABASE"); database != "" {
		config.Database = database
	}

	if sslMode := os.Getenv("DB_SSL_MODE"); sslMode != "" {
		config.SSLMode = sslMode
	}

	if timezone := os.Getenv("DB_TIMEZONE"); timezone != "" {
		config.Timezone = timezone
	}

	if maxIdle := os.Getenv("DB_MAX_IDLE_CONNS"); maxIdle != "" {
		if m, err := strconv.Atoi(maxIdle); err == nil {
			config.MaxIdleConns = m
		}
	}

	if maxOpen := os.Getenv("DB_MAX_OPEN_CONNS"); maxOpen != "" {
		if m, err := strconv.Atoi(maxOpen); err == nil {
			config.MaxOpenConns = m
		}
	}

	if lifetime := os.Getenv("DB_CONN_MAX_LIFETIME"); lifetime != "" {
		if l, err := time.ParseDuration(lifetime); err == nil {
			config.ConnMaxLifetime = l
		}
	}

	if logLevel := os.Getenv("DB_LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	return config
}

// DSN 生成数据库连接字符串
func (c *DatabaseConfig) DSN() string {
	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Host, c.Port, c.Username, c.Password, c.Database, c.SSLMode, c.Timezone,
	)
}

// Validate 验证数据库配置
func (c *DatabaseConfig) Validate() error {
	if c.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("database port must be between 1 and 65535")
	}
	if c.Username == "" {
		return fmt.Errorf("database username is required")
	}
	if c.Database == "" {
		return fmt.Errorf("database name is required")
	}
	if c.MaxIdleConns < 0 {
		return fmt.Errorf("max idle connections must be non-negative")
	}
	if c.MaxOpenConns < 0 {
		return fmt.Errorf("max open connections must be non-negative")
	}
	if c.MaxIdleConns > c.MaxOpenConns && c.MaxOpenConns > 0 {
		return fmt.Errorf("max idle connections cannot be greater than max open connections")
	}
	return nil
}

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	db     *gorm.DB
	config *DatabaseConfig
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(config *DatabaseConfig) *DatabaseManager {
	return &DatabaseManager{
		config: config,
	}
}

// Connect 连接数据库
func (dm *DatabaseManager) Connect() error {
	if err := dm.config.Validate(); err != nil {
		return fmt.Errorf("invalid database config: %w", err)
	}

	// 配置GORM日志级别
	logLevel := logger.Info
	switch dm.config.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	}

	// GORM配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NamingStrategy: &CustomNamingStrategy{},
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dm.config.DSN()), gormConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxIdleConns(dm.config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(dm.config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(dm.config.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	dm.db = db
	log.Printf("Connected to database successfully: %s:%d/%s", 
		dm.config.Host, dm.config.Port, dm.config.Database)
	
	return nil
}

// GetDB 获取数据库连接
func (dm *DatabaseManager) GetDB() *gorm.DB {
	return dm.db
}

// Close 关闭数据库连接
func (dm *DatabaseManager) Close() error {
	if dm.db != nil {
		sqlDB, err := dm.db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// HealthCheck 健康检查
func (dm *DatabaseManager) HealthCheck() error {
	if dm.db == nil {
		return fmt.Errorf("database connection is nil")
	}

	sqlDB, err := dm.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// GetStats 获取连接池统计信息
func (dm *DatabaseManager) GetStats() (map[string]interface{}, error) {
	if dm.db == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	sqlDB, err := dm.db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration.String(),
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_idle_time_closed":    stats.MaxIdleTimeClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}, nil
}

// CustomNamingStrategy 自定义命名策略
type CustomNamingStrategy struct{}

// TableName 表名策略
func (ns *CustomNamingStrategy) TableName(str string) string {
	// 使用默认的表名策略（复数形式）
	return gorm.NamingStrategy{}.TableName(str)
}

// SchemaName 模式名策略
func (ns *CustomNamingStrategy) SchemaName(table string) string {
	return gorm.NamingStrategy{}.SchemaName(table)
}

// ColumnName 列名策略
func (ns *CustomNamingStrategy) ColumnName(table, column string) string {
	// 使用蛇形命名法
	return gorm.NamingStrategy{}.ColumnName(table, column)
}

// JoinTableName 连接表名策略
func (ns *CustomNamingStrategy) JoinTableName(str string) string {
	return gorm.NamingStrategy{}.JoinTableName(str)
}

// RelationshipFKName 外键名策略
func (ns *CustomNamingStrategy) RelationshipFKName(rel gorm.Relationship) string {
	return gorm.NamingStrategy{}.RelationshipFKName(rel)
}

// CheckerName 检查器名策略
func (ns *CustomNamingStrategy) CheckerName(table, column string) string {
	return gorm.NamingStrategy{}.CheckerName(table, column)
}

// IndexName 索引名策略
func (ns *CustomNamingStrategy) IndexName(table, column string) string {
	return gorm.NamingStrategy{}.IndexName(table, column)
}
