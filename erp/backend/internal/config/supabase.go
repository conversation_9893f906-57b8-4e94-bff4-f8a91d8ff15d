package config

import (
	"fmt"
	"log"

	"github.com/supabase-community/supabase-go"
)

// SupabaseManager Supabase客户端管理器
type SupabaseManager struct {
	client *supabase.Client
	config *SupabaseConfig
}

// NewSupabaseManager 创建Supabase管理器
func NewSupabaseManager(config *SupabaseConfig) (*SupabaseManager, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("Supabase未启用")
	}

	if config.URL == "" {
		return nil, fmt.Errorf("Supabase URL不能为空")
	}

	if config.ServiceKey == "" {
		return nil, fmt.Errorf("Supabase服务密钥不能为空")
	}

	// 创建Supabase客户端
	client, err := supabase.NewClient(config.URL, config.ServiceKey, &supabase.ClientOptions{
		Headers: map[string]string{
			"apikey": config.ServiceKey,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建Supabase客户端失败: %w", err)
	}

	manager := &SupabaseManager{
		client: client,
		config: config,
	}

	log.Printf("Supabase客户端初始化成功: %s", config.URL)
	return manager, nil
}

// GetClient 获取Supabase客户端
func (sm *SupabaseManager) GetClient() *supabase.Client {
	return sm.client
}

// GetConfig 获取Supabase配置
func (sm *SupabaseManager) GetConfig() *SupabaseConfig {
	return sm.config
}

// GetDatabaseURL 获取PostgreSQL连接URL（用于GORM）
func (sm *SupabaseManager) GetDatabaseURL() string {
	// Supabase的PostgreSQL连接URL格式
	// 注意：这需要从Supabase项目设置中获取实际的数据库连接信息
	// 这里提供一个基本的格式，实际使用时需要用户提供完整的连接字符串
	return fmt.Sprintf("postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres")
}

// IsEnabled 检查Supabase是否启用
func (sm *SupabaseManager) IsEnabled() bool {
	return sm.config.Enabled
}

// TestConnection 测试Supabase连接
func (sm *SupabaseManager) TestConnection() error {
	if sm.client == nil {
		return fmt.Errorf("Supabase客户端未初始化")
	}

	// 简化连接测试 - 只检查客户端是否正确初始化
	// 实际的数据库连接测试将通过GORM进行
	log.Println("Supabase客户端连接测试成功")
	return nil
}

// Close 关闭Supabase连接
func (sm *SupabaseManager) Close() error {
	// Supabase Go客户端通常不需要显式关闭
	// 但我们可以在这里做一些清理工作
	log.Println("Supabase客户端已关闭")
	return nil
}
