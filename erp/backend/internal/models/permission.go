package models

import (
	"time"
)

// Permission 权限模型
type Permission struct {
	ID          uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null;comment:权限名称"`
	Code        string    `json:"code" gorm:"type:varchar(100);uniqueIndex;not null;comment:权限代码"`
	Description string    `json:"description" gorm:"type:text;comment:权限描述"`
	Module      string    `json:"module" gorm:"type:varchar(100);index;comment:所属模块"`
	Resource    string    `json:"resource" gorm:"type:varchar(100);comment:资源标识"`
	Action      string    `json:"action" gorm:"type:varchar(50);comment:操作类型"`
	Status      int8      `json:"status" gorm:"type:smallint;default:1;index;comment:状态：1-正常，2-禁用"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	RolePermissions []RolePermission `json:"role_permissions,omitempty" gorm:"foreignKey:PermissionID"`
	Roles           []Role           `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// IsActive 检查权限是否激活
func (p *Permission) IsActive() bool {
	return p.Status == PermissionStatusActive
}

// 权限状态常量
const (
	PermissionStatusActive   = 1 // 正常
	PermissionStatusDisabled = 2 // 禁用
)

// 权限操作类型常量
const (
	ActionCreate = "create" // 创建
	ActionRead   = "read"   // 读取
	ActionUpdate = "update" // 更新
	ActionDelete = "delete" // 删除
)

// PermissionCreateRequest 创建权限请求
type PermissionCreateRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100" label:"权限名称"`
	Code        string `json:"code" binding:"required,min=2,max=100" label:"权限代码"`
	Description string `json:"description" binding:"omitempty,max=500" label:"权限描述"`
	Module      string `json:"module" binding:"required,min=2,max=100" label:"所属模块"`
	Resource    string `json:"resource" binding:"required,min=2,max=100" label:"资源标识"`
	Action      string `json:"action" binding:"required,oneof=create read update delete" label:"操作类型"`
}

// PermissionUpdateRequest 更新权限请求
type PermissionUpdateRequest struct {
	Name        string `json:"name" binding:"omitempty,min=2,max=100" label:"权限名称"`
	Description string `json:"description" binding:"omitempty,max=500" label:"权限描述"`
	Module      string `json:"module" binding:"omitempty,min=2,max=100" label:"所属模块"`
	Resource    string `json:"resource" binding:"omitempty,min=2,max=100" label:"资源标识"`
	Action      string `json:"action" binding:"omitempty,oneof=create read update delete" label:"操作类型"`
	Status      int8   `json:"status" binding:"omitempty,oneof=1 2" label:"状态"`
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID          uint64    `json:"id"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Description string    `json:"description"`
	Module      string    `json:"module"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	ActionText  string    `json:"action_text"`
	Status      int8      `json:"status"`
	StatusText  string    `json:"status_text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (p *Permission) ToResponse() *PermissionResponse {
	resp := &PermissionResponse{
		ID:          p.ID,
		Name:        p.Name,
		Code:        p.Code,
		Description: p.Description,
		Module:      p.Module,
		Resource:    p.Resource,
		Action:      p.Action,
		Status:      p.Status,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}

	// 操作类型文本
	switch p.Action {
	case ActionCreate:
		resp.ActionText = "创建"
	case ActionRead:
		resp.ActionText = "读取"
	case ActionUpdate:
		resp.ActionText = "更新"
	case ActionDelete:
		resp.ActionText = "删除"
	default:
		resp.ActionText = p.Action
	}

	// 状态文本
	switch p.Status {
	case PermissionStatusActive:
		resp.StatusText = "正常"
	case PermissionStatusDisabled:
		resp.StatusText = "禁用"
	default:
		resp.StatusText = "未知"
	}

	return resp
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	RoleID       uint64    `json:"role_id" gorm:"not null;index"`
	PermissionID uint64    `json:"permission_id" gorm:"not null;index"`
	GrantedBy    *uint64   `json:"granted_by" gorm:"comment:授权者用户ID"`
	GrantedAt    time.Time `json:"granted_at" gorm:"autoCreateTime"`

	// 关联关系
	Role          Role       `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	Permission    Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
	GrantedByUser *User      `json:"granted_by_user,omitempty" gorm:"foreignKey:GrantedBy"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}

// RolePermissionCreateRequest 分配权限请求
type RolePermissionCreateRequest struct {
	RoleID       uint64 `json:"role_id" binding:"required" label:"角色ID"`
	PermissionID uint64 `json:"permission_id" binding:"required" label:"权限ID"`
}

// RolePermissionResponse 角色权限响应
type RolePermissionResponse struct {
	ID           uint64    `json:"id"`
	RoleID       uint64    `json:"role_id"`
	PermissionID uint64    `json:"permission_id"`
	GrantedBy    *uint64   `json:"granted_by"`
	GrantedAt    time.Time `json:"granted_at"`

	// 关联数据
	Role          *RoleResponse       `json:"role,omitempty"`
	Permission    *PermissionResponse `json:"permission,omitempty"`
	GrantedByUser *UserResponse       `json:"granted_by_user,omitempty"`
}

// ToResponse 转换为响应格式
func (rp *RolePermission) ToResponse() *RolePermissionResponse {
	resp := &RolePermissionResponse{
		ID:           rp.ID,
		RoleID:       rp.RoleID,
		PermissionID: rp.PermissionID,
		GrantedBy:    rp.GrantedBy,
		GrantedAt:    rp.GrantedAt,
	}

	// 关联数据
	if rp.Role.ID != 0 {
		resp.Role = rp.Role.ToResponse()
	}
	if rp.Permission.ID != 0 {
		resp.Permission = rp.Permission.ToResponse()
	}
	if rp.GrantedByUser != nil && rp.GrantedByUser.ID != 0 {
		resp.GrantedByUser = rp.GrantedByUser.ToResponse()
	}

	return resp
}

// PermissionGroup 权限分组（用于前端展示）
type PermissionGroup struct {
	Module      string               `json:"module"`
	ModuleName  string               `json:"module_name"`
	Permissions []PermissionResponse `json:"permissions"`
}

// GroupPermissionsByModule 按模块分组权限
func GroupPermissionsByModule(permissions []Permission) []PermissionGroup {
	moduleMap := make(map[string][]PermissionResponse)
	moduleNames := map[string]string{
		"tenant":     "租户管理",
		"user":       "用户管理",
		"role":       "角色管理",
		"permission": "权限管理",
		"system":     "系统管理",
	}

	for _, permission := range permissions {
		if _, exists := moduleMap[permission.Module]; !exists {
			moduleMap[permission.Module] = make([]PermissionResponse, 0)
		}
		moduleMap[permission.Module] = append(moduleMap[permission.Module], *permission.ToResponse())
	}

	var groups []PermissionGroup
	for module, perms := range moduleMap {
		moduleName := moduleNames[module]
		if moduleName == "" {
			moduleName = module
		}

		groups = append(groups, PermissionGroup{
			Module:      module,
			ModuleName:  moduleName,
			Permissions: perms,
		})
	}

	return groups
}
