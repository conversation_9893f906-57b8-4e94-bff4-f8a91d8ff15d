package models

import (
	"encoding/json"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID              uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID        *uint64        `json:"tenant_id" gorm:"index;comment:租户ID，系统管理员为NULL"`
	Username        string         `json:"username" gorm:"type:varchar(100);uniqueIndex;not null;comment:用户名"`
	Email           string         `json:"email" gorm:"type:varchar(255);uniqueIndex;not null;comment:邮箱"`
	Phone           string         `json:"phone" gorm:"type:varchar(50);comment:手机号"`
	PasswordHash    string         `json:"-" gorm:"type:varchar(255);not null;comment:密码哈希"`
	RealName        string         `json:"real_name" gorm:"type:varchar(100);comment:真实姓名"`
	AvatarURL       string         `json:"avatar_url" gorm:"type:varchar(500);comment:头像URL"`
	UserType        int8           `json:"user_type" gorm:"type:smallint;default:1;index;comment:用户类型：1-租户用户，2-系统管理员"`
	Status          int8           `json:"status" gorm:"type:smallint;default:1;index;comment:状态：1-正常，2-暂停，3-禁用"`
	LastLoginAt     *time.Time     `json:"last_login_at" gorm:"comment:最后登录时间"`
	LastLoginIP     string         `json:"last_login_ip" gorm:"type:varchar(45);comment:最后登录IP"`
	EmailVerifiedAt *time.Time     `json:"email_verified_at" gorm:"comment:邮箱验证时间"`
	PhoneVerifiedAt *time.Time     `json:"phone_verified_at" gorm:"comment:手机验证时间"`
	Settings        datatypes.JSON `json:"settings" gorm:"type:jsonb;comment:用户个人设置"`
	CreatedAt       time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:软删除时间"`

	// 关联关系
	Tenant    *Tenant       `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	UserRoles []UserRole    `json:"user_roles,omitempty" gorm:"foreignKey:UserID"`
	Roles     []Role        `json:"roles,omitempty" gorm:"many2many:user_roles;"`
	Sessions  []UserSession `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 如果没有设置用户类型，根据租户ID判断
	if u.UserType == 0 {
		if u.TenantID == nil {
			u.UserType = UserTypeSystemAdmin
		} else {
			u.UserType = UserTypeTenant
		}
	}
	return nil
}

// SetPassword 设置密码
func (u *User) SetPassword(password string) error {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.PasswordHash = string(hash)
	return nil
}

// CheckPassword 验证密码
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsSystemAdmin 检查是否为系统管理员
func (u *User) IsSystemAdmin() bool {
	return u.UserType == UserTypeSystemAdmin
}

// IsTenantUser 检查是否为租户用户
func (u *User) IsTenantUser() bool {
	return u.UserType == UserTypeTenant && u.TenantID != nil
}

// IsEmailVerified 检查邮箱是否已验证
func (u *User) IsEmailVerified() bool {
	return u.EmailVerifiedAt != nil
}

// IsPhoneVerified 检查手机是否已验证
func (u *User) IsPhoneVerified() bool {
	return u.PhoneVerifiedAt != nil
}

// GetSetting 获取用户设置
func (u *User) GetSetting(key string) interface{} {
	if u.Settings == nil {
		return nil
	}

	var settings map[string]interface{}
	if err := json.Unmarshal(u.Settings, &settings); err != nil {
		return nil
	}

	return settings[key]
}

// SetSetting 设置用户配置
func (u *User) SetSetting(key string, value interface{}) error {
	var settings map[string]interface{}
	if u.Settings != nil {
		if err := json.Unmarshal(u.Settings, &settings); err != nil {
			return err
		}
	} else {
		settings = make(map[string]interface{})
	}

	settings[key] = value

	settingsJSON, err := json.Marshal(settings)
	if err != nil {
		return err
	}

	u.Settings = datatypes.JSON(settingsJSON)
	return nil
}

// UpdateLastLogin 更新最后登录信息
func (u *User) UpdateLastLogin(ip string) {
	now := time.Now()
	u.LastLoginAt = &now
	u.LastLoginIP = ip
}

// 用户类型常量
const (
	UserTypeTenant      = 1 // 租户用户
	UserTypeSystemAdmin = 2 // 系统管理员
)

// 用户状态常量
const (
	UserStatusActive    = 1 // 正常
	UserStatusSuspended = 2 // 暂停
	UserStatusDisabled  = 3 // 禁用
)

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	TenantID *uint64                `json:"tenant_id" label:"租户ID"`
	Username string                 `json:"username" binding:"required,min=3,max=100,alphanum" label:"用户名"`
	Email    string                 `json:"email" binding:"required,email" label:"邮箱"`
	Phone    string                 `json:"phone" binding:"omitempty,min=10,max=20" label:"手机号"`
	Password string                 `json:"password" binding:"required,min=6,max=50" label:"密码"`
	RealName string                 `json:"real_name" binding:"omitempty,max=100" label:"真实姓名"`
	UserType int8                   `json:"user_type" binding:"omitempty,oneof=1 2" label:"用户类型"`
	RoleIDs  []uint64               `json:"role_ids" label:"角色ID列表"`
	Settings map[string]interface{} `json:"settings" label:"用户设置"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Email     string                 `json:"email" binding:"omitempty,email" label:"邮箱"`
	Phone     string                 `json:"phone" binding:"omitempty,min=10,max=20" label:"手机号"`
	RealName  string                 `json:"real_name" binding:"omitempty,max=100" label:"真实姓名"`
	AvatarURL string                 `json:"avatar_url" binding:"omitempty,url" label:"头像URL"`
	Status    int8                   `json:"status" binding:"omitempty,oneof=1 2 3" label:"状态"`
	RoleIDs   []uint64               `json:"role_ids" label:"角色ID列表"`
	Settings  map[string]interface{} `json:"settings" label:"用户设置"`
}

// UserChangePasswordRequest 修改密码请求
type UserChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required" label:"原密码"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=50" label:"新密码"`
}

// UserResetPasswordRequest 重置密码请求
type UserResetPasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required,min=6,max=50" label:"新密码"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID              uint64                 `json:"id"`
	TenantID        *uint64                `json:"tenant_id"`
	Username        string                 `json:"username"`
	Email           string                 `json:"email"`
	Phone           string                 `json:"phone"`
	RealName        string                 `json:"real_name"`
	AvatarURL       string                 `json:"avatar_url"`
	UserType        int8                   `json:"user_type"`
	UserTypeText    string                 `json:"user_type_text"`
	Status          int8                   `json:"status"`
	StatusText      string                 `json:"status_text"`
	LastLoginAt     *time.Time             `json:"last_login_at"`
	LastLoginIP     string                 `json:"last_login_ip"`
	EmailVerifiedAt *time.Time             `json:"email_verified_at"`
	PhoneVerifiedAt *time.Time             `json:"phone_verified_at"`
	Settings        map[string]interface{} `json:"settings"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`

	// 关联数据
	Tenant *TenantResponse `json:"tenant,omitempty"`
	Roles  []RoleResponse  `json:"roles,omitempty"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	resp := &UserResponse{
		ID:              u.ID,
		TenantID:        u.TenantID,
		Username:        u.Username,
		Email:           u.Email,
		Phone:           u.Phone,
		RealName:        u.RealName,
		AvatarURL:       u.AvatarURL,
		UserType:        u.UserType,
		Status:          u.Status,
		LastLoginAt:     u.LastLoginAt,
		LastLoginIP:     u.LastLoginIP,
		EmailVerifiedAt: u.EmailVerifiedAt,
		PhoneVerifiedAt: u.PhoneVerifiedAt,
		CreatedAt:       u.CreatedAt,
		UpdatedAt:       u.UpdatedAt,
	}

	// 用户类型文本
	switch u.UserType {
	case UserTypeTenant:
		resp.UserTypeText = "租户用户"
	case UserTypeSystemAdmin:
		resp.UserTypeText = "系统管理员"
	default:
		resp.UserTypeText = "未知"
	}

	// 状态文本
	switch u.Status {
	case UserStatusActive:
		resp.StatusText = "正常"
	case UserStatusSuspended:
		resp.StatusText = "暂停"
	case UserStatusDisabled:
		resp.StatusText = "禁用"
	default:
		resp.StatusText = "未知"
	}

	// 设置信息
	if u.Settings != nil {
		var settings map[string]interface{}
		if err := json.Unmarshal(u.Settings, &settings); err == nil {
			resp.Settings = settings
		}
	}

	// 关联数据
	if u.Tenant != nil {
		resp.Tenant = u.Tenant.ToResponse()
	}

	if len(u.Roles) > 0 {
		resp.Roles = make([]RoleResponse, len(u.Roles))
		for i, role := range u.Roles {
			resp.Roles[i] = *role.ToResponse()
		}
	}

	return resp
}
