package models

import (
	"time"
	"gorm.io/gorm"
)

// UserSession 用户会话模型
type UserSession struct {
	ID         uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID     uint64    `json:"user_id" gorm:"not null;index"`
	TokenID    string    `json:"token_id" gorm:"type:varchar(255);uniqueIndex;not null;comment:JWT Token ID"`
	DeviceInfo string    `json:"device_info" gorm:"type:text;comment:设备信息"`
	IPAddress  string    `json:"ip_address" gorm:"type:varchar(45);comment:IP地址"`
	UserAgent  string    `json:"user_agent" gorm:"type:text;comment:用户代理"`
	ExpiresAt  time.Time `json:"expires_at" gorm:"not null;index;comment:过期时间"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	LastUsedAt time.Time `json:"last_used_at" gorm:"autoUpdateTime"`

	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (UserSession) TableName() string {
	return "user_sessions"
}

// IsExpired 检查会话是否过期
func (us *UserSession) IsExpired() bool {
	return us.ExpiresAt.Before(time.Now())
}

// IsValid 检查会话是否有效
func (us *UserSession) IsValid() bool {
	return !us.IsExpired()
}

// UpdateLastUsed 更新最后使用时间
func (us *UserSession) UpdateLastUsed() {
	us.LastUsedAt = time.Now()
}

// UserSessionCreateRequest 创建会话请求
type UserSessionCreateRequest struct {
	UserID     uint64    `json:"user_id" binding:"required" label:"用户ID"`
	TokenID    string    `json:"token_id" binding:"required" label:"Token ID"`
	DeviceInfo string    `json:"device_info" label:"设备信息"`
	IPAddress  string    `json:"ip_address" label:"IP地址"`
	UserAgent  string    `json:"user_agent" label:"用户代理"`
	ExpiresAt  time.Time `json:"expires_at" binding:"required" label:"过期时间"`
}

// UserSessionResponse 用户会话响应
type UserSessionResponse struct {
	ID         uint64    `json:"id"`
	UserID     uint64    `json:"user_id"`
	TokenID    string    `json:"token_id"`
	DeviceInfo string    `json:"device_info"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	ExpiresAt  time.Time `json:"expires_at"`
	IsExpired  bool      `json:"is_expired"`
	CreatedAt  time.Time `json:"created_at"`
	LastUsedAt time.Time `json:"last_used_at"`
	
	// 关联数据
	User *UserResponse `json:"user,omitempty"`
}

// ToResponse 转换为响应格式
func (us *UserSession) ToResponse() *UserSessionResponse {
	resp := &UserSessionResponse{
		ID:         us.ID,
		UserID:     us.UserID,
		TokenID:    us.TokenID,
		DeviceInfo: us.DeviceInfo,
		IPAddress:  us.IPAddress,
		UserAgent:  us.UserAgent,
		ExpiresAt:  us.ExpiresAt,
		IsExpired:  us.IsExpired(),
		CreatedAt:  us.CreatedAt,
		LastUsedAt: us.LastUsedAt,
	}

	// 关联数据
	if us.User.ID != 0 {
		resp.User = us.User.ToResponse()
	}

	return resp
}

// SessionManager 会话管理器接口
type SessionManager interface {
	// CreateSession 创建会话
	CreateSession(userID uint64, tokenID string, deviceInfo, ipAddress, userAgent string, expiresAt time.Time) (*UserSession, error)
	
	// GetSession 获取会话
	GetSession(tokenID string) (*UserSession, error)
	
	// UpdateSession 更新会话最后使用时间
	UpdateSession(tokenID string) error
	
	// DeleteSession 删除会话
	DeleteSession(tokenID string) error
	
	// DeleteUserSessions 删除用户所有会话
	DeleteUserSessions(userID uint64) error
	
	// CleanExpiredSessions 清理过期会话
	CleanExpiredSessions() error
	
	// GetUserSessions 获取用户会话列表
	GetUserSessions(userID uint64) ([]UserSession, error)
}

// SessionCleanupJob 会话清理任务
type SessionCleanupJob struct {
	db *gorm.DB
}

// NewSessionCleanupJob 创建会话清理任务
func NewSessionCleanupJob(db *gorm.DB) *SessionCleanupJob {
	return &SessionCleanupJob{db: db}
}

// Run 执行清理任务
func (job *SessionCleanupJob) Run() error {
	// 删除过期的会话
	result := job.db.Where("expires_at < ?", time.Now()).Delete(&UserSession{})
	if result.Error != nil {
		return result.Error
	}
	
	// 可以添加日志记录清理的会话数量
	// log.Printf("Cleaned up %d expired sessions", result.RowsAffected)
	
	return nil
}

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	Platform    string `json:"platform"`     // 平台：web, mobile, desktop
	OS          string `json:"os"`           // 操作系统
	Browser     string `json:"browser"`      // 浏览器
	Version     string `json:"version"`      // 版本
	DeviceType  string `json:"device_type"`  // 设备类型：desktop, mobile, tablet
	DeviceName  string `json:"device_name"`  // 设备名称
}

// ParseUserAgent 解析用户代理字符串（简化版本）
func ParseUserAgent(userAgent string) *DeviceInfo {
	// 这里可以使用第三方库如 github.com/mileusna/useragent 来解析
	// 为了简化，这里返回基本信息
	return &DeviceInfo{
		Platform:   "web",
		OS:         "Unknown",
		Browser:    "Unknown",
		Version:    "Unknown",
		DeviceType: "desktop",
		DeviceName: "Unknown Device",
	}
}

// LoginAttempt 登录尝试记录（用于安全监控）
type LoginAttempt struct {
	ID        uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	Username  string    `json:"username" gorm:"type:varchar(100);index;comment:尝试登录的用户名"`
	IPAddress string    `json:"ip_address" gorm:"type:varchar(45);index;comment:IP地址"`
	UserAgent string    `json:"user_agent" gorm:"type:text;comment:用户代理"`
	Success   bool      `json:"success" gorm:"index;comment:是否成功"`
	FailReason string   `json:"fail_reason" gorm:"type:varchar(255);comment:失败原因"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;index"`
}

// TableName 指定表名
func (LoginAttempt) TableName() string {
	return "login_attempts"
}

// LoginAttemptResponse 登录尝试响应
type LoginAttemptResponse struct {
	ID         uint64    `json:"id"`
	Username   string    `json:"username"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	Success    bool      `json:"success"`
	FailReason string    `json:"fail_reason"`
	CreatedAt  time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (la *LoginAttempt) ToResponse() *LoginAttemptResponse {
	return &LoginAttemptResponse{
		ID:         la.ID,
		Username:   la.Username,
		IPAddress:  la.IPAddress,
		UserAgent:  la.UserAgent,
		Success:    la.Success,
		FailReason: la.FailReason,
		CreatedAt:  la.CreatedAt,
	}
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	MaxLoginAttempts    int           `json:"max_login_attempts"`     // 最大登录尝试次数
	LockoutDuration     time.Duration `json:"lockout_duration"`       // 锁定持续时间
	SessionTimeout      time.Duration `json:"session_timeout"`        // 会话超时时间
	MaxConcurrentSessions int         `json:"max_concurrent_sessions"` // 最大并发会话数
}

// DefaultSecurityConfig 默认安全配置
func DefaultSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		MaxLoginAttempts:      5,
		LockoutDuration:       15 * time.Minute,
		SessionTimeout:        24 * time.Hour,
		MaxConcurrentSessions: 5,
	}
}
