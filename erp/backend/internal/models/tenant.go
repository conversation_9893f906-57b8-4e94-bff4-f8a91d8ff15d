package models

import (
	"time"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Tenant 租户模型
type Tenant struct {
	ID           uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	Name         string         `json:"name" gorm:"type:varchar(255);not null;comment:租户名称"`
	Code         string         `json:"code" gorm:"type:varchar(100);uniqueIndex;not null;comment:租户代码"`
	Domain       string         `json:"domain" gorm:"type:varchar(255);comment:租户域名"`
	LogoURL      string         `json:"logo_url" gorm:"type:varchar(500);comment:租户Logo URL"`
	ContactEmail string         `json:"contact_email" gorm:"type:varchar(255);comment:联系邮箱"`
	ContactPhone string         `json:"contact_phone" gorm:"type:varchar(50);comment:联系电话"`
	Address      string         `json:"address" gorm:"type:text;comment:地址"`
	Status       int8           `json:"status" gorm:"type:smallint;default:1;comment:状态：1-正常，2-暂停，3-禁用"`
	MaxUsers     int            `json:"max_users" gorm:"type:integer;default:100;comment:最大用户数限制"`
	ExpiredAt    *time.Time     `json:"expired_at" gorm:"comment:到期时间"`
	Settings     datatypes.JSON `json:"settings" gorm:"type:jsonb;comment:租户配置信息"`
	CreatedAt    time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:软删除时间"`

	// 关联关系
	Users []User `json:"users,omitempty" gorm:"foreignKey:TenantID"`
	Roles []Role `json:"roles,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName 指定表名
func (Tenant) TableName() string {
	return "tenants"
}

// BeforeCreate 创建前钩子
func (t *Tenant) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加创建前的业务逻辑
	// 例如：生成默认配置、验证数据等
	return nil
}

// AfterCreate 创建后钩子
func (t *Tenant) AfterCreate(tx *gorm.DB) error {
	// 创建租户后自动创建默认角色
	defaultRoles := []Role{
		{
			TenantID:    &t.ID,
			Name:        "管理员",
			Code:        "admin",
			Description: "租户管理员，拥有租户内所有权限",
			RoleType:    1, // 租户角色
			IsDefault:   true,
		},
		{
			TenantID:    &t.ID,
			Name:        "普通用户",
			Code:        "user",
			Description: "普通用户，拥有基础权限",
			RoleType:    1, // 租户角色
			IsDefault:   true,
		},
	}

	for _, role := range defaultRoles {
		if err := tx.Create(&role).Error; err != nil {
			return err
		}
	}

	return nil
}

// IsActive 检查租户是否激活
func (t *Tenant) IsActive() bool {
	if t.Status != 1 {
		return false
	}
	if t.ExpiredAt != nil && t.ExpiredAt.Before(time.Now()) {
		return false
	}
	return true
}

// CanAddUser 检查是否可以添加用户
func (t *Tenant) CanAddUser(currentUserCount int) bool {
	return currentUserCount < t.MaxUsers
}

// GetSetting 获取租户配置
func (t *Tenant) GetSetting(key string) interface{} {
	if t.Settings == nil {
		return nil
	}
	
	var settings map[string]interface{}
	if err := t.Settings.Unmarshal(&settings); err != nil {
		return nil
	}
	
	return settings[key]
}

// SetSetting 设置租户配置
func (t *Tenant) SetSetting(key string, value interface{}) error {
	var settings map[string]interface{}
	if t.Settings != nil {
		if err := t.Settings.Unmarshal(&settings); err != nil {
			return err
		}
	} else {
		settings = make(map[string]interface{})
	}
	
	settings[key] = value
	
	settingsJSON, err := datatypes.NewJSON(settings)
	if err != nil {
		return err
	}
	
	t.Settings = settingsJSON
	return nil
}

// TenantStatus 租户状态常量
const (
	TenantStatusActive    = 1 // 正常
	TenantStatusSuspended = 2 // 暂停
	TenantStatusDisabled  = 3 // 禁用
)

// TenantCreateRequest 创建租户请求
type TenantCreateRequest struct {
	Name         string                 `json:"name" binding:"required,min=2,max=255" label:"租户名称"`
	Code         string                 `json:"code" binding:"required,min=2,max=100,alphanum" label:"租户代码"`
	Domain       string                 `json:"domain" binding:"omitempty,fqdn" label:"租户域名"`
	ContactEmail string                 `json:"contact_email" binding:"omitempty,email" label:"联系邮箱"`
	ContactPhone string                 `json:"contact_phone" binding:"omitempty,min=10,max=20" label:"联系电话"`
	Address      string                 `json:"address" binding:"omitempty,max=500" label:"地址"`
	MaxUsers     int                    `json:"max_users" binding:"omitempty,min=1,max=10000" label:"最大用户数"`
	ExpiredAt    *time.Time             `json:"expired_at" label:"到期时间"`
	Settings     map[string]interface{} `json:"settings" label:"租户配置"`
}

// TenantUpdateRequest 更新租户请求
type TenantUpdateRequest struct {
	Name         string                 `json:"name" binding:"omitempty,min=2,max=255" label:"租户名称"`
	Domain       string                 `json:"domain" binding:"omitempty,fqdn" label:"租户域名"`
	LogoURL      string                 `json:"logo_url" binding:"omitempty,url" label:"Logo URL"`
	ContactEmail string                 `json:"contact_email" binding:"omitempty,email" label:"联系邮箱"`
	ContactPhone string                 `json:"contact_phone" binding:"omitempty,min=10,max=20" label:"联系电话"`
	Address      string                 `json:"address" binding:"omitempty,max=500" label:"地址"`
	Status       int8                   `json:"status" binding:"omitempty,oneof=1 2 3" label:"状态"`
	MaxUsers     int                    `json:"max_users" binding:"omitempty,min=1,max=10000" label:"最大用户数"`
	ExpiredAt    *time.Time             `json:"expired_at" label:"到期时间"`
	Settings     map[string]interface{} `json:"settings" label:"租户配置"`
}

// TenantResponse 租户响应
type TenantResponse struct {
	ID           uint64                 `json:"id"`
	Name         string                 `json:"name"`
	Code         string                 `json:"code"`
	Domain       string                 `json:"domain"`
	LogoURL      string                 `json:"logo_url"`
	ContactEmail string                 `json:"contact_email"`
	ContactPhone string                 `json:"contact_phone"`
	Address      string                 `json:"address"`
	Status       int8                   `json:"status"`
	StatusText   string                 `json:"status_text"`
	MaxUsers     int                    `json:"max_users"`
	UserCount    int                    `json:"user_count"`
	ExpiredAt    *time.Time             `json:"expired_at"`
	IsExpired    bool                   `json:"is_expired"`
	Settings     map[string]interface{} `json:"settings"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (t *Tenant) ToResponse() *TenantResponse {
	resp := &TenantResponse{
		ID:           t.ID,
		Name:         t.Name,
		Code:         t.Code,
		Domain:       t.Domain,
		LogoURL:      t.LogoURL,
		ContactEmail: t.ContactEmail,
		ContactPhone: t.ContactPhone,
		Address:      t.Address,
		Status:       t.Status,
		MaxUsers:     t.MaxUsers,
		ExpiredAt:    t.ExpiredAt,
		CreatedAt:    t.CreatedAt,
		UpdatedAt:    t.UpdatedAt,
	}

	// 状态文本
	switch t.Status {
	case TenantStatusActive:
		resp.StatusText = "正常"
	case TenantStatusSuspended:
		resp.StatusText = "暂停"
	case TenantStatusDisabled:
		resp.StatusText = "禁用"
	default:
		resp.StatusText = "未知"
	}

	// 是否过期
	resp.IsExpired = t.ExpiredAt != nil && t.ExpiredAt.Before(time.Now())

	// 设置信息
	if t.Settings != nil {
		var settings map[string]interface{}
		if err := t.Settings.Unmarshal(&settings); err == nil {
			resp.Settings = settings
		}
	}

	return resp
}
