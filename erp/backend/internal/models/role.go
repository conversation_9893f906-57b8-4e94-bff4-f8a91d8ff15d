package models

import (
	"time"
	"gorm.io/gorm"
)

// Role 角色模型
type Role struct {
	ID          uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID    *uint64        `json:"tenant_id" gorm:"index;comment:租户ID，系统角色为NULL"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null;comment:角色名称"`
	Code        string         `json:"code" gorm:"type:varchar(100);not null;comment:角色代码"`
	Description string         `json:"description" gorm:"type:text;comment:角色描述"`
	RoleType    int8           `json:"role_type" gorm:"type:smallint;default:1;index;comment:角色类型：1-租户角色，2-系统角色"`
	IsDefault   bool           `json:"is_default" gorm:"default:false;comment:是否为默认角色"`
	Status      int8           `json:"status" gorm:"type:smallint;default:1;index;comment:状态：1-正常，2-禁用"`
	CreatedAt   time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:软删除时间"`

	// 关联关系
	Tenant          *Tenant          `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	UserRoles       []UserRole       `json:"user_roles,omitempty" gorm:"foreignKey:RoleID"`
	Users           []User           `json:"users,omitempty" gorm:"many2many:user_roles;"`
	RolePermissions []RolePermission `json:"role_permissions,omitempty" gorm:"foreignKey:RoleID"`
	Permissions     []Permission     `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

// BeforeCreate 创建前钩子
func (r *Role) BeforeCreate(tx *gorm.DB) error {
	// 确保同一租户内角色代码唯一
	var count int64
	query := tx.Model(&Role{}).Where("code = ? AND deleted_at IS NULL", r.Code)
	if r.TenantID != nil {
		query = query.Where("tenant_id = ?", *r.TenantID)
	} else {
		query = query.Where("tenant_id IS NULL")
	}
	
	if err := query.Count(&count).Error; err != nil {
		return err
	}
	
	if count > 0 {
		return gorm.ErrDuplicatedKey
	}
	
	return nil
}

// IsActive 检查角色是否激活
func (r *Role) IsActive() bool {
	return r.Status == RoleStatusActive
}

// IsSystemRole 检查是否为系统角色
func (r *Role) IsSystemRole() bool {
	return r.RoleType == RoleTypeSystem
}

// IsTenantRole 检查是否为租户角色
func (r *Role) IsTenantRole() bool {
	return r.RoleType == RoleTypeTenant && r.TenantID != nil
}

// 角色类型常量
const (
	RoleTypeTenant = 1 // 租户角色
	RoleTypeSystem = 2 // 系统角色
)

// 角色状态常量
const (
	RoleStatusActive   = 1 // 正常
	RoleStatusDisabled = 2 // 禁用
)

// RoleCreateRequest 创建角色请求
type RoleCreateRequest struct {
	TenantID      *uint64  `json:"tenant_id" label:"租户ID"`
	Name          string   `json:"name" binding:"required,min=2,max=100" label:"角色名称"`
	Code          string   `json:"code" binding:"required,min=2,max=100,alphanum" label:"角色代码"`
	Description   string   `json:"description" binding:"omitempty,max=500" label:"角色描述"`
	RoleType      int8     `json:"role_type" binding:"omitempty,oneof=1 2" label:"角色类型"`
	PermissionIDs []uint64 `json:"permission_ids" label:"权限ID列表"`
}

// RoleUpdateRequest 更新角色请求
type RoleUpdateRequest struct {
	Name          string   `json:"name" binding:"omitempty,min=2,max=100" label:"角色名称"`
	Description   string   `json:"description" binding:"omitempty,max=500" label:"角色描述"`
	Status        int8     `json:"status" binding:"omitempty,oneof=1 2" label:"状态"`
	PermissionIDs []uint64 `json:"permission_ids" label:"权限ID列表"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID           uint64                `json:"id"`
	TenantID     *uint64               `json:"tenant_id"`
	Name         string                `json:"name"`
	Code         string                `json:"code"`
	Description  string                `json:"description"`
	RoleType     int8                  `json:"role_type"`
	RoleTypeText string                `json:"role_type_text"`
	IsDefault    bool                  `json:"is_default"`
	Status       int8                  `json:"status"`
	StatusText   string                `json:"status_text"`
	UserCount    int                   `json:"user_count"`
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
	
	// 关联数据
	Tenant      *TenantResponse      `json:"tenant,omitempty"`
	Permissions []PermissionResponse `json:"permissions,omitempty"`
}

// ToResponse 转换为响应格式
func (r *Role) ToResponse() *RoleResponse {
	resp := &RoleResponse{
		ID:          r.ID,
		TenantID:    r.TenantID,
		Name:        r.Name,
		Code:        r.Code,
		Description: r.Description,
		RoleType:    r.RoleType,
		IsDefault:   r.IsDefault,
		Status:      r.Status,
		CreatedAt:   r.CreatedAt,
		UpdatedAt:   r.UpdatedAt,
	}

	// 角色类型文本
	switch r.RoleType {
	case RoleTypeTenant:
		resp.RoleTypeText = "租户角色"
	case RoleTypeSystem:
		resp.RoleTypeText = "系统角色"
	default:
		resp.RoleTypeText = "未知"
	}

	// 状态文本
	switch r.Status {
	case RoleStatusActive:
		resp.StatusText = "正常"
	case RoleStatusDisabled:
		resp.StatusText = "禁用"
	default:
		resp.StatusText = "未知"
	}

	// 用户数量
	resp.UserCount = len(r.Users)

	// 关联数据
	if r.Tenant != nil {
		resp.Tenant = r.Tenant.ToResponse()
	}

	if len(r.Permissions) > 0 {
		resp.Permissions = make([]PermissionResponse, len(r.Permissions))
		for i, permission := range r.Permissions {
			resp.Permissions[i] = *permission.ToResponse()
		}
	}

	return resp
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID         uint64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID     uint64     `json:"user_id" gorm:"not null;index"`
	RoleID     uint64     `json:"role_id" gorm:"not null;index"`
	AssignedBy *uint64    `json:"assigned_by" gorm:"comment:分配者用户ID"`
	AssignedAt time.Time  `json:"assigned_at" gorm:"autoCreateTime"`
	ExpiresAt  *time.Time `json:"expires_at" gorm:"comment:过期时间"`

	// 关联关系
	User       User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role       Role  `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	AssignedByUser *User `json:"assigned_by_user,omitempty" gorm:"foreignKey:AssignedBy"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_roles"
}

// IsExpired 检查是否过期
func (ur *UserRole) IsExpired() bool {
	return ur.ExpiresAt != nil && ur.ExpiresAt.Before(time.Now())
}

// UserRoleCreateRequest 分配角色请求
type UserRoleCreateRequest struct {
	UserID    uint64     `json:"user_id" binding:"required" label:"用户ID"`
	RoleID    uint64     `json:"role_id" binding:"required" label:"角色ID"`
	ExpiresAt *time.Time `json:"expires_at" label:"过期时间"`
}

// UserRoleResponse 用户角色响应
type UserRoleResponse struct {
	ID         uint64     `json:"id"`
	UserID     uint64     `json:"user_id"`
	RoleID     uint64     `json:"role_id"`
	AssignedBy *uint64    `json:"assigned_by"`
	AssignedAt time.Time  `json:"assigned_at"`
	ExpiresAt  *time.Time `json:"expires_at"`
	IsExpired  bool       `json:"is_expired"`
	
	// 关联数据
	User           *UserResponse `json:"user,omitempty"`
	Role           *RoleResponse `json:"role,omitempty"`
	AssignedByUser *UserResponse `json:"assigned_by_user,omitempty"`
}

// ToResponse 转换为响应格式
func (ur *UserRole) ToResponse() *UserRoleResponse {
	resp := &UserRoleResponse{
		ID:         ur.ID,
		UserID:     ur.UserID,
		RoleID:     ur.RoleID,
		AssignedBy: ur.AssignedBy,
		AssignedAt: ur.AssignedAt,
		ExpiresAt:  ur.ExpiresAt,
		IsExpired:  ur.IsExpired(),
	}

	// 关联数据
	if ur.User.ID != 0 {
		resp.User = ur.User.ToResponse()
	}
	if ur.Role.ID != 0 {
		resp.Role = ur.Role.ToResponse()
	}
	if ur.AssignedByUser != nil && ur.AssignedByUser.ID != 0 {
		resp.AssignedByUser = ur.AssignedByUser.ToResponse()
	}

	return resp
}
