package middleware

import (
	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TenantMiddleware 多租户数据隔离中间件
func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户信息
		claims, exists := GetCurrentUser(c)
		if !exists {
			// 如果没有用户信息，跳过租户检查
			c.Next()
			return
		}

		// 系统管理员可以访问所有数据，不需要租户隔离
		if claims.IsSystemAdmin() {
			c.Set("is_system_admin", true)
			c.Next()
			return
		}

		// 租户用户必须有租户ID
		if claims.TenantID == nil {
			utils.AbortWithMessage(c, utils.CodeForbidden, "租户用户必须属于一个租户")
			return
		}

		// 设置租户上下文
		c.Set("current_tenant_id", *claims.TenantID)
		c.Set("is_tenant_user", true)
		c.Next()
	}
}

// GetCurrentTenantIDFromContext 从上下文获取当前租户ID
func GetCurrentTenantIDFromContext(c *gin.Context) (*uint64, bool) {
	if tenantID, exists := c.Get("current_tenant_id"); exists {
		if id, ok := tenantID.(uint64); ok {
			return &id, true
		}
	}
	return nil, false
}

// IsSystemAdminFromContext 从上下文检查是否为系统管理员
func IsSystemAdminFromContext(c *gin.Context) bool {
	if isAdmin, exists := c.Get("is_system_admin"); exists {
		if admin, ok := isAdmin.(bool); ok {
			return admin
		}
	}
	return false
}

// IsTenantUserFromContext 从上下文检查是否为租户用户
func IsTenantUserFromContext(c *gin.Context) bool {
	if isTenant, exists := c.Get("is_tenant_user"); exists {
		if tenant, ok := isTenant.(bool); ok {
			return tenant
		}
	}
	return false
}

// ApplyTenantScope 应用租户数据范围
func ApplyTenantScope(c *gin.Context, db *gorm.DB) *gorm.DB {
	// 如果是系统管理员，返回原始DB（可以访问所有数据）
	if IsSystemAdminFromContext(c) {
		return db
	}

	// 如果是租户用户，添加租户ID过滤
	if tenantID, exists := GetCurrentTenantIDFromContext(c); exists {
		return db.Where("tenant_id = ?", *tenantID)
	}

	// 如果没有租户信息，返回空结果（安全起见）
	return db.Where("1 = 0")
}

// ApplyTenantScopeForTable 为指定表应用租户数据范围
func ApplyTenantScopeForTable(c *gin.Context, db *gorm.DB, tableName string) *gorm.DB {
	// 如果是系统管理员，返回原始DB
	if IsSystemAdminFromContext(c) {
		return db
	}

	// 如果是租户用户，添加租户ID过滤
	if tenantID, exists := GetCurrentTenantIDFromContext(c); exists {
		return db.Where(tableName+".tenant_id = ?", *tenantID)
	}

	// 如果没有租户信息，返回空结果
	return db.Where("1 = 0")
}

// ValidateTenantAccess 验证租户访问权限
func ValidateTenantAccess(c *gin.Context, targetTenantID uint64) bool {
	// 系统管理员可以访问所有租户
	if IsSystemAdminFromContext(c) {
		return true
	}

	// 租户用户只能访问自己的租户数据
	if currentTenantID, exists := GetCurrentTenantIDFromContext(c); exists {
		return *currentTenantID == targetTenantID
	}

	return false
}

// RequireTenantAccess 要求租户访问权限的中间件
func RequireTenantAccess(getTenantID func(*gin.Context) (uint64, error)) gin.HandlerFunc {
	return func(c *gin.Context) {
		targetTenantID, err := getTenantID(c)
		if err != nil {
			utils.AbortWithMessage(c, utils.CodeBadRequest, "无法获取租户ID: "+err.Error())
			return
		}

		if !ValidateTenantAccess(c, targetTenantID) {
			utils.AbortWithError(c, utils.CodePermissionDenied)
			return
		}

		c.Next()
	}
}

// SetTenantForCreate 为创建操作设置租户ID
func SetTenantForCreate(c *gin.Context, model interface{}) error {
	// 系统管理员创建的资源可能不需要租户ID（如系统级角色、权限等）
	if IsSystemAdminFromContext(c) {
		return nil
	}

	// 租户用户创建的资源必须属于当前租户
	if tenantID, exists := GetCurrentTenantIDFromContext(c); exists {
		// 使用反射设置租户ID
		if err := setTenantIDField(model, *tenantID); err != nil {
			return err
		}
	}

	return nil
}

// setTenantIDField 使用反射设置模型的租户ID字段
func setTenantIDField(model interface{}, tenantID uint64) error {
	// 这里可以使用反射来设置TenantID字段
	// 为了简化，我们假设所有模型都有SetTenantID方法
	if setter, ok := model.(interface{ SetTenantID(uint64) }); ok {
		setter.SetTenantID(tenantID)
		return nil
	}
	return nil
}

// TenantScopeDB 租户范围数据库查询辅助函数
type TenantScopeDB struct {
	db *gorm.DB
	c  *gin.Context
}

// NewTenantScopeDB 创建租户范围数据库查询
func NewTenantScopeDB(c *gin.Context, db *gorm.DB) *TenantScopeDB {
	return &TenantScopeDB{
		db: db,
		c:  c,
	}
}

// DB 获取应用了租户范围的数据库连接
func (t *TenantScopeDB) DB() *gorm.DB {
	return ApplyTenantScope(t.c, t.db)
}

// Table 获取应用了租户范围的指定表查询
func (t *TenantScopeDB) Table(tableName string) *gorm.DB {
	return ApplyTenantScopeForTable(t.c, t.db, tableName)
}

// Model 获取应用了租户范围的模型查询
func (t *TenantScopeDB) Model(model interface{}) *gorm.DB {
	return ApplyTenantScope(t.c, t.db).Model(model)
}

// Create 创建记录（自动设置租户ID）
func (t *TenantScopeDB) Create(model interface{}) *gorm.DB {
	if err := SetTenantForCreate(t.c, model); err != nil {
		db := t.db.Session(&gorm.Session{})
		db.AddError(err)
		return db
	}
	return t.DB().Create(model)
}

// Find 查找记录（应用租户范围）
func (t *TenantScopeDB) Find(dest interface{}, conds ...interface{}) *gorm.DB {
	return t.DB().Find(dest, conds...)
}

// First 查找第一条记录（应用租户范围）
func (t *TenantScopeDB) First(dest interface{}, conds ...interface{}) *gorm.DB {
	return t.DB().First(dest, conds...)
}

// Where 添加查询条件（应用租户范围）
func (t *TenantScopeDB) Where(query interface{}, args ...interface{}) *gorm.DB {
	return t.DB().Where(query, args...)
}

// Update 更新记录（应用租户范围）
func (t *TenantScopeDB) Update(column string, value interface{}) *gorm.DB {
	return t.DB().Update(column, value)
}

// Updates 批量更新记录（应用租户范围）
func (t *TenantScopeDB) Updates(values interface{}) *gorm.DB {
	return t.DB().Updates(values)
}

// Delete 删除记录（应用租户范围）
func (t *TenantScopeDB) Delete(value interface{}, conds ...interface{}) *gorm.DB {
	return t.DB().Delete(value, conds...)
}

// Count 统计记录数（应用租户范围）
func (t *TenantScopeDB) Count(count *int64) *gorm.DB {
	return t.DB().Count(count)
}

// Scopes 应用作用域（应用租户范围）
func (t *TenantScopeDB) Scopes(funcs ...func(*gorm.DB) *gorm.DB) *gorm.DB {
	return t.DB().Scopes(funcs...)
}
