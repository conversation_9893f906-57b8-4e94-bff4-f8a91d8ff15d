package middleware

import (
	"fmt"
	"strings"
	"time"

	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			utils.AbortWithError(c, utils.CodeUnauthorized)
			return
		}

		// 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			utils.AbortWithMessage(c, utils.CodeUnauthorized, "Authorization头格式错误")
			return
		}

		// 提取token
		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			utils.AbortWithMessage(c, utils.CodeTokenInvalid, "Token不能为空")
			return
		}

		// 验证token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			utils.AbortWithMessage(c, utils.CodeTokenInvalid, "Token无效: "+err.Error())
			return
		}

		// 检查token是否在黑名单中
		if blacklist != nil {
			tokenID, err := jwtManager.GetTokenID(tokenString)
			if err == nil {
				isBlacklisted, err := blacklist.IsBlacklisted(tokenID)
				if err == nil && isBlacklisted {
					utils.AbortWithMessage(c, utils.CodeTokenInvalid, "Token已失效")
					return
				}
			}
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("tenant_id", claims.TenantID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("roles", claims.Roles)
		c.Set("claims", claims)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware(jwtManager *utils.JWTManager, blacklist utils.TokenBlacklist) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			c.Next()
			return
		}

		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			c.Next()
			return
		}

		// 尝试验证token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// 检查黑名单
		if blacklist != nil {
			tokenID, err := jwtManager.GetTokenID(tokenString)
			if err == nil {
				isBlacklisted, err := blacklist.IsBlacklisted(tokenID)
				if err == nil && isBlacklisted {
					c.Next()
					return
				}
			}
		}

		// 存储用户信息
		c.Set("user_id", claims.UserID)
		c.Set("tenant_id", claims.TenantID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("roles", claims.Roles)
		c.Set("claims", claims)

		c.Next()
	}
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) (*utils.JWTClaims, bool) {
	if claims, exists := c.Get("claims"); exists {
		if userClaims, ok := claims.(*utils.JWTClaims); ok {
			return userClaims, true
		}
	}
	return nil, false
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(c *gin.Context) (uint64, bool) {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint64); ok {
			return id, true
		}
	}
	return 0, false
}

// GetCurrentTenantID 获取当前租户ID
func GetCurrentTenantID(c *gin.Context) (*uint64, bool) {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(*uint64); ok {
			return id, true
		}
	}
	return nil, false
}

// GetCurrentUsername 获取当前用户名
func GetCurrentUsername(c *gin.Context) (string, bool) {
	if username, exists := c.Get("username"); exists {
		if name, ok := username.(string); ok {
			return name, true
		}
	}
	return "", false
}

// GetCurrentUserType 获取当前用户类型
func GetCurrentUserType(c *gin.Context) (int8, bool) {
	if userType, exists := c.Get("user_type"); exists {
		if t, ok := userType.(int8); ok {
			return t, true
		}
	}
	return 0, false
}

// GetCurrentUserRoles 获取当前用户角色
func GetCurrentUserRoles(c *gin.Context) ([]string, bool) {
	if roles, exists := c.Get("roles"); exists {
		if r, ok := roles.([]string); ok {
			return r, true
		}
	}
	return nil, false
}

// IsSystemAdmin 检查当前用户是否为系统管理员
func IsSystemAdmin(c *gin.Context) bool {
	claims, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return claims.IsSystemAdmin()
}

// IsTenantUser 检查当前用户是否为租户用户
func IsTenantUser(c *gin.Context) bool {
	claims, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return claims.IsTenantUser()
}

// HasRole 检查当前用户是否拥有指定角色
func HasRole(c *gin.Context, role string) bool {
	claims, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return claims.HasRole(role)
}

// HasAnyRole 检查当前用户是否拥有任意一个指定角色
func HasAnyRole(c *gin.Context, roles []string) bool {
	claims, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return claims.HasAnyRole(roles)
}

// RequireSystemAdmin 要求系统管理员权限的中间件
func RequireSystemAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsSystemAdmin(c) {
			utils.AbortWithError(c, utils.CodePermissionDenied)
			return
		}
		c.Next()
	}
}

// RequireTenantUser 要求租户用户权限的中间件
func RequireTenantUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsTenantUser(c) {
			utils.AbortWithError(c, utils.CodePermissionDenied)
			return
		}
		c.Next()
	}
}

// RequireRole 要求指定角色的中间件
func RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !HasRole(c, role) {
			utils.AbortWithError(c, utils.CodePermissionDenied)
			return
		}
		c.Next()
	}
}

// RequireAnyRole 要求任意一个指定角色的中间件
func RequireAnyRole(roles []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !HasAnyRole(c, roles) {
			utils.AbortWithError(c, utils.CodePermissionDenied)
			return
		}
		c.Next()
	}
}

// RequirePermission 要求指定权限的中间件
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现权限检查逻辑
		// 这里需要查询数据库，检查用户是否拥有指定权限
		// 暂时先检查角色
		c.Next()
	}
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	}
}

// RateLimiter 限流中间件（简单实现）
func RateLimiter() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以实现更复杂的限流逻辑
		// 目前只是一个占位符
		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("%d-%s", time.Now().UnixNano(), uuid.New().String()[:8])
}
