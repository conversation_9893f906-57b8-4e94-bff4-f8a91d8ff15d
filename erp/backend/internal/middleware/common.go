package middleware

import (
	"context"
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"erp/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware CORS中间件
func CORSMiddleware(allowedOrigins, allowedMethods, allowedHeaders []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 检查是否允许该来源
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		}

		c.<PERSON><PERSON>("Access-Control-Allow-Methods", strings.Join(allowedMethods, ", "))
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", strings.Join(allowedHeaders, ", "))
		c.<PERSON>("Access-Control-Allow-Credentials", "true")
		c<PERSON><PERSON>("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// LoggerMiddleware 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.ErrorMessage,
		)
	})
}

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 记录panic信息
		fmt.Printf("Panic recovered: %v\n", recovered)
		fmt.Printf("Stack trace:\n%s\n", debug.Stack())

		// 返回500错误
		utils.AbortWithMessage(c, utils.CodeInternalServerError, "服务器内部错误")
	})
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = utils.GenerateUUID()
		}

		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(requests int, window time.Duration) gin.HandlerFunc {
	// 简单的内存限流实现
	// 生产环境建议使用Redis实现分布式限流
	limiter := make(map[string][]time.Time)

	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// 清理过期记录
		if times, exists := limiter[clientIP]; exists {
			var validTimes []time.Time
			for _, t := range times {
				if now.Sub(t) < window {
					validTimes = append(validTimes, t)
				}
			}
			limiter[clientIP] = validTimes
		}

		// 检查是否超过限制
		if len(limiter[clientIP]) >= requests {
			utils.AbortWithError(c, utils.CodeTooManyRequests)
			return
		}

		// 记录当前请求时间
		limiter[clientIP] = append(limiter[clientIP], now)
		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止XSS攻击
		c.Header("X-XSS-Protection", "1; mode=block")

		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")

		// 防止点击劫持
		c.Header("X-Frame-Options", "DENY")

		// 强制HTTPS（生产环境）
		if gin.Mode() == gin.ReleaseMode {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		// 内容安全策略
		c.Header("Content-Security-Policy", "default-src 'self'")

		c.Next()
	}
}

// ValidateContentTypeMiddleware 验证Content-Type中间件
func ValidateContentTypeMiddleware(allowedTypes []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对POST、PUT、PATCH请求验证Content-Type
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")

			// 检查Content-Type是否被允许
			allowed := false
			for _, allowedType := range allowedTypes {
				if strings.Contains(contentType, allowedType) {
					allowed = true
					break
				}
			}

			if !allowed {
				utils.AbortWithMessage(c, utils.CodeBadRequest, "不支持的Content-Type")
				return
			}
		}

		c.Next()
	}
}

// TimeoutMiddleware 超时中间件
func TimeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置请求超时
		ctx := c.Request.Context()
		ctx, cancel := context.WithTimeout(ctx, timeout)
		defer cancel()

		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware(path string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == path {
			c.JSON(http.StatusOK, gin.H{
				"status":    "ok",
				"timestamp": time.Now().Unix(),
				"version":   "1.0.0",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		// 记录请求指标
		duration := time.Since(start)
		method := c.Request.Method
		path := c.Request.URL.Path
		status := c.Writer.Status()

		// 这里可以将指标发送到监控系统
		fmt.Printf("Metrics: %s %s %d %v\n", method, path, status, duration)
	}
}

// DatabaseMiddleware 数据库中间件
func DatabaseMiddleware(db interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	}
}

// GetDB 从上下文获取数据库连接
func GetDB(c *gin.Context) (interface{}, bool) {
	if db, exists := c.Get("db"); exists {
		return db, true
	}
	return nil, false
}

// ErrorHandlerMiddleware 错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理请求过程中的错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 根据错误类型返回相应的响应
			switch err.Type {
			case gin.ErrorTypeBind:
				utils.ValidationError(c, err.Err)
			case gin.ErrorTypePublic:
				utils.BadRequest(c, err.Error())
			default:
				utils.InternalServerError(c)
			}
		}
	}
}
