package database

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

// SupabaseAdapter Supabase REST API适配器
type SupabaseAdapter struct {
	baseURL    string
	serviceKey string
	client     *http.Client
}

// NewSupabaseAdapter 创建Supabase适配器
func NewSupabaseAdapter() *SupabaseAdapter {
	return &SupabaseAdapter{
		baseURL:    os.Getenv("SUPABASE_URL"),
		serviceKey: os.Getenv("SUPABASE_SERVICE_KEY"),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TestConnection 测试Supabase连接
func (s *SupabaseAdapter) TestConnection() error {
	if s.baseURL == "" || s.serviceKey == "" {
		return fmt.Errorf("Supabase配置不完整")
	}

	// 测试API连接
	req, err := http.NewRequest("GET", s.baseURL+"/rest/v1/", nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("apikey", s.serviceKey)
	req.Header.Set("Authorization", "Bearer "+s.serviceKey)

	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("API连接失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API响应错误: %d", resp.StatusCode)
	}

	return nil
}

// ExecuteSQL 执行SQL查询
func (s *SupabaseAdapter) ExecuteSQL(query string) ([]map[string]interface{}, error) {
	url := s.baseURL + "/rest/v1/rpc/exec_sql"
	
	payload := map[string]string{
		"sql": query,
	}
	
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("JSON编码失败: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.serviceKey)
	req.Header.Set("Authorization", "Bearer "+s.serviceKey)

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("SQL执行失败 (%d): %s", resp.StatusCode, string(body))
	}

	var result []map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return result, nil
}

// GetTable 获取表数据
func (s *SupabaseAdapter) GetTable(tableName string, conditions map[string]interface{}) ([]map[string]interface{}, error) {
	url := s.baseURL + "/rest/v1/" + tableName

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 添加查询条件
	q := req.URL.Query()
	for key, value := range conditions {
		q.Add(key, fmt.Sprintf("eq.%v", value))
	}
	req.URL.RawQuery = q.Encode()

	req.Header.Set("apikey", s.serviceKey)
	req.Header.Set("Authorization", "Bearer "+s.serviceKey)

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("查询失败 (%d): %s", resp.StatusCode, string(body))
	}

	var result []map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return result, nil
}

// InsertTable 插入表数据
func (s *SupabaseAdapter) InsertTable(tableName string, data map[string]interface{}) (map[string]interface{}, error) {
	url := s.baseURL + "/rest/v1/" + tableName

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("JSON编码失败: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.serviceKey)
	req.Header.Set("Authorization", "Bearer "+s.serviceKey)
	req.Header.Set("Prefer", "return=representation")

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("插入失败 (%d): %s", resp.StatusCode, string(body))
	}

	var result []map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("插入成功但未返回数据")
	}

	return result[0], nil
}

// InitializeWithAdapter 使用适配器初始化数据库（仅用于测试连接）
func InitializeWithAdapter() (*SupabaseAdapter, error) {
	adapter := NewSupabaseAdapter()
	
	if err := adapter.TestConnection(); err != nil {
		return nil, fmt.Errorf("Supabase适配器连接失败: %w", err)
	}

	return adapter, nil
}

// FallbackMode 回退模式标志
var FallbackMode = false

// SetFallbackMode 设置回退模式
func SetFallbackMode(enabled bool) {
	FallbackMode = enabled
}

// IsFallbackMode 检查是否为回退模式
func IsFallbackMode() bool {
	return FallbackMode
}

// MockGormDB 创建一个模拟的GORM数据库连接（用于回退模式）
func MockGormDB() *gorm.DB {
	// 这里返回一个空的GORM实例，实际操作会通过适配器进行
	// 注意：这是一个临时解决方案，生产环境中应该实现完整的适配器
	return &gorm.DB{}
}
