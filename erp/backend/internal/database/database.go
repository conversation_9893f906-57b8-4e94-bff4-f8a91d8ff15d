package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"erp/backend/internal/config"
	"erp/backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDB 初始化数据库连接
func InitDB(cfg *config.Config) (*gorm.DB, error) {
	var dsn string

	// 检查是否使用Supabase
	if cfg.Supabase.Enabled {
		// 使用Supabase数据库连接
		supabaseDBURL := os.Getenv("SUPABASE_DB_URL")
		if supabaseDBURL == "" {
			return nil, fmt.Errorf("启用Supabase时必须设置SUPABASE_DB_URL环境变量")
		}
		dsn = supabaseDBURL
		log.Println("使用Supabase数据库连接")
	} else {
		// 使用传统PostgreSQL连接
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
			cfg.Database.Host,
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.DBName,
			cfg.Database.Port,
			cfg.Database.SSLMode,
			cfg.Database.TimeZone,
		)
		log.Println("使用传统PostgreSQL数据库连接")
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(getLogLevel(cfg.Database.LogLevel)),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		// 如果是Supabase模式且连接失败，尝试使用REST API适配器
		if cfg.Supabase.Enabled {
			log.Printf("PostgreSQL连接失败，尝试使用Supabase REST API适配器: %v", err)

			_, adapterErr := InitializeWithAdapter()
			if adapterErr != nil {
				return nil, fmt.Errorf("数据库连接和适配器都失败 - PostgreSQL: %w, Adapter: %v", err, adapterErr)
			}

			log.Println("✅ Supabase REST API适配器连接成功，启用回退模式")
			SetFallbackMode(true)

			// 返回一个模拟的GORM连接，实际操作通过适配器进行
			// 注意：这是临时解决方案，生产环境需要完整的ORM适配器
			return MockGormDB(), nil
		}
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.Database.ConnMaxLifetime) * time.Minute)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	log.Println("数据库连接成功")
	return db, nil
}

// RunMigrations 运行数据库迁移
func RunMigrations(db *gorm.DB) error {
	// 检查是否为回退模式
	if IsFallbackMode() {
		log.Println("✅ 回退模式：跳过GORM迁移，表结构已通过Supabase API创建")
		return nil
	}

	log.Println("开始数据库迁移...")

	// 自动迁移所有模型
	err := db.AutoMigrate(
		&models.Tenant{},
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)

	if err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	// 创建默认数据
	if err := createDefaultData(db); err != nil {
		return fmt.Errorf("创建默认数据失败: %w", err)
	}

	log.Println("数据库迁移完成")
	return nil
}

// createDefaultData 创建默认数据
func createDefaultData(db *gorm.DB) error {
	// 创建默认系统权限
	if err := createDefaultPermissions(db); err != nil {
		return err
	}

	// 创建默认系统角色
	if err := createDefaultRoles(db); err != nil {
		return err
	}

	// 创建默认系统管理员用户
	if err := createDefaultAdmin(db); err != nil {
		return err
	}

	return nil
}

// createDefaultPermissions 创建默认权限
func createDefaultPermissions(db *gorm.DB) error {
	permissions := []models.Permission{
		// 用户管理权限
		{Resource: "user", Action: "create", Name: "创建用户", Description: "创建新用户"},
		{Resource: "user", Action: "read", Name: "查看用户", Description: "查看用户信息"},
		{Resource: "user", Action: "update", Name: "更新用户", Description: "更新用户信息"},
		{Resource: "user", Action: "delete", Name: "删除用户", Description: "删除用户"},

		// 角色管理权限
		{Resource: "role", Action: "create", Name: "创建角色", Description: "创建新角色"},
		{Resource: "role", Action: "read", Name: "查看角色", Description: "查看角色信息"},
		{Resource: "role", Action: "update", Name: "更新角色", Description: "更新角色信息"},
		{Resource: "role", Action: "delete", Name: "删除角色", Description: "删除角色"},

		// 权限管理权限
		{Resource: "permission", Action: "read", Name: "查看权限", Description: "查看权限信息"},
		{Resource: "permission", Action: "assign", Name: "分配权限", Description: "分配权限给角色"},

		// 租户管理权限（系统管理员专用）
		{Resource: "tenant", Action: "create", Name: "创建租户", Description: "创建新租户"},
		{Resource: "tenant", Action: "read", Name: "查看租户", Description: "查看租户信息"},
		{Resource: "tenant", Action: "update", Name: "更新租户", Description: "更新租户信息"},
		{Resource: "tenant", Action: "delete", Name: "删除租户", Description: "删除租户"},
		{Resource: "tenant", Action: "manage", Name: "管理租户", Description: "管理租户状态和配置"},
	}

	for _, permission := range permissions {
		var existingPermission models.Permission
		result := db.Where("resource = ? AND action = ?", permission.Resource, permission.Action).First(&existingPermission)
		if result.Error == gorm.ErrRecordNotFound {
			if err := db.Create(&permission).Error; err != nil {
				return fmt.Errorf("创建权限失败: %w", err)
			}
		}
	}

	return nil
}

// createDefaultRoles 创建默认角色
func createDefaultRoles(db *gorm.DB) error {
	// 创建系统管理员角色
	var adminRole models.Role
	result := db.Where("code = ?", "system_admin").First(&adminRole)
	if result.Error == gorm.ErrRecordNotFound {
		adminRole = models.Role{
			Name:        "系统管理员",
			Code:        "system_admin",
			Description: "系统管理员，拥有所有权限",
			RoleType:    0, // 系统角色
			IsDefault:   false,
		}
		if err := db.Create(&adminRole).Error; err != nil {
			return fmt.Errorf("创建系统管理员角色失败: %w", err)
		}

		// 给系统管理员分配所有权限
		var permissions []models.Permission
		db.Find(&permissions)
		for _, permission := range permissions {
			rolePermission := models.RolePermission{
				RoleID:       adminRole.ID,
				PermissionID: permission.ID,
			}
			db.Create(&rolePermission)
		}
	}

	return nil
}

// createDefaultAdmin 创建默认系统管理员
func createDefaultAdmin(db *gorm.DB) error {
	var adminUser models.User
	result := db.Where("username = ?", "admin").First(&adminUser)
	if result.Error == gorm.ErrRecordNotFound {
		// 创建默认系统管理员
		adminUser = models.User{
			Username:     "admin",
			Email:        "<EMAIL>",
			PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			RealName:     "系统管理员",
			UserType:     models.UserTypeSystemAdmin,
			Status:       models.UserStatusActive,
		}
		if err := db.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("创建默认管理员失败: %w", err)
		}

		// 分配系统管理员角色
		var adminRole models.Role
		if err := db.Where("code = ?", "system_admin").First(&adminRole).Error; err == nil {
			userRole := models.UserRole{
				UserID: adminUser.ID,
				RoleID: adminRole.ID,
			}
			db.Create(&userRole)
		}

		log.Println("默认系统管理员创建成功 - 用户名: admin, 密码: password")
	}

	return nil
}

// getLogLevel 获取日志级别
func getLogLevel(level string) logger.LogLevel {
	switch level {
	case "silent":
		return logger.Silent
	case "error":
		return logger.Error
	case "warn":
		return logger.Warn
	case "info":
		return logger.Info
	default:
		return logger.Info
	}
}
