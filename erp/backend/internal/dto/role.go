package dto

import "time"

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string   `json:"name" binding:"required,min=2,max=50"`
	Code        string   `json:"code" binding:"required,min=2,max=50" validate:"role_code"`
	Description string   `json:"description,omitempty" binding:"max=200"`
	TenantID    *uint64  `json:"tenant_id,omitempty"`    // 租户ID，为空表示系统角色
	PermissionIDs []uint64 `json:"permission_ids,omitempty"` // 权限ID列表
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string   `json:"name,omitempty" binding:"min=2,max=50"`
	Description string   `json:"description,omitempty" binding:"max=200"`
	PermissionIDs []uint64 `json:"permission_ids,omitempty"` // 权限ID列表
}

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	Page     int     `form:"page,default=1" binding:"min=1"`
	PageSize int     `form:"page_size,default=20" binding:"min=1,max=100"`
	Keyword  string  `form:"keyword"`                     // 搜索关键词（角色名、代码）
	TenantID *uint64 `form:"tenant_id"`                   // 租户ID过滤
	SortBy   string  `form:"sort_by,default=created_at"`  // 排序字段
	SortDesc bool    `form:"sort_desc,default=true"`      // 是否降序
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          uint64           `json:"id"`
	Name        string           `json:"name"`
	Code        string           `json:"code"`
	Description string           `json:"description"`
	TenantID    *uint64          `json:"tenant_id"`
	TenantName  string           `json:"tenant_name,omitempty"`
	Permissions []PermissionInfo `json:"permissions"`
	UserCount   int64            `json:"user_count"`   // 拥有此角色的用户数量
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

// RoleListResponse 角色列表响应
type RoleListResponse struct {
	Roles      []RoleResponse `json:"roles"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// PermissionInfo 权限信息
type PermissionInfo struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Code        string `json:"code" binding:"required,min=2,max=100" validate:"permission_code"`
	Description string `json:"description,omitempty" binding:"max=200"`
	Resource    string `json:"resource" binding:"required,min=2,max=50"`
	Action      string `json:"action" binding:"required,min=2,max=50"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name,omitempty" binding:"min=2,max=50"`
	Description string `json:"description,omitempty" binding:"max=200"`
	Resource    string `json:"resource,omitempty" binding:"min=2,max=50"`
	Action      string `json:"action,omitempty" binding:"min=2,max=50"`
}

// PermissionListRequest 权限列表请求
type PermissionListRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Keyword  string `form:"keyword"`                    // 搜索关键词
	Resource string `form:"resource"`                   // 资源过滤
	Action   string `form:"action"`                     // 操作过滤
	SortBy   string `form:"sort_by,default=created_at"` // 排序字段
	SortDesc bool   `form:"sort_desc,default=true"`     // 是否降序
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID          uint64    `json:"id"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Description string    `json:"description"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	RoleCount   int64     `json:"role_count"`   // 拥有此权限的角色数量
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// PermissionListResponse 权限列表响应
type PermissionListResponse struct {
	Permissions []PermissionResponse `json:"permissions"`
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	TotalPages  int                  `json:"total_pages"`
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	PermissionIDs []uint64 `json:"permission_ids" binding:"required"`
}

// RolePermissionAssignment 角色权限分配
type RolePermissionAssignment struct {
	RoleID       uint64    `json:"role_id"`
	PermissionID uint64    `json:"permission_id"`
	RoleName     string    `json:"role_name"`
	RoleCode     string    `json:"role_code"`
	PermissionName string  `json:"permission_name"`
	PermissionCode string  `json:"permission_code"`
	CreatedAt    time.Time `json:"created_at"`
}

// BatchAssignRolesRequest 批量分配角色请求
type BatchAssignRolesRequest struct {
	UserIDs []uint64 `json:"user_ids" binding:"required"`
	RoleIDs []uint64 `json:"role_ids" binding:"required"`
}

// BatchRevokeRolesRequest 批量撤销角色请求
type BatchRevokeRolesRequest struct {
	UserIDs []uint64 `json:"user_ids" binding:"required"`
	RoleIDs []uint64 `json:"role_ids" binding:"required"`
}

// RoleStatistics 角色统计信息
type RoleStatistics struct {
	TotalRoles       int64 `json:"total_roles"`
	SystemRoles      int64 `json:"system_roles"`
	TenantRoles      int64 `json:"tenant_roles"`
	TotalPermissions int64 `json:"total_permissions"`
	ActiveRoles      int64 `json:"active_roles"`      // 有用户的角色
	UnusedRoles      int64 `json:"unused_roles"`      // 没有用户的角色
}

// UserRoleInfo 用户角色信息
type UserRoleInfo struct {
	UserID    uint64     `json:"user_id"`
	Username  string     `json:"username"`
	RealName  string     `json:"real_name"`
	RoleID    uint64     `json:"role_id"`
	RoleName  string     `json:"role_name"`
	RoleCode  string     `json:"role_code"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
}

// GetRoleUsersRequest 获取角色用户请求
type GetRoleUsersRequest struct {
	Page     int `form:"page,default=1" binding:"min=1"`
	PageSize int `form:"page_size,default=20" binding:"min=1,max=100"`
}

// GetRoleUsersResponse 获取角色用户响应
type GetRoleUsersResponse struct {
	Users      []UserRoleInfo `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// PermissionTreeNode 权限树节点
type PermissionTreeNode struct {
	ID          uint64                `json:"id"`
	Name        string                `json:"name"`
	Code        string                `json:"code"`
	Resource    string                `json:"resource"`
	Action      string                `json:"action"`
	Description string                `json:"description"`
	Children    []*PermissionTreeNode `json:"children,omitempty"`
}

// GetPermissionTreeResponse 获取权限树响应
type GetPermissionTreeResponse struct {
	Tree []*PermissionTreeNode `json:"tree"`
}

// CheckRolePermissionRequest 检查角色权限请求
type CheckRolePermissionRequest struct {
	RoleID     uint64 `json:"role_id" binding:"required"`
	Permission string `json:"permission" binding:"required"`
}

// CheckRolePermissionResponse 检查角色权限响应
type CheckRolePermissionResponse struct {
	HasPermission bool   `json:"has_permission"`
	Reason        string `json:"reason,omitempty"`
}

// RolePermissionMatrix 角色权限矩阵
type RolePermissionMatrix struct {
	RoleID        uint64   `json:"role_id"`
	RoleName      string   `json:"role_name"`
	RoleCode      string   `json:"role_code"`
	PermissionIDs []uint64 `json:"permission_ids"`
}

// GetRolePermissionMatrixResponse 获取角色权限矩阵响应
type GetRolePermissionMatrixResponse struct {
	Matrix      []RolePermissionMatrix `json:"matrix"`
	Permissions []PermissionInfo       `json:"permissions"`
}

// ImportRolesRequest 导入角色请求
type ImportRolesRequest struct {
	Roles []CreateRoleRequest `json:"roles" binding:"required"`
}

// ImportRolesResponse 导入角色响应
type ImportRolesResponse struct {
	SuccessCount int                   `json:"success_count"`
	FailureCount int                   `json:"failure_count"`
	Failures     []ImportFailureDetail `json:"failures,omitempty"`
}

// ExportRolesRequest 导出角色请求
type ExportRolesRequest struct {
	Format   string  `form:"format,default=csv" binding:"oneof=csv xlsx json"`
	TenantID *uint64 `form:"tenant_id"`
}

// RoleTemplateInfo 角色模板信息
type RoleTemplateInfo struct {
	ID          uint64           `json:"id"`
	Name        string           `json:"name"`
	Code        string           `json:"code"`
	Description string           `json:"description"`
	Permissions []PermissionInfo `json:"permissions"`
}

// GetRoleTemplatesResponse 获取角色模板响应
type GetRoleTemplatesResponse struct {
	Templates []RoleTemplateInfo `json:"templates"`
}

// CreateRoleFromTemplateRequest 从模板创建角色请求
type CreateRoleFromTemplateRequest struct {
	TemplateID  uint64  `json:"template_id" binding:"required"`
	Name        string  `json:"name" binding:"required,min=2,max=50"`
	Code        string  `json:"code" binding:"required,min=2,max=50" validate:"role_code"`
	Description string  `json:"description,omitempty" binding:"max=200"`
	TenantID    *uint64 `json:"tenant_id,omitempty"`
}

// RoleAuditLog 角色审计日志
type RoleAuditLog struct {
	ID          uint64    `json:"id"`
	RoleID      uint64    `json:"role_id"`
	RoleName    string    `json:"role_name"`
	Action      string    `json:"action"`      // CREATE, UPDATE, DELETE, ASSIGN, REVOKE
	Description string    `json:"description"`
	OperatorID  uint64    `json:"operator_id"`
	Operator    string    `json:"operator"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetRoleAuditLogRequest 获取角色审计日志请求
type GetRoleAuditLogRequest struct {
	RoleID    *uint64 `form:"role_id"`
	Action    string  `form:"action"`
	StartDate string  `form:"start_date"` // YYYY-MM-DD
	EndDate   string  `form:"end_date"`   // YYYY-MM-DD
	Page      int     `form:"page,default=1" binding:"min=1"`
	PageSize  int     `form:"page_size,default=20" binding:"min=1,max=100"`
}

// GetRoleAuditLogResponse 获取角色审计日志响应
type GetRoleAuditLogResponse struct {
	Logs       []RoleAuditLog `json:"logs"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}
