package dto

import "time"

// CreateTenantRequest 创建租户请求
type CreateTenantRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	Code        string `json:"code" binding:"required,min=2,max=50" validate:"tenant_code"`
	Description string `json:"description,omitempty" binding:"max=500"`
	ContactName string `json:"contact_name,omitempty" binding:"max=50"`
	ContactPhone string `json:"contact_phone,omitempty" binding:"max=20"`
	ContactEmail string `json:"contact_email,omitempty" binding:"email,max=100"`
	Address     string `json:"address,omitempty" binding:"max=200"`
	MaxUsers    int    `json:"max_users,omitempty" binding:"min=1,max=10000"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// UpdateTenantRequest 更新租户请求
type UpdateTenantRequest struct {
	Name        string `json:"name,omitempty" binding:"min=2,max=100"`
	Description string `json:"description,omitempty" binding:"max=500"`
	ContactName string `json:"contact_name,omitempty" binding:"max=50"`
	ContactPhone string `json:"contact_phone,omitempty" binding:"max=20"`
	ContactEmail string `json:"contact_email,omitempty" binding:"email,max=100"`
	Address     string `json:"address,omitempty" binding:"max=200"`
	MaxUsers    *int   `json:"max_users,omitempty" binding:"min=1,max=10000"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// TenantListRequest 租户列表请求
type TenantListRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Keyword  string `form:"keyword"`                     // 搜索关键词（租户名、代码）
	Status   *int   `form:"status"`                      // 状态过滤
	SortBy   string `form:"sort_by,default=created_at"`  // 排序字段
	SortDesc bool   `form:"sort_desc,default=true"`      // 是否降序
}

// TenantResponse 租户响应
type TenantResponse struct {
	ID           uint64     `json:"id"`
	Name         string     `json:"name"`
	Code         string     `json:"code"`
	Description  string     `json:"description"`
	ContactName  string     `json:"contact_name"`
	ContactPhone string     `json:"contact_phone"`
	ContactEmail string     `json:"contact_email"`
	Address      string     `json:"address"`
	Status       int        `json:"status"`
	StatusText   string     `json:"status_text"`
	MaxUsers     int        `json:"max_users"`
	CurrentUsers int64      `json:"current_users"`  // 当前用户数量
	ExpiresAt    *time.Time `json:"expires_at"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// TenantListResponse 租户列表响应
type TenantListResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// UpdateTenantStatusRequest 更新租户状态请求
type UpdateTenantStatusRequest struct {
	Status int    `json:"status" binding:"required,oneof=0 1 2"` // 0:禁用 1:正常 2:过期
	Reason string `json:"reason,omitempty" binding:"max=200"`     // 状态变更原因
}

// TenantStatistics 租户统计信息
type TenantStatistics struct {
	TotalTenants   int64 `json:"total_tenants"`
	ActiveTenants  int64 `json:"active_tenants"`
	DisabledTenants int64 `json:"disabled_tenants"`
	ExpiredTenants int64 `json:"expired_tenants"`
	TotalUsers     int64 `json:"total_users"`
	AverageUsers   float64 `json:"average_users"`
	MaxUsersLimit  int64 `json:"max_users_limit"`
}

// TenantUserInfo 租户用户信息
type TenantUserInfo struct {
	UserID    uint64    `json:"user_id"`
	Username  string    `json:"username"`
	RealName  string    `json:"real_name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Status    int       `json:"status"`
	StatusText string   `json:"status_text"`
	UserType  int       `json:"user_type"`
	UserTypeText string `json:"user_type_text"`
	CreatedAt time.Time `json:"created_at"`
	LastLoginAt *time.Time `json:"last_login_at"`
}

// GetTenantUsersRequest 获取租户用户请求
type GetTenantUsersRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Keyword  string `form:"keyword"`                     // 搜索关键词
	Status   *int   `form:"status"`                      // 状态过滤
	UserType *int   `form:"user_type"`                   // 用户类型过滤
	SortBy   string `form:"sort_by,default=created_at"`  // 排序字段
	SortDesc bool   `form:"sort_desc,default=true"`      // 是否降序
}

// GetTenantUsersResponse 获取租户用户响应
type GetTenantUsersResponse struct {
	Users      []TenantUserInfo `json:"users"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// TenantResourceUsage 租户资源使用情况
type TenantResourceUsage struct {
	TenantID     uint64  `json:"tenant_id"`
	TenantName   string  `json:"tenant_name"`
	UserCount    int64   `json:"user_count"`
	MaxUsers     int     `json:"max_users"`
	UserUsage    float64 `json:"user_usage"`    // 用户使用率
	RoleCount    int64   `json:"role_count"`
	StorageUsed  int64   `json:"storage_used"`  // 存储使用量（字节）
	StorageLimit int64   `json:"storage_limit"` // 存储限制（字节）
	StorageUsage float64 `json:"storage_usage"` // 存储使用率
}

// GetTenantResourceUsageResponse 获取租户资源使用情况响应
type GetTenantResourceUsageResponse struct {
	Usage []TenantResourceUsage `json:"usage"`
}

// BatchUpdateTenantsRequest 批量更新租户请求
type BatchUpdateTenantsRequest struct {
	TenantIDs []uint64 `json:"tenant_ids" binding:"required"`
	Updates   struct {
		Status    *int       `json:"status,omitempty" binding:"omeof=0 1 2"`
		MaxUsers  *int       `json:"max_users,omitempty" binding:"min=1,max=10000"`
		ExpiresAt *time.Time `json:"expires_at,omitempty"`
	} `json:"updates" binding:"required"`
	Reason string `json:"reason,omitempty" binding:"max=200"`
}

// BatchUpdateTenantsResponse 批量更新租户响应
type BatchUpdateTenantsResponse struct {
	SuccessCount int                   `json:"success_count"`
	FailureCount int                   `json:"failure_count"`
	Failures     []BatchFailureDetail  `json:"failures,omitempty"`
}

// BatchFailureDetail 批量操作失败详情
type BatchFailureDetail struct {
	ID     uint64 `json:"id"`
	Reason string `json:"reason"`
}

// ImportFailureDetail 导入失败详情
type ImportFailureDetail struct {
	Index  int    `json:"index"`
	Reason string `json:"reason"`
	Data   interface{} `json:"data,omitempty"`
}

// ImportTenantsRequest 导入租户请求
type ImportTenantsRequest struct {
	Tenants []CreateTenantRequest `json:"tenants" binding:"required"`
}

// ImportTenantsResponse 导入租户响应
type ImportTenantsResponse struct {
	SuccessCount int                   `json:"success_count"`
	FailureCount int                   `json:"failure_count"`
	Failures     []ImportFailureDetail `json:"failures,omitempty"`
}

// ExportTenantsRequest 导出租户请求
type ExportTenantsRequest struct {
	Format   string `form:"format,default=csv" binding:"oneof=csv xlsx json"`
	Status   *int   `form:"status"`
	Keyword  string `form:"keyword"`
}

// TenantAuditLog 租户审计日志
type TenantAuditLog struct {
	ID          uint64    `json:"id"`
	TenantID    uint64    `json:"tenant_id"`
	TenantName  string    `json:"tenant_name"`
	Action      string    `json:"action"`      // CREATE, UPDATE, DELETE, ACTIVATE, DISABLE, EXPIRE
	Description string    `json:"description"`
	OldValue    string    `json:"old_value,omitempty"`
	NewValue    string    `json:"new_value,omitempty"`
	OperatorID  uint64    `json:"operator_id"`
	Operator    string    `json:"operator"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetTenantAuditLogRequest 获取租户审计日志请求
type GetTenantAuditLogRequest struct {
	TenantID  *uint64 `form:"tenant_id"`
	Action    string  `form:"action"`
	StartDate string  `form:"start_date"` // YYYY-MM-DD
	EndDate   string  `form:"end_date"`   // YYYY-MM-DD
	Page      int     `form:"page,default=1" binding:"min=1"`
	PageSize  int     `form:"page_size,default=20" binding:"min=1,max=100"`
}

// GetTenantAuditLogResponse 获取租户审计日志响应
type GetTenantAuditLogResponse struct {
	Logs       []TenantAuditLog `json:"logs"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// TenantConfig 租户配置
type TenantConfig struct {
	TenantID         uint64                 `json:"tenant_id"`
	AllowedModules   []string               `json:"allowed_modules"`   // 允许使用的模块
	FeatureFlags     map[string]bool        `json:"feature_flags"`     // 功能开关
	CustomSettings   map[string]interface{} `json:"custom_settings"`   // 自定义设置
	ThemeConfig      map[string]string      `json:"theme_config"`      // 主题配置
	NotificationConfig map[string]bool      `json:"notification_config"` // 通知配置
}

// UpdateTenantConfigRequest 更新租户配置请求
type UpdateTenantConfigRequest struct {
	AllowedModules     []string               `json:"allowed_modules,omitempty"`
	FeatureFlags       map[string]bool        `json:"feature_flags,omitempty"`
	CustomSettings     map[string]interface{} `json:"custom_settings,omitempty"`
	ThemeConfig        map[string]string      `json:"theme_config,omitempty"`
	NotificationConfig map[string]bool        `json:"notification_config,omitempty"`
}

// GetTenantConfigResponse 获取租户配置响应
type GetTenantConfigResponse struct {
	Config TenantConfig `json:"config"`
}

// TenantQuota 租户配额
type TenantQuota struct {
	TenantID      uint64 `json:"tenant_id"`
	MaxUsers      int    `json:"max_users"`
	MaxStorage    int64  `json:"max_storage"`    // 最大存储空间（字节）
	MaxAPIRequests int64 `json:"max_api_requests"` // 每日API请求限制
	MaxFileSize   int64  `json:"max_file_size"`  // 最大文件大小（字节）
	MaxFileCount  int    `json:"max_file_count"` // 最大文件数量
}

// UpdateTenantQuotaRequest 更新租户配额请求
type UpdateTenantQuotaRequest struct {
	MaxUsers       *int   `json:"max_users,omitempty" binding:"min=1,max=10000"`
	MaxStorage     *int64 `json:"max_storage,omitempty" binding:"min=1"`
	MaxAPIRequests *int64 `json:"max_api_requests,omitempty" binding:"min=1"`
	MaxFileSize    *int64 `json:"max_file_size,omitempty" binding:"min=1"`
	MaxFileCount   *int   `json:"max_file_count,omitempty" binding:"min=1"`
}

// GetTenantQuotaResponse 获取租户配额响应
type GetTenantQuotaResponse struct {
	Quota TenantQuota `json:"quota"`
}

// TenantDashboard 租户仪表板数据
type TenantDashboard struct {
	TenantInfo      TenantResponse      `json:"tenant_info"`
	UserStatistics  struct {
		TotalUsers   int64 `json:"total_users"`
		ActiveUsers  int64 `json:"active_users"`
		NewUsers     int64 `json:"new_users"`     // 本月新增用户
	} `json:"user_statistics"`
	ResourceUsage   TenantResourceUsage `json:"resource_usage"`
	RecentActivities []TenantAuditLog   `json:"recent_activities"`
}

// GetTenantDashboardResponse 获取租户仪表板响应
type GetTenantDashboardResponse struct {
	Dashboard TenantDashboard `json:"dashboard"`
}
