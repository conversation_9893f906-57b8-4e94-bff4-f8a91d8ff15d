package dto

import "time"

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required" validate:"username"`
	Password string `json:"password" binding:"required,min=6"`
	TenantCode string `json:"tenant_code,omitempty"` // 租户代码，系统管理员登录时可为空
	RememberMe bool   `json:"remember_me"`           // 记住我
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	TokenType    string    `json:"token_type"`
	ExpiresIn    int64     `json:"expires_in"`
	User         *UserInfo `json:"user"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username   string `json:"username" binding:"required" validate:"username"`
	Password   string `json:"password" binding:"required" validate:"password"`
	Email      string `json:"email" binding:"required,email"`
	Phone      string `json:"phone,omitempty" validate:"phone"`
	RealName   string `json:"real_name" binding:"required,min=2,max=50"`
	TenantCode string `json:"tenant_code" binding:"required" validate:"tenant_code"`
}

// RegisterResponse 注册响应
type RegisterResponse struct {
	UserID   uint64    `json:"user_id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	Message  string    `json:"message"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RefreshTokenResponse 刷新令牌响应
type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	RefreshToken string `json:"refresh_token,omitempty"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required" validate:"password"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ResetPasswordConfirmRequest 确认重置密码请求
type ResetPasswordConfirmRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required" validate:"password"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID         uint64     `json:"id"`
	Username   string     `json:"username"`
	Email      string     `json:"email"`
	Phone      string     `json:"phone,omitempty"`
	RealName   string     `json:"real_name"`
	Avatar     string     `json:"avatar,omitempty"`
	UserType   int8       `json:"user_type"`
	Status     int8       `json:"status"`
	TenantID   *uint64    `json:"tenant_id,omitempty"`
	TenantName string     `json:"tenant_name,omitempty"`
	Roles      []string   `json:"roles"`
	Permissions []string  `json:"permissions,omitempty"`
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	CreatedAt  time.Time  `json:"created_at"`
}

// ProfileUpdateRequest 更新个人资料请求
type ProfileUpdateRequest struct {
	Email    string `json:"email,omitempty" validate:"email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	RealName string `json:"real_name,omitempty" binding:"min=2,max=50"`
	Avatar   string `json:"avatar,omitempty"`
}

// SessionInfo 会话信息
type SessionInfo struct {
	ID          uint64    `json:"id"`
	UserID      uint64    `json:"user_id"`
	TokenID     string    `json:"token_id"`
	UserAgent   string    `json:"user_agent"`
	IPAddress   string    `json:"ip_address"`
	LoginAt     time.Time `json:"login_at"`
	LastActiveAt time.Time `json:"last_active_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	IsActive    bool      `json:"is_active"`
}

// LoginAttemptInfo 登录尝试信息
type LoginAttemptInfo struct {
	ID          uint64    `json:"id"`
	Username    string    `json:"username"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	Success     bool      `json:"success"`
	FailReason  string    `json:"fail_reason,omitempty"`
	AttemptedAt time.Time `json:"attempted_at"`
}

// GetSessionsResponse 获取会话列表响应
type GetSessionsResponse struct {
	Sessions []SessionInfo `json:"sessions"`
	Total    int64         `json:"total"`
}

// GetLoginAttemptsResponse 获取登录尝试列表响应
type GetLoginAttemptsResponse struct {
	Attempts []LoginAttemptInfo `json:"attempts"`
	Total    int64              `json:"total"`
}

// RevokeSessionRequest 撤销会话请求
type RevokeSessionRequest struct {
	SessionID uint64 `json:"session_id" binding:"required"`
}

// ValidateTokenRequest 验证令牌请求
type ValidateTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// ValidateTokenResponse 验证令牌响应
type ValidateTokenResponse struct {
	Valid   bool      `json:"valid"`
	UserID  uint64    `json:"user_id,omitempty"`
	Username string   `json:"username,omitempty"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// TenantLoginRequest 租户登录请求（用于租户用户登录）
type TenantLoginRequest struct {
	Username   string `json:"username" binding:"required" validate:"username"`
	Password   string `json:"password" binding:"required,min=6"`
	TenantCode string `json:"tenant_code" binding:"required" validate:"tenant_code"`
	RememberMe bool   `json:"remember_me"`
}

// SystemAdminLoginRequest 系统管理员登录请求
type SystemAdminLoginRequest struct {
	Username   string `json:"username" binding:"required" validate:"username"`
	Password   string `json:"password" binding:"required,min=6"`
	RememberMe bool   `json:"remember_me"`
}

// AuthStatus 认证状态
type AuthStatus struct {
	IsAuthenticated bool      `json:"is_authenticated"`
	User            *UserInfo `json:"user,omitempty"`
	ExpiresAt       *time.Time `json:"expires_at,omitempty"`
}

// SecuritySettings 安全设置
type SecuritySettings struct {
	MaxLoginAttempts      int    `json:"max_login_attempts"`
	LockoutDuration       int    `json:"lockout_duration_minutes"`
	SessionTimeout        int    `json:"session_timeout_hours"`
	MaxConcurrentSessions int    `json:"max_concurrent_sessions"`
	PasswordMinLength     int    `json:"password_min_length"`
	RequireStrongPassword bool   `json:"require_strong_password"`
	EnableTwoFactor       bool   `json:"enable_two_factor"`
}

// UpdateSecuritySettingsRequest 更新安全设置请求
type UpdateSecuritySettingsRequest struct {
	MaxLoginAttempts      *int  `json:"max_login_attempts,omitempty"`
	LockoutDuration       *int  `json:"lockout_duration_minutes,omitempty"`
	SessionTimeout        *int  `json:"session_timeout_hours,omitempty"`
	MaxConcurrentSessions *int  `json:"max_concurrent_sessions,omitempty"`
	PasswordMinLength     *int  `json:"password_min_length,omitempty"`
	RequireStrongPassword *bool `json:"require_strong_password,omitempty"`
	EnableTwoFactor       *bool `json:"enable_two_factor,omitempty"`
}

// AccountLockInfo 账户锁定信息
type AccountLockInfo struct {
	IsLocked      bool       `json:"is_locked"`
	LockReason    string     `json:"lock_reason,omitempty"`
	LockedAt      *time.Time `json:"locked_at,omitempty"`
	UnlockAt      *time.Time `json:"unlock_at,omitempty"`
	FailedAttempts int       `json:"failed_attempts"`
}

// UnlockAccountRequest 解锁账户请求
type UnlockAccountRequest struct {
	UserID uint64 `json:"user_id" binding:"required"`
	Reason string `json:"reason,omitempty"`
}
