package dto

import "time"

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username  string   `json:"username" binding:"required" validate:"username"`
	Password  string   `json:"password" binding:"required" validate:"password"`
	Email     string   `json:"email" binding:"required,email"`
	Phone     string   `json:"phone,omitempty" validate:"phone"`
	RealName  string   `json:"real_name" binding:"required,min=2,max=50"`
	AvatarURL string   `json:"avatar_url,omitempty"`
	UserType  int8     `json:"user_type" binding:"required,oneof=1 2"` // 1-租户用户，2-系统管理员
	TenantID  *uint64  `json:"tenant_id,omitempty"`                    // 租户用户必须提供
	RoleIDs   []uint64 `json:"role_ids,omitempty"`                     // 角色ID列表
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email     string   `json:"email,omitempty" validate:"email"`
	Phone     string   `json:"phone,omitempty" validate:"phone"`
	RealName  string   `json:"real_name,omitempty" binding:"min=2,max=50"`
	AvatarURL string   `json:"avatar_url,omitempty"`
	Status    *int8    `json:"status,omitempty" binding:"oneof=1 2 3"` // 1-正常，2-暂停，3-禁用
	RoleIDs   []uint64 `json:"role_ids,omitempty"`                     // 角色ID列表
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page     int     `form:"page,default=1" binding:"min=1"`
	PageSize int     `form:"page_size,default=20" binding:"min=1,max=100"`
	Keyword  string  `form:"keyword"`                                 // 搜索关键词（用户名、邮箱、真实姓名）
	UserType *int8   `form:"user_type" binding:"omitempty,oneof=1 2"` // 用户类型过滤
	Status   *int8   `form:"status" binding:"omitempty,oneof=1 2 3"`  // 状态过滤
	TenantID *uint64 `form:"tenant_id"`                               // 租户ID过滤
	RoleID   *uint64 `form:"role_id"`                                 // 角色ID过滤
	SortBy   string  `form:"sort_by,default=created_at"`              // 排序字段
	SortDesc bool    `form:"sort_desc,default=true"`                  // 是否降序
}

// UserResponse 用户响应
type UserResponse struct {
	ID              uint64     `json:"id"`
	Username        string     `json:"username"`
	Email           string     `json:"email"`
	Phone           string     `json:"phone"`
	RealName        string     `json:"real_name"`
	AvatarURL       string     `json:"avatar_url"`
	UserType        int8       `json:"user_type"`
	UserTypeText    string     `json:"user_type_text"`
	Status          int8       `json:"status"`
	StatusText      string     `json:"status_text"`
	TenantID        *uint64    `json:"tenant_id"`
	TenantName      string     `json:"tenant_name,omitempty"`
	Roles           []RoleInfo `json:"roles"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	LastLoginIP     string     `json:"last_login_ip"`
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// RoleInfo 角色信息
type RoleInfo struct {
	ID          uint64     `json:"id"`
	Name        string     `json:"name"`
	Code        string     `json:"code"`
	Description string     `json:"description"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// AdminResetPasswordRequest 管理员重置用户密码请求
type AdminResetPasswordRequest struct {
	UserID      uint64 `json:"user_id" binding:"required"`
	NewPassword string `json:"new_password" binding:"required" validate:"password"`
	SendEmail   bool   `json:"send_email"` // 是否发送邮件通知用户
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status int8   `json:"status" binding:"required,oneof=1 2 3"` // 1-正常，2-暂停，3-禁用
	Reason string `json:"reason,omitempty"`                      // 状态变更原因
}

// AssignRolesRequest 分配角色请求
type AssignRolesRequest struct {
	RoleIDs []uint64 `json:"role_ids" binding:"required"`
}

// UserRoleAssignment 用户角色分配
type UserRoleAssignment struct {
	UserID    uint64     `json:"user_id"`
	RoleID    uint64     `json:"role_id"`
	RoleName  string     `json:"role_name"`
	RoleCode  string     `json:"role_code"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
}

// BatchUpdateUsersRequest 批量更新用户请求
type BatchUpdateUsersRequest struct {
	UserIDs []uint64               `json:"user_ids" binding:"required"`
	Updates map[string]interface{} `json:"updates" binding:"required"`
}

// BatchDeleteUsersRequest 批量删除用户请求
type BatchDeleteUsersRequest struct {
	UserIDs []uint64 `json:"user_ids" binding:"required"`
	Force   bool     `json:"force"` // 是否强制删除（物理删除）
}

// UserStatistics 用户统计信息
type UserStatistics struct {
	TotalUsers       int64 `json:"total_users"`
	ActiveUsers      int64 `json:"active_users"`
	SuspendedUsers   int64 `json:"suspended_users"`
	DisabledUsers    int64 `json:"disabled_users"`
	TenantUsers      int64 `json:"tenant_users"`
	SystemAdmins     int64 `json:"system_admins"`
	RecentLogins     int64 `json:"recent_logins"` // 最近7天登录用户数
	NewUsersThisWeek int64 `json:"new_users_this_week"`
}

// UserActivityLog 用户活动日志
type UserActivityLog struct {
	ID          uint64    `json:"id"`
	UserID      uint64    `json:"user_id"`
	Username    string    `json:"username"`
	Action      string    `json:"action"`
	Description string    `json:"description"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetUserActivityRequest 获取用户活动请求
type GetUserActivityRequest struct {
	UserID    uint64 `form:"user_id"`
	Page      int    `form:"page,default=1" binding:"min=1"`
	PageSize  int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Action    string `form:"action"`
	StartDate string `form:"start_date"` // YYYY-MM-DD
	EndDate   string `form:"end_date"`   // YYYY-MM-DD
}

// UserActivityResponse 用户活动响应
type UserActivityResponse struct {
	Activities []UserActivityLog `json:"activities"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// ImportUsersRequest 导入用户请求
type ImportUsersRequest struct {
	Users []CreateUserRequest `json:"users" binding:"required"`
}

// ImportUsersResponse 导入用户响应
type ImportUsersResponse struct {
	SuccessCount int                       `json:"success_count"`
	FailureCount int                       `json:"failure_count"`
	Failures     []UserImportFailureDetail `json:"failures,omitempty"`
}

// UserImportFailureDetail 用户导入失败详情
type UserImportFailureDetail struct {
	Index    int    `json:"index"`
	Username string `json:"username"`
	Error    string `json:"error"`
}

// ExportUsersRequest 导出用户请求
type ExportUsersRequest struct {
	Format   string  `form:"format,default=csv" binding:"oneof=csv xlsx json"` // 导出格式
	UserType *int8   `form:"user_type" binding:"omitempty,oneof=1 2"`
	Status   *int8   `form:"status" binding:"omitempty,oneof=1 2 3"`
	TenantID *uint64 `form:"tenant_id"`
}

// UserPermissionInfo 用户权限信息
type UserPermissionInfo struct {
	UserID      uint64   `json:"user_id"`
	Username    string   `json:"username"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
}

// CheckUserPermissionRequest 检查用户权限请求
type CheckUserPermissionRequest struct {
	UserID     uint64 `json:"user_id" binding:"required"`
	Permission string `json:"permission" binding:"required"`
}

// CheckUserPermissionResponse 检查用户权限响应
type CheckUserPermissionResponse struct {
	HasPermission bool   `json:"has_permission"`
	Reason        string `json:"reason,omitempty"`
}

// UserSessionInfo 用户会话信息
type UserSessionInfo struct {
	ID         uint64    `json:"id"`
	TokenID    string    `json:"token_id"`
	DeviceInfo string    `json:"device_info"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	ExpiresAt  time.Time `json:"expires_at"`
	CreatedAt  time.Time `json:"created_at"`
	LastUsedAt time.Time `json:"last_used_at"`
	IsExpired  bool      `json:"is_expired"`
}

// GetUserSessionsResponse 获取用户会话响应
type GetUserSessionsResponse struct {
	Sessions []UserSessionInfo `json:"sessions"`
	Total    int64             `json:"total"`
}

// RevokeUserSessionRequest 撤销用户会话请求
type RevokeUserSessionRequest struct {
	SessionID uint64 `json:"session_id" binding:"required"`
}

// UserSecurityInfo 用户安全信息
type UserSecurityInfo struct {
	UserID              uint64     `json:"user_id"`
	Username            string     `json:"username"`
	LastPasswordChange  *time.Time `json:"last_password_change"`
	FailedLoginAttempts int        `json:"failed_login_attempts"`
	LastFailedLogin     *time.Time `json:"last_failed_login"`
	IsLocked            bool       `json:"is_locked"`
	LockReason          string     `json:"lock_reason,omitempty"`
	TwoFactorEnabled    bool       `json:"two_factor_enabled"`
}

// UpdateUserSecurityRequest 更新用户安全设置请求
type UpdateUserSecurityRequest struct {
	ForcePasswordChange bool `json:"force_password_change"`
	EnableTwoFactor     bool `json:"enable_two_factor"`
	UnlockAccount       bool `json:"unlock_account"`
}
