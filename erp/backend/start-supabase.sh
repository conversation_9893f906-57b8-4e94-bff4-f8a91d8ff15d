#!/bin/bash

# ERP系统后端启动脚本 - Supabase版本

# 设置环境变量
export SERVER_ADDRESS=":8080"
export SERVER_MODE="debug"
export SERVER_READ_TIMEOUT="30"
export SERVER_WRITE_TIMEOUT="30"

# Supabase配置
export SUPABASE_ENABLED="true"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="your-anon-key-here"
export SUPABASE_SERVICE_KEY="your-service-key-here"
export SUPABASE_DB_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"

# 传统数据库配置（Supabase启用时会被忽略）
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_USER="postgres"
export DB_PASSWORD="password"
export DB_NAME="erp_db"
export DB_SSL_MODE="disable"
export DB_TIMEZONE="Asia/Shanghai"
export DB_MAX_IDLE_CONNS="10"
export DB_MAX_OPEN_CONNS="100"
export DB_CONN_MAX_LIFETIME="3600"
export DB_LOG_LEVEL="info"

# JWT配置
export JWT_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
export JWT_ACCESS_TOKEN_EXPIRY="3600"    # 1小时
export JWT_REFRESH_TOKEN_EXPIRY="604800" # 7天
export JWT_ISSUER="erp-system"

# CORS配置
export CORS_ALLOW_ORIGINS="*"
export CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"
export CORS_ALLOW_HEADERS="Origin,Content-Type,Accept,Authorization,X-Requested-With"
export CORS_EXPOSE_HEADERS="Content-Length"
export CORS_ALLOW_CREDENTIALS="true"
export CORS_MAX_AGE="86400"

# Redis配置（可选）
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export REDIS_PASSWORD=""
export REDIS_DB="0"

echo "=== ERP系统后端启动 (Supabase版本) ==="
echo "服务地址: $SERVER_ADDRESS"
echo "运行模式: $SERVER_MODE"
echo "Supabase URL: $SUPABASE_URL"
echo ""

# 检查必要的环境变量
if [ -z "$SUPABASE_URL" ] || [ "$SUPABASE_URL" = "https://your-project.supabase.co" ]; then
    echo "❌ 错误: 请设置正确的SUPABASE_URL"
    echo "请在Supabase项目设置中获取项目URL"
    exit 1
fi

if [ -z "$SUPABASE_SERVICE_KEY" ] || [ "$SUPABASE_SERVICE_KEY" = "your-service-key-here" ]; then
    echo "❌ 错误: 请设置正确的SUPABASE_SERVICE_KEY"
    echo "请在Supabase项目设置 > API中获取service_role密钥"
    exit 1
fi

if [ -z "$SUPABASE_DB_URL" ] || [[ "$SUPABASE_DB_URL" == *"[YOUR-PASSWORD]"* ]]; then
    echo "❌ 错误: 请设置正确的SUPABASE_DB_URL"
    echo "请在Supabase项目设置 > Database中获取连接字符串"
    exit 1
fi

# 编译项目
echo "编译项目..."
if ! go build -o main .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"
echo ""

# 启动服务
echo "启动服务..."
./main
