#!/bin/bash

# Supabase集成测试脚本

echo "=== ERP系统 Supabase集成测试 ==="
echo ""

# 检查环境变量
echo "1. 检查环境变量配置..."

required_vars=(
    "SUPABASE_URL"
    "SUPABASE_SERVICE_KEY"
    "SUPABASE_DB_URL"
)

missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    else
        echo "✅ $var: 已设置"
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo ""
    echo "❌ 缺少以下环境变量："
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "请设置这些环境变量后重新运行测试。"
    echo "参考 start-supabase.sh 文件中的配置示例。"
    exit 1
fi

echo ""

# 检查Supabase连接
echo "2. 测试Supabase API连接..."

# 使用curl测试Supabase REST API
response=$(curl -s -w "%{http_code}" -o /tmp/supabase_test.json \
    -H "apikey: $SUPABASE_SERVICE_KEY" \
    -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
    "$SUPABASE_URL/rest/v1/")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo "✅ Supabase API连接成功"
else
    echo "❌ Supabase API连接失败 (HTTP $http_code)"
    echo "请检查SUPABASE_URL和SUPABASE_SERVICE_KEY是否正确"
    exit 1
fi

echo ""

# 测试数据库连接
echo "3. 测试PostgreSQL数据库连接..."

# 使用psql测试数据库连接（如果可用）
if command -v psql &> /dev/null; then
    if psql "$SUPABASE_DB_URL" -c "SELECT version();" &> /dev/null; then
        echo "✅ PostgreSQL数据库连接成功"
    else
        echo "❌ PostgreSQL数据库连接失败"
        echo "请检查SUPABASE_DB_URL是否正确"
        exit 1
    fi
else
    echo "⚠️  psql未安装，跳过直接数据库连接测试"
fi

echo ""

# 编译应用程序
echo "4. 编译应用程序..."
if go build -o main .; then
    echo "✅ 应用程序编译成功"
else
    echo "❌ 应用程序编译失败"
    exit 1
fi

echo ""

# 设置Supabase模式
echo "5. 配置Supabase模式..."
export SUPABASE_ENABLED="true"
echo "✅ Supabase模式已启用"

echo ""

# 启动应用程序进行测试
echo "6. 启动应用程序测试..."
echo "启动应用程序（10秒后自动停止）..."

# 在后台启动应用程序
./main &
APP_PID=$!

# 等待应用程序启动
sleep 3

# 检查应用程序是否正在运行
if kill -0 $APP_PID 2>/dev/null; then
    echo "✅ 应用程序启动成功"
    
    # 测试健康检查端点
    echo ""
    echo "7. 测试API端点..."
    
    if curl -s http://localhost:8080/health > /dev/null; then
        echo "✅ 健康检查端点正常"
    else
        echo "❌ 健康检查端点失败"
    fi
    
    # 等待一段时间让应用程序完全初始化
    sleep 5
    
    # 停止应用程序
    kill $APP_PID 2>/dev/null
    wait $APP_PID 2>/dev/null
    
    echo "✅ 应用程序已停止"
else
    echo "❌ 应用程序启动失败"
    exit 1
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "🎉 所有测试通过！Supabase集成配置正确。"
echo ""
echo "现在您可以使用以下命令启动应用程序："
echo "  ./start-supabase.sh"
echo ""
echo "或者手动设置环境变量后运行："
echo "  export SUPABASE_ENABLED=true"
echo "  ./main"
