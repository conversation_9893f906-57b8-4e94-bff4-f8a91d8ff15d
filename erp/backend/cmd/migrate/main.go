package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"erp/backend/internal/config"
	"erp/backend/migrations"
)

func main() {
	// 定义命令行参数
	var (
		action  = flag.String("action", "migrate", "Action to perform: migrate, rollback, status, reset, fresh")
		version = flag.String("version", "", "Migration version (required for rollback)")
		help    = flag.Bool("help", false, "Show help information")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// 加载数据库配置
	dbConfig := config.LoadDatabaseConfigFromEnv()

	// 创建数据库管理器
	dbManager := config.NewDatabaseManager(dbConfig, nil)

	// 连接数据库
	if err := dbManager.Connect(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer dbManager.Close()

	// 创建迁移管理器
	migrator := migrations.NewMigrator(dbManager.GetDB())

	// 初始化迁移表
	if err := migrator.Init(); err != nil {
		log.Fatalf("Failed to initialize migrator: %v", err)
	}

	// 执行相应的操作
	switch *action {
	case "migrate":
		if err := migrator.Migrate(); err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
		fmt.Println("Migration completed successfully!")

	case "rollback":
		if *version == "" {
			log.Fatal("Version is required for rollback action")
		}
		if err := migrator.Rollback(*version); err != nil {
			log.Fatalf("Rollback failed: %v", err)
		}
		fmt.Printf("Rollback of version %s completed successfully!\n", *version)

	case "status":
		if err := migrator.Status(); err != nil {
			log.Fatalf("Failed to get migration status: %v", err)
		}

	case "reset":
		fmt.Println("WARNING: This will reset all migrations and drop all tables!")
		fmt.Print("Are you sure you want to continue? (yes/no): ")

		var confirm string
		fmt.Scanln(&confirm)

		if confirm != "yes" {
			fmt.Println("Operation cancelled.")
			return
		}

		if err := migrator.Reset(); err != nil {
			log.Fatalf("Reset failed: %v", err)
		}
		fmt.Println("Reset completed successfully!")

	case "fresh":
		fmt.Println("WARNING: This will reset all migrations and recreate all tables!")
		fmt.Print("Are you sure you want to continue? (yes/no): ")

		var confirm string
		fmt.Scanln(&confirm)

		if confirm != "yes" {
			fmt.Println("Operation cancelled.")
			return
		}

		if err := migrator.Fresh(); err != nil {
			log.Fatalf("Fresh migration failed: %v", err)
		}
		fmt.Println("Fresh migration completed successfully!")

	default:
		fmt.Printf("Unknown action: %s\n", *action)
		showHelp()
		os.Exit(1)
	}
}

func showHelp() {
	fmt.Println("ERP Database Migration Tool")
	fmt.Println("===========================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  go run cmd/migrate/main.go [options]")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -action string")
	fmt.Println("        Action to perform: migrate, rollback, status, reset, fresh (default \"migrate\")")
	fmt.Println("  -version string")
	fmt.Println("        Migration version (required for rollback)")
	fmt.Println("  -help")
	fmt.Println("        Show this help information")
	fmt.Println()
	fmt.Println("Actions:")
	fmt.Println("  migrate   - Run all pending migrations")
	fmt.Println("  rollback  - Rollback a specific migration (requires -version)")
	fmt.Println("  status    - Show migration status")
	fmt.Println("  reset     - Reset all migrations (drops all tables)")
	fmt.Println("  fresh     - Reset and re-run all migrations")
	fmt.Println()
	fmt.Println("Environment Variables:")
	fmt.Println("  DB_HOST       - Database host (default: localhost)")
	fmt.Println("  DB_PORT       - Database port (default: 5432)")
	fmt.Println("  DB_USERNAME   - Database username (default: postgres)")
	fmt.Println("  DB_PASSWORD   - Database password (default: password)")
	fmt.Println("  DB_DATABASE   - Database name (default: erp_db)")
	fmt.Println("  DB_SSL_MODE   - SSL mode (default: disable)")
	fmt.Println("  DB_TIMEZONE   - Timezone (default: Asia/Shanghai)")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  # Run all migrations")
	fmt.Println("  go run cmd/migrate/main.go")
	fmt.Println()
	fmt.Println("  # Check migration status")
	fmt.Println("  go run cmd/migrate/main.go -action=status")
	fmt.Println()
	fmt.Println("  # Rollback a specific migration")
	fmt.Println("  go run cmd/migrate/main.go -action=rollback -version=001")
	fmt.Println()
	fmt.Println("  # Reset all migrations")
	fmt.Println("  go run cmd/migrate/main.go -action=reset")
	fmt.Println()
	fmt.Println("  # Fresh migration (reset and re-run)")
	fmt.Println("  go run cmd/migrate/main.go -action=fresh")
}
