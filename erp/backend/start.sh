#!/bin/bash

# ERP系统后端启动脚本

# 设置环境变量
export SERVER_ADDRESS=":8080"
export SERVER_MODE="debug"
export SERVER_READ_TIMEOUT="30"
export SERVER_WRITE_TIMEOUT="30"

# 数据库配置
export DB_HOST="*************"
export DB_PORT="5432"
export DB_USER="zebranuxus"
export DB_PASSWORD="a1346797"
export DB_NAME="erp"
export DB_SSL_MODE="disable"
export DB_TIMEZONE="Asia/Shanghai"
export DB_MAX_IDLE_CONNS="10"
export DB_MAX_OPEN_CONNS="100"
export DB_CONN_MAX_LIFETIME="3600"
export DB_LOG_LEVEL="info"

# JWT配置
export JWT_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
export JWT_ACCESS_TOKEN_EXPIRY="3600"    # 1小时
export JWT_REFRESH_TOKEN_EXPIRY="604800" # 7天
export JWT_ISSUER="erp-system"

# CORS配置
export CORS_ALLOW_ORIGINS="*"
export CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"
export CORS_ALLOW_HEADERS="Origin,Content-Type,Accept,Authorization,X-Requested-With"
export CORS_EXPOSE_HEADERS="Content-Length"
export CORS_ALLOW_CREDENTIALS="true"
export CORS_MAX_AGE="86400"

# Redis配置（可选）
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export REDIS_PASSWORD=""
export REDIS_DB="0"

echo "启动ERP系统后端服务..."
echo "服务地址: http://localhost:8080"
echo "API文档: http://localhost:8080/swagger/index.html"
echo ""

# 启动应用
./main
