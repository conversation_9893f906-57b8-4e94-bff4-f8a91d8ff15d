package migrations

import (
	"fmt"
	"log"
	"time"
	"gorm.io/gorm"
)

// Migration 迁移结构
type Migration struct {
	ID          uint      `gorm:"primaryKey;autoIncrement"`
	Version     string    `gorm:"type:varchar(255);uniqueIndex;not null;comment:迁移版本"`
	Name        string    `gorm:"type:varchar(255);not null;comment:迁移名称"`
	Applied     bool      `gorm:"default:false;comment:是否已应用"`
	AppliedAt   *time.Time `gorm:"comment:应用时间"`
	RolledBack  bool      `gorm:"default:false;comment:是否已回滚"`
	RolledBackAt *time.Time `gorm:"comment:回滚时间"`
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (Migration) TableName() string {
	return "migrations"
}

// MigrationFunc 迁移函数类型
type MigrationFunc func(*gorm.DB) error

// MigrationItem 迁移项
type MigrationItem struct {
	Version  string
	Name     string
	Up       MigrationFunc
	Down     MigrationFunc
}

// Migrator 迁移管理器
type Migrator struct {
	db         *gorm.DB
	migrations []MigrationItem
}

// NewMigrator 创建迁移管理器
func NewMigrator(db *gorm.DB) *Migrator {
	migrator := &Migrator{
		db:         db,
		migrations: make([]MigrationItem, 0),
	}
	
	// 注册所有迁移
	migrator.registerMigrations()
	
	return migrator
}

// registerMigrations 注册所有迁移
func (m *Migrator) registerMigrations() {
	m.migrations = []MigrationItem{
		{
			Version: "001",
			Name:    "创建初始表结构",
			Up:      Migration001CreateInitialTables,
			Down:    Rollback001CreateInitialTables,
		},
		// 在这里添加更多迁移...
	}
}

// Init 初始化迁移表
func (m *Migrator) Init() error {
	// 创建迁移记录表
	if err := m.db.AutoMigrate(&Migration{}); err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}
	
	log.Println("Migration table initialized successfully")
	return nil
}

// Migrate 执行迁移
func (m *Migrator) Migrate() error {
	log.Println("Starting database migration...")
	
	for _, migration := range m.migrations {
		if err := m.applyMigration(migration); err != nil {
			return fmt.Errorf("failed to apply migration %s: %w", migration.Version, err)
		}
	}
	
	log.Println("Database migration completed successfully")
	return nil
}

// applyMigration 应用单个迁移
func (m *Migrator) applyMigration(migration MigrationItem) error {
	// 检查迁移是否已经应用
	var migrationRecord Migration
	result := m.db.Where("version = ?", migration.Version).First(&migrationRecord)
	
	if result.Error == nil && migrationRecord.Applied {
		log.Printf("Migration %s (%s) already applied, skipping", migration.Version, migration.Name)
		return nil
	}
	
	log.Printf("Applying migration %s: %s", migration.Version, migration.Name)
	
	// 开始事务
	tx := m.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	
	// 执行迁移
	if err := migration.Up(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("migration failed: %w", err)
	}
	
	// 记录迁移
	now := time.Now()
	if result.Error == gorm.ErrRecordNotFound {
		// 创建新记录
		migrationRecord = Migration{
			Version:   migration.Version,
			Name:      migration.Name,
			Applied:   true,
			AppliedAt: &now,
		}
		if err := tx.Create(&migrationRecord).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to record migration: %w", err)
		}
	} else {
		// 更新现有记录
		migrationRecord.Applied = true
		migrationRecord.AppliedAt = &now
		migrationRecord.RolledBack = false
		migrationRecord.RolledBackAt = nil
		if err := tx.Save(&migrationRecord).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update migration record: %w", err)
		}
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit migration transaction: %w", err)
	}
	
	log.Printf("Migration %s applied successfully", migration.Version)
	return nil
}

// Rollback 回滚迁移
func (m *Migrator) Rollback(version string) error {
	log.Printf("Rolling back migration %s...", version)
	
	// 查找迁移
	var migration MigrationItem
	found := false
	for _, m := range m.migrations {
		if m.Version == version {
			migration = m
			found = true
			break
		}
	}
	
	if !found {
		return fmt.Errorf("migration %s not found", version)
	}
	
	// 检查迁移记录
	var migrationRecord Migration
	result := m.db.Where("version = ?", version).First(&migrationRecord)
	if result.Error != nil {
		return fmt.Errorf("migration record not found: %w", result.Error)
	}
	
	if !migrationRecord.Applied {
		return fmt.Errorf("migration %s is not applied", version)
	}
	
	if migrationRecord.RolledBack {
		log.Printf("Migration %s already rolled back", version)
		return nil
	}
	
	// 开始事务
	tx := m.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	
	// 执行回滚
	if err := migration.Down(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("rollback failed: %w", err)
	}
	
	// 更新迁移记录
	now := time.Now()
	migrationRecord.Applied = false
	migrationRecord.RolledBack = true
	migrationRecord.RolledBackAt = &now
	if err := tx.Save(&migrationRecord).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update migration record: %w", err)
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit rollback transaction: %w", err)
	}
	
	log.Printf("Migration %s rolled back successfully", version)
	return nil
}

// Status 查看迁移状态
func (m *Migrator) Status() error {
	log.Println("Migration Status:")
	log.Println("================")
	
	var migrationRecords []Migration
	m.db.Order("version").Find(&migrationRecords)
	
	// 创建记录映射
	recordMap := make(map[string]Migration)
	for _, record := range migrationRecords {
		recordMap[record.Version] = record
	}
	
	for _, migration := range m.migrations {
		record, exists := recordMap[migration.Version]
		status := "Pending"
		appliedAt := ""
		
		if exists {
			if record.Applied && !record.RolledBack {
				status = "Applied"
				if record.AppliedAt != nil {
					appliedAt = record.AppliedAt.Format("2006-01-02 15:04:05")
				}
			} else if record.RolledBack {
				status = "Rolled Back"
				if record.RolledBackAt != nil {
					appliedAt = "Rolled back at: " + record.RolledBackAt.Format("2006-01-02 15:04:05")
				}
			}
		}
		
		log.Printf("%-10s %-15s %-30s %s", migration.Version, status, migration.Name, appliedAt)
	}
	
	return nil
}

// Reset 重置所有迁移（危险操作）
func (m *Migrator) Reset() error {
	log.Println("WARNING: This will reset all migrations and drop all tables!")
	log.Println("This operation cannot be undone.")
	
	// 获取所有已应用的迁移，按版本倒序
	var migrationRecords []Migration
	m.db.Where("applied = ?", true).Order("version DESC").Find(&migrationRecords)
	
	// 逐个回滚
	for _, record := range migrationRecords {
		if err := m.Rollback(record.Version); err != nil {
			return fmt.Errorf("failed to rollback migration %s: %w", record.Version, err)
		}
	}
	
	// 删除迁移记录表
	if err := m.db.Migrator().DropTable(&Migration{}); err != nil {
		return fmt.Errorf("failed to drop migrations table: %w", err)
	}
	
	log.Println("All migrations have been reset")
	return nil
}

// Fresh 重新运行所有迁移
func (m *Migrator) Fresh() error {
	log.Println("Running fresh migration...")
	
	// 重置所有迁移
	if err := m.Reset(); err != nil {
		return fmt.Errorf("failed to reset migrations: %w", err)
	}
	
	// 重新初始化
	if err := m.Init(); err != nil {
		return fmt.Errorf("failed to reinitialize: %w", err)
	}
	
	// 运行所有迁移
	if err := m.Migrate(); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}
	
	log.Println("Fresh migration completed successfully")
	return nil
}
