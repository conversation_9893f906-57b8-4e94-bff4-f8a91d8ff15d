package migrations

import (
	"erp/backend/internal/models"
	"gorm.io/gorm"
)

// Migration001CreateInitialTables 创建初始表结构
func Migration001CreateInitialTables(db *gorm.DB) error {
	// 创建所有表
	err := db.AutoMigrate(
		&models.Tenant{},
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
		&models.UserSession{},
		&models.LoginAttempt{},
	)
	if err != nil {
		return err
	}

	// 创建额外的索引
	if err := createAdditionalIndexes(db); err != nil {
		return err
	}

	// 插入初始数据
	if err := insertInitialData(db); err != nil {
		return err
	}

	return nil
}

// createAdditionalIndexes 创建额外的索引
func createAdditionalIndexes(db *gorm.DB) error {
	// 租户表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tenants_code ON tenants(code) WHERE deleted_at IS NULL").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tenants_status_active ON tenants(status) WHERE status = 1 AND deleted_at IS NULL").Error; err != nil {
		return err
	}

	// 用户表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_users_tenant_status ON users(tenant_id, status) WHERE deleted_at IS NULL").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_users_email_active ON users(email) WHERE status = 1 AND deleted_at IS NULL").Error; err != nil {
		return err
	}

	// 角色表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_roles_tenant_code ON roles(tenant_id, code) WHERE deleted_at IS NULL").Error; err != nil {
		return err
	}

	// 会话表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_user_sessions_user_expires ON user_sessions(user_id, expires_at)").Error; err != nil {
		return err
	}

	// 登录尝试表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_time ON login_attempts(ip_address, created_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_login_attempts_username_time ON login_attempts(username, created_at)").Error; err != nil {
		return err
	}

	return nil
}

// insertInitialData 插入初始数据
func insertInitialData(db *gorm.DB) error {
	// 插入系统权限
	if err := insertSystemPermissions(db); err != nil {
		return err
	}

	// 插入系统角色
	if err := insertSystemRoles(db); err != nil {
		return err
	}

	// 创建默认系统管理员
	if err := createDefaultSystemAdmin(db); err != nil {
		return err
	}

	return nil
}

// insertSystemPermissions 插入系统权限
func insertSystemPermissions(db *gorm.DB) error {
	permissions := []models.Permission{
		// 租户管理权限（系统管理员专用）
		{Name: "查看租户列表", Code: "tenant.list", Description: "查看所有租户信息", Module: "tenant", Resource: "tenant", Action: "read"},
		{Name: "创建租户", Code: "tenant.create", Description: "创建新租户", Module: "tenant", Resource: "tenant", Action: "create"},
		{Name: "编辑租户", Code: "tenant.update", Description: "编辑租户信息", Module: "tenant", Resource: "tenant", Action: "update"},
		{Name: "删除租户", Code: "tenant.delete", Description: "删除租户", Module: "tenant", Resource: "tenant", Action: "delete"},
		{Name: "租户状态管理", Code: "tenant.status", Description: "管理租户状态", Module: "tenant", Resource: "tenant", Action: "update"},

		// 用户管理权限
		{Name: "查看用户列表", Code: "user.list", Description: "查看用户列表", Module: "user", Resource: "user", Action: "read"},
		{Name: "创建用户", Code: "user.create", Description: "创建新用户", Module: "user", Resource: "user", Action: "create"},
		{Name: "编辑用户", Code: "user.update", Description: "编辑用户信息", Module: "user", Resource: "user", Action: "update"},
		{Name: "删除用户", Code: "user.delete", Description: "删除用户", Module: "user", Resource: "user", Action: "delete"},
		{Name: "重置密码", Code: "user.reset_password", Description: "重置用户密码", Module: "user", Resource: "user", Action: "update"},

		// 角色权限管理
		{Name: "查看角色列表", Code: "role.list", Description: "查看角色列表", Module: "role", Resource: "role", Action: "read"},
		{Name: "创建角色", Code: "role.create", Description: "创建新角色", Module: "role", Resource: "role", Action: "create"},
		{Name: "编辑角色", Code: "role.update", Description: "编辑角色信息", Module: "role", Resource: "role", Action: "update"},
		{Name: "删除角色", Code: "role.delete", Description: "删除角色", Module: "role", Resource: "role", Action: "delete"},
		{Name: "分配权限", Code: "role.assign_permission", Description: "为角色分配权限", Module: "role", Resource: "role", Action: "update"},
		{Name: "分配角色", Code: "user.assign_role", Description: "为用户分配角色", Module: "user", Resource: "user", Action: "update"},

		// 权限管理
		{Name: "查看权限列表", Code: "permission.list", Description: "查看权限列表", Module: "permission", Resource: "permission", Action: "read"},
		{Name: "创建权限", Code: "permission.create", Description: "创建新权限", Module: "permission", Resource: "permission", Action: "create"},
		{Name: "编辑权限", Code: "permission.update", Description: "编辑权限信息", Module: "permission", Resource: "permission", Action: "update"},
		{Name: "删除权限", Code: "permission.delete", Description: "删除权限", Module: "permission", Resource: "permission", Action: "delete"},

		// 系统管理权限
		{Name: "系统监控", Code: "system.monitor", Description: "查看系统监控信息", Module: "system", Resource: "system", Action: "read"},
		{Name: "系统配置", Code: "system.config", Description: "管理系统配置", Module: "system", Resource: "system", Action: "update"},
		{Name: "查看日志", Code: "system.logs", Description: "查看系统日志", Module: "system", Resource: "system", Action: "read"},
		{Name: "数据备份", Code: "system.backup", Description: "执行数据备份", Module: "system", Resource: "system", Action: "create"},

		// 基础权限（所有用户都应该有的）
		{Name: "查看个人信息", Code: "profile.view", Description: "查看个人资料", Module: "profile", Resource: "profile", Action: "read"},
		{Name: "编辑个人信息", Code: "profile.update", Description: "编辑个人资料", Module: "profile", Resource: "profile", Action: "update"},
		{Name: "修改密码", Code: "profile.change_password", Description: "修改个人密码", Module: "profile", Resource: "profile", Action: "update"},
	}

	for _, permission := range permissions {
		var count int64
		db.Model(&models.Permission{}).Where("code = ?", permission.Code).Count(&count)
		if count == 0 {
			if err := db.Create(&permission).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// insertSystemRoles 插入系统角色
func insertSystemRoles(db *gorm.DB) error {
	// 创建超级管理员角色
	var superAdminRole models.Role
	result := db.Where("code = ? AND tenant_id IS NULL", "super_admin").First(&superAdminRole)
	if result.Error == gorm.ErrRecordNotFound {
		superAdminRole = models.Role{
			TenantID:    nil,
			Name:        "超级管理员",
			Code:        "super_admin",
			Description: "系统超级管理员，拥有所有权限",
			RoleType:    models.RoleTypeSystem,
			IsDefault:   true,
		}
		if err := db.Create(&superAdminRole).Error; err != nil {
			return err
		}

		// 为超级管理员分配所有权限
		var permissions []models.Permission
		db.Find(&permissions)
		for _, permission := range permissions {
			rolePermission := models.RolePermission{
				RoleID:       superAdminRole.ID,
				PermissionID: permission.ID,
			}
			db.Create(&rolePermission)
		}
	}

	return nil
}

// createDefaultSystemAdmin 创建默认系统管理员
func createDefaultSystemAdmin(db *gorm.DB) error {
	var adminUser models.User
	result := db.Where("username = ? AND user_type = ?", "admin", models.UserTypeSystemAdmin).First(&adminUser)
	if result.Error == gorm.ErrRecordNotFound {
		adminUser = models.User{
			TenantID: nil,
			Username: "admin",
			Email:    "<EMAIL>",
			RealName: "系统管理员",
			UserType: models.UserTypeSystemAdmin,
			Status:   models.UserStatusActive,
		}
		
		// 设置默认密码
		if err := adminUser.SetPassword("admin123456"); err != nil {
			return err
		}

		if err := db.Create(&adminUser).Error; err != nil {
			return err
		}

		// 分配超级管理员角色
		var superAdminRole models.Role
		if err := db.Where("code = ? AND tenant_id IS NULL", "super_admin").First(&superAdminRole).Error; err == nil {
			userRole := models.UserRole{
				UserID: adminUser.ID,
				RoleID: superAdminRole.ID,
			}
			db.Create(&userRole)
		}
	}

	return nil
}

// Rollback001CreateInitialTables 回滚初始表创建
func Rollback001CreateInitialTables(db *gorm.DB) error {
	// 删除表（注意顺序，先删除有外键依赖的表）
	tables := []interface{}{
		&models.LoginAttempt{},
		&models.UserSession{},
		&models.RolePermission{},
		&models.UserRole{},
		&models.Permission{},
		&models.Role{},
		&models.User{},
		&models.Tenant{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			return err
		}
	}

	return nil
}
