#!/bin/bash

# ERP系统API测试脚本

BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api"

echo "=== ERP系统API测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 检查服务是否运行
echo "1. 检查服务状态..."
if curl -s "$BASE_URL/health" > /dev/null; then
    echo "✅ 服务正在运行"
else
    echo "❌ 服务未运行，请先启动服务"
    exit 1
fi

echo ""

# 测试健康检查
echo "2. 测试健康检查..."
curl -s "$BASE_URL/health" | jq '.' || echo "健康检查响应"

echo ""

# 测试系统管理员登录
echo "3. 测试系统管理员登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "user_type": "system"
  }')

echo "登录响应:"
echo "$LOGIN_RESPONSE" | jq '.' 2>/dev/null || echo "$LOGIN_RESPONSE"

# 提取访问令牌
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.access_token' 2>/dev/null)

if [ "$ACCESS_TOKEN" != "null" ] && [ "$ACCESS_TOKEN" != "" ]; then
    echo "✅ 登录成功，获取到访问令牌"
    
    echo ""
    echo "4. 测试获取用户信息..."
    curl -s -X GET "$API_BASE/auth/profile" \
      -H "Authorization: Bearer $ACCESS_TOKEN" | jq '.' 2>/dev/null || echo "获取用户信息响应"
    
    echo ""
    echo "5. 测试获取租户列表..."
    curl -s -X GET "$API_BASE/tenants" \
      -H "Authorization: Bearer $ACCESS_TOKEN" | jq '.' 2>/dev/null || echo "获取租户列表响应"
    
    echo ""
    echo "6. 测试获取用户列表..."
    curl -s -X GET "$API_BASE/users" \
      -H "Authorization: Bearer $ACCESS_TOKEN" | jq '.' 2>/dev/null || echo "获取用户列表响应"
    
    echo ""
    echo "7. 测试获取角色列表..."
    curl -s -X GET "$API_BASE/roles" \
      -H "Authorization: Bearer $ACCESS_TOKEN" | jq '.' 2>/dev/null || echo "获取角色列表响应"
    
else
    echo "❌ 登录失败，无法获取访问令牌"
    echo "这可能是因为："
    echo "- 数据库未初始化"
    echo "- 默认管理员账户未创建"
    echo "- 服务配置错误"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到错误，请检查："
echo "1. 数据库是否正常运行"
echo "2. 服务是否正确启动"
echo "3. 环境变量是否正确配置"
echo "4. 数据库是否已初始化并创建默认数据"
