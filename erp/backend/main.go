package main

import (
	"log"
	"net/http"
	"time"

	"erp/backend/internal/config"
	"erp/backend/internal/controller"
	"erp/backend/internal/database"
	"erp/backend/internal/middleware"
	"erp/backend/internal/routes"
	"erp/backend/internal/service"
	"erp/backend/internal/utils"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title ERP系统API文档
// @version 1.0
// @description 多租户ERP系统后端API接口文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	db, err := database.InitDB(cfg)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 运行数据库迁移
	if err := database.RunMigrations(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化JWT管理器
	jwtManager := utils.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.AccessTokenExpiry, cfg.JWT.RefreshTokenExpiry, cfg.JWT.Issuer)

	// 初始化Token黑名单
	blacklist := utils.NewMemoryTokenBlacklist()

	// 初始化服务层
	userService := service.NewUserService(db)
	roleService := service.NewRoleService(db)
	permissionService := service.NewPermissionService(db)
	tenantService := service.NewTenantService(db)

	// 认证服务配置
	authConfig := &service.AuthConfig{
		MaxLoginAttempts:      5,
		LockoutDuration:       time.Minute * 15,
		SessionTimeout:        time.Hour * 24,
		MaxConcurrentSessions: 3,
	}
	authService := service.NewAuthService(db, jwtManager, blacklist, authConfig)

	// 初始化控制器层
	userController := controller.NewUserController(userService)
	roleController := controller.NewRoleController(roleService)
	permissionController := controller.NewPermissionController(permissionService)
	tenantController := controller.NewTenantController(tenantService)
	authController := controller.NewAuthController(authService)

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	router := gin.New()

	// 添加中间件
	setupMiddleware(router, cfg)

	// 注册路由
	setupRoutes(router, authController, userController, roleController, permissionController, tenantController, jwtManager, blacklist)

	// 启动服务器
	server := &http.Server{
		Addr:           cfg.Server.Address,
		Handler:        router,
		ReadTimeout:    time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout:   time.Duration(cfg.Server.WriteTimeout) * time.Second,
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	log.Printf("服务器启动在 %s", cfg.Server.Address)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// setupMiddleware 设置中间件
func setupMiddleware(router *gin.Engine, cfg *config.Config) {
	// 日志中间件
	router.Use(gin.Logger())

	// 恢复中间件
	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(cors.New(cors.Config{
		AllowOrigins:     cfg.CORS.AllowOrigins,
		AllowMethods:     cfg.CORS.AllowMethods,
		AllowHeaders:     cfg.CORS.AllowHeaders,
		ExposeHeaders:    cfg.CORS.ExposeHeaders,
		AllowCredentials: cfg.CORS.AllowCredentials,
		MaxAge:           time.Duration(cfg.CORS.MaxAge) * time.Second,
	}))

	// 安全头中间件
	router.Use(middleware.SecurityHeaders())

	// 限流中间件
	router.Use(middleware.RateLimiter())

	// 请求ID中间件
	router.Use(middleware.RequestID())
}

// setupRoutes 设置路由
func setupRoutes(
	router *gin.Engine,
	authController *controller.AuthController,
	userController *controller.UserController,
	roleController *controller.RoleController,
	permissionController *controller.PermissionController,
	tenantController *controller.TenantController,
	jwtManager *utils.JWTManager,
	blacklist utils.TokenBlacklist,
) {
	// API版本组
	v1 := router.Group("/api/v1")
	{
		// 健康检查
		v1.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":    "ok",
				"timestamp": time.Now().Unix(),
				"version":   "1.0.0",
			})
		})
	}

	// 注册认证路由
	routes.RegisterAuthRoutes(router, authController, jwtManager, blacklist)

	// 注册用户管理路由
	routes.RegisterUserRoutes(router, userController, jwtManager, blacklist)

	// 注册角色权限路由
	routes.RegisterRoleRoutes(router, roleController, permissionController, jwtManager, blacklist)

	// 注册租户管理路由
	routes.RegisterTenantRoutes(router, tenantController, jwtManager, blacklist)

	// Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 静态文件服务（如果需要）
	router.Static("/static", "./static")

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		utils.NotFound(c)
	})
}
