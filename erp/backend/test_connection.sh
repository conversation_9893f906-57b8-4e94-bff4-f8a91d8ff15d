#!/bin/bash

# 测试Supabase连接
echo "=== Supabase连接测试 ==="

# 加载环境变量
source ./start-supabase.sh

echo "项目URL: $SUPABASE_URL"
echo "数据库主机: db.ibbvlzotljknwkrrnorl.supabase.co"

# 测试DNS解析
echo ""
echo "1. 测试DNS解析..."
nslookup db.ibbvlzotljknwkrrnorl.supabase.co

# 测试网络连接
echo ""
echo "2. 测试网络连接..."
nc -zv db.ibbvlzotljknwkrrnorl.supabase.co 5432 2>&1

# 测试HTTP API
echo ""
echo "3. 测试Supabase API..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" \
  -H "apikey: $SUPABASE_ANON_KEY" \
  "$SUPABASE_URL/rest/v1/"

# 测试PostgreSQL连接（如果psql可用）
echo ""
echo "4. 测试PostgreSQL连接..."
if command -v psql &> /dev/null; then
    echo "使用psql测试连接..."
    psql "$SUPABASE_DB_URL" -c "SELECT version();" 2>&1
else
    echo "psql未安装，跳过PostgreSQL连接测试"
fi

echo ""
echo "=== 连接测试完成 ==="
