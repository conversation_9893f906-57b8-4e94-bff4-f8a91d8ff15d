# ERP系统用户管理模块

基于Golang + Gin + GORM + PostgreSQL的多租户ERP系统用户管理模块。

## 功能特性

### 🏢 多租户架构
- 完整的租户隔离机制
- 租户级别的数据隔离
- 灵活的租户配置管理
- 租户用户数量限制

### 👥 用户管理
- 用户注册、登录、登出
- 用户信息管理（增删改查）
- 用户状态管理（正常、暂停、禁用）
- 用户批量导入/导出
- 用户头像上传

### 🔐 认证授权
- JWT令牌认证
- 访问令牌 + 刷新令牌机制
- 令牌黑名单管理
- 会话管理
- 登录尝试记录

### 🛡️ 角色权限
- 基于角色的访问控制(RBAC)
- 角色管理（增删改查）
- 权限管理（增删改查）
- 角色权限分配
- 用户角色分配

### 🔒 安全特性
- 密码加密存储
- 登录失败锁定
- 并发会话限制
- 安全头设置
- 请求限流
- CORS跨域配置

## 技术栈

### 后端
- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **ORM**: GORM v2
- **数据库**: PostgreSQL 15+ / Supabase
- **缓存**: Redis 7+ (可选)
- **认证**: JWT
- **文档**: Swagger/OpenAPI
- **云服务**: Supabase (可选)

### 前端 (计划中)
- **框架**: Vue 3
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router

## 快速开始

### 1. 环境要求
- Go 1.21+
- Docker & Docker Compose
- Git

### 2. 克隆项目
```bash
git clone <repository-url>
cd erp
```

### 3. 启动数据库
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 查看服务状态
docker-compose ps
```

### 4. 启动后端服务

#### 方式一：使用传统PostgreSQL
```bash
cd backend

# 编译项目
go build -o main .

# 启动服务
./start.sh
```

#### 方式二：使用Supabase（推荐）
```bash
cd backend

# 1. 创建Supabase项目
# 访问 https://supabase.com 创建新项目

# 2. 配置环境变量
# 编辑 start-supabase.sh 文件，设置：
# - SUPABASE_URL: 项目URL
# - SUPABASE_SERVICE_KEY: 服务密钥
# - SUPABASE_DB_URL: 数据库连接字符串

# 3. 启动服务
./start-supabase.sh
```

### 5. 访问服务
- **API服务**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger/index.html
- **数据库管理**: http://localhost:5050 (pgAdmin)
  - 邮箱: <EMAIL>
  - 密码: admin123

## API文档

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌
- `POST /api/auth/change-password` - 修改密码
- `GET /api/auth/profile` - 获取用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户

### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `GET /api/roles/:id` - 获取角色详情
- `PUT /api/roles/:id` - 更新角色
- `DELETE /api/roles/:id` - 删除角色

### 租户管理
- `GET /api/tenants` - 获取租户列表
- `POST /api/tenants` - 创建租户
- `GET /api/tenants/:id` - 获取租户详情
- `PUT /api/tenants/:id` - 更新租户
- `DELETE /api/tenants/:id` - 删除租户

## 配置说明

### 环境变量
```bash
# 服务器配置
SERVER_ADDRESS=":8080"
SERVER_MODE="debug"

# 数据库配置
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="password"
DB_NAME="erp_db"

# JWT配置
JWT_SECRET_KEY="your-secret-key"
JWT_ACCESS_TOKEN_EXPIRY="3600"
JWT_REFRESH_TOKEN_EXPIRY="604800"
```

## 开发指南

### 项目结构
```
erp/
├── backend/                 # 后端代码
│   ├── cmd/                # 命令行工具
│   ├── internal/           # 内部包
│   │   ├── config/        # 配置管理
│   │   ├── controller/    # 控制器
│   │   ├── dto/           # 数据传输对象
│   │   ├── middleware/    # 中间件
│   │   ├── models/        # 数据模型
│   │   ├── routes/        # 路由定义
│   │   ├── service/       # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── docs/              # API文档
│   └── main.go            # 程序入口
├── frontend/               # 前端代码 (计划中)
├── scripts/                # 脚本文件
├── docs/                   # 项目文档
└── docker-compose.yml      # Docker配置
```

### 数据库设计
- **多租户隔离**: 所有业务表都包含tenant_id字段
- **软删除**: 支持软删除机制
- **审计字段**: 创建时间、更新时间、创建人、更新人
- **索引优化**: 关键字段建立索引

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t erp-backend .

# 启动完整服务
docker-compose up -d
```

### 生产环境配置
1. 修改JWT密钥
2. 配置数据库连接
3. 设置Redis缓存
4. 配置HTTPS
5. 设置日志级别

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请提交Issue或联系开发团队。

## 开发状态

✅ **Phase 1: 项目初始化和数据库设计** - 已完成
- ✅ 项目结构创建
- ✅ 多租户数据库设计
- ✅ GORM模型创建
- ✅ 数据库迁移系统
- ✅ 配置管理系统

✅ **Phase 2: 后端API开发** - 已完成
- ✅ 用户管理API
- ✅ 角色权限管理API
- ✅ 租户管理API
- ✅ 认证授权系统
- ✅ 中间件系统
- ✅ 应用程序编译成功

🔄 **Phase 3: 前端界面开发** - 计划中
- Vue 3 + Element Plus界面
- 用户管理界面
- 角色权限管理界面
- 租户管理界面

🔄 **Phase 4: 集成测试和优化** - 计划中
- API功能测试
- 多租户数据隔离测试
- 性能优化
- 安全测试
