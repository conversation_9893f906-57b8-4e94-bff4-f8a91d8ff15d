# 多租户ERP系统用户管理模块

## 项目概述

这是一个多租户ERP系统的用户管理模块，支持租户用户和系统管理员用户的完整管理功能。

## 技术栈

### 后端
- **语言**: Golang
- **框架**: Gin
- **ORM**: GORM
- **数据库**: PostgreSQL
- **认证**: JWT Token

### 前端
- **框架**: Vue 3
- **UI库**: Element Plus
- **构建工具**: Vite

## 多租户架构

### 租户用户（Tenant Users）
- 每个租户独立管理自己的用户
- 租户用户只能访问本租户的数据和功能
- 实现租户级别的用户权限管理

### 系统管理员用户（System Admin Users）
- 系统级别的管理账号，独立于租户体系
- 主要功能：管理租户、监控系统、系统配置等
- 具有跨租户的管理权限

## 项目结构

```
erp/
├── backend/                 # 后端代码
│   ├── cmd/                # 应用程序入口
│   ├── internal/           # 内部包
│   │   ├── config/        # 配置管理
│   │   ├── models/        # 数据模型
│   │   ├── handlers/      # HTTP处理器
│   │   ├── middleware/    # 中间件
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── migrations/        # 数据库迁移
│   ├── docs/             # API文档
│   └── go.mod            # Go模块文件
├── frontend/              # 前端代码
│   ├── src/              # 源代码
│   │   ├── components/   # 组件
│   │   ├── views/        # 页面视图
│   │   ├── router/       # 路由配置
│   │   ├── store/        # 状态管理
│   │   ├── api/          # API接口
│   │   └── utils/        # 工具函数
│   ├── public/           # 静态资源
│   └── package.json      # 依赖配置
├── docs/                 # 项目文档
└── README.md            # 项目说明
```

## 核心功能

### 数据安全和租户隔离
- 实现严格的多租户数据隔离机制
- 所有数据操作都基于租户ID进行过滤
- JWT token包含租户信息和用户权限

### 用户认证和授权
- JWT token认证机制
- 基于角色的权限控制（RBAC）
- 支持租户级别和系统级别的权限管理

### 错误处理和日志
- 完整的错误处理机制
- 详细的操作日志记录
- 安全审计日志

## 开发进度

- [ ] 阶段1：项目初始化和数据库设计
- [ ] 阶段2：后端API开发
- [ ] 阶段3：前端界面开发
- [ ] 阶段4：集成测试和优化

## 快速开始

### 环境要求
- Go 1.19+
- Node.js 16+
- PostgreSQL 13+

### 安装和运行
详细的安装和运行说明将在开发完成后提供。

## 贡献指南

所有代码注释和文档使用中文，确保代码的可读性和维护性。
