# 多租户ERP系统数据库设计

## 设计原则

### 多租户数据隔离策略
- **行级安全（Row Level Security）**: 使用租户ID（tenant_id）字段实现数据隔离
- **系统管理员独立**: 系统管理员用户独立于租户体系，不受租户ID限制
- **数据安全**: 所有业务数据都必须包含租户ID，确保数据不会跨租户访问

### 命名规范
- 表名使用复数形式，如 `tenants`, `users`, `roles`
- 字段名使用下划线分隔，如 `created_at`, `tenant_id`
- 主键统一使用 `id`，外键使用 `表名_id` 格式

## 核心表结构

### 1. 租户表 (tenants)
存储租户（企业/组织）的基本信息

```sql
CREATE TABLE tenants (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '租户名称',
    code VARCHAR(100) UNIQUE NOT NULL COMMENT '租户代码，用于标识',
    domain VARCHAR(255) COMMENT '租户域名',
    logo_url VARCHAR(500) COMMENT '租户Logo URL',
    contact_email VARCHAR(255) COMMENT '联系邮箱',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    address TEXT COMMENT '地址',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-正常，2-暂停，3-禁用',
    max_users INTEGER DEFAULT 100 COMMENT '最大用户数限制',
    expired_at TIMESTAMP COMMENT '到期时间',
    settings JSONB COMMENT '租户配置信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP COMMENT '软删除时间'
);

-- 创建索引
CREATE INDEX idx_tenants_code ON tenants(code);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_deleted_at ON tenants(deleted_at);
```

### 2. 用户表 (users)
存储所有用户信息，包括租户用户和系统管理员

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT COMMENT '租户ID，系统管理员为NULL',
    username VARCHAR(100) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(50) COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    user_type SMALLINT DEFAULT 1 COMMENT '用户类型：1-租户用户，2-系统管理员',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-正常，2-暂停，3-禁用',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    email_verified_at TIMESTAMP COMMENT '邮箱验证时间',
    phone_verified_at TIMESTAMP COMMENT '手机验证时间',
    settings JSONB COMMENT '用户个人设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP COMMENT '软删除时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
```

### 3. 角色表 (roles)
存储角色信息，支持租户级别和系统级别的角色

```sql
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT COMMENT '租户ID，系统角色为NULL',
    name VARCHAR(100) NOT NULL COMMENT '角色名称',
    code VARCHAR(100) NOT NULL COMMENT '角色代码',
    description TEXT COMMENT '角色描述',
    role_type SMALLINT DEFAULT 1 COMMENT '角色类型：1-租户角色，2-系统角色',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认角色',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP COMMENT '软删除时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE(tenant_id, code) -- 同一租户内角色代码唯一
);

-- 创建索引
CREATE INDEX idx_roles_tenant_id ON roles(tenant_id);
CREATE INDEX idx_roles_code ON roles(code);
CREATE INDEX idx_roles_role_type ON roles(role_type);
CREATE INDEX idx_roles_status ON roles(status);
CREATE INDEX idx_roles_deleted_at ON roles(deleted_at);
```

### 4. 权限表 (permissions)
存储系统所有权限定义

```sql
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限代码',
    description TEXT COMMENT '权限描述',
    module VARCHAR(100) COMMENT '所属模块',
    resource VARCHAR(100) COMMENT '资源标识',
    action VARCHAR(50) COMMENT '操作类型：create,read,update,delete',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_permissions_code ON permissions(code);
CREATE INDEX idx_permissions_module ON permissions(module);
CREATE INDEX idx_permissions_status ON permissions(status);
```

### 5. 用户角色关联表 (user_roles)
用户和角色的多对多关联关系

```sql
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    assigned_by BIGINT COMMENT '分配者用户ID',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP COMMENT '过期时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(user_id, role_id) -- 同一用户不能重复分配同一角色
);

-- 创建索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
```

### 6. 角色权限关联表 (role_permissions)
角色和权限的多对多关联关系

```sql
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    granted_by BIGINT COMMENT '授权者用户ID',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(role_id, permission_id) -- 同一角色不能重复分配同一权限
);

-- 创建索引
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);
```

### 7. 用户会话表 (user_sessions)
存储用户登录会话信息，用于JWT token管理

```sql
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token_id VARCHAR(255) UNIQUE NOT NULL COMMENT 'JWT Token ID',
    device_info TEXT COMMENT '设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token_id ON user_sessions(token_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
```

## 初始数据

### 系统默认权限
系统启动时需要初始化的基础权限：

```sql
-- 租户管理权限（系统管理员专用）
INSERT INTO permissions (name, code, description, module, resource, action) VALUES
('查看租户列表', 'tenant.list', '查看所有租户信息', 'tenant', 'tenant', 'read'),
('创建租户', 'tenant.create', '创建新租户', 'tenant', 'tenant', 'create'),
('编辑租户', 'tenant.update', '编辑租户信息', 'tenant', 'tenant', 'update'),
('删除租户', 'tenant.delete', '删除租户', 'tenant', 'tenant', 'delete'),
('租户状态管理', 'tenant.status', '管理租户状态', 'tenant', 'tenant', 'update');

-- 用户管理权限
INSERT INTO permissions (name, code, description, module, resource, action) VALUES
('查看用户列表', 'user.list', '查看用户列表', 'user', 'user', 'read'),
('创建用户', 'user.create', '创建新用户', 'user', 'user', 'create'),
('编辑用户', 'user.update', '编辑用户信息', 'user', 'user', 'update'),
('删除用户', 'user.delete', '删除用户', 'user', 'user', 'delete'),
('重置密码', 'user.reset_password', '重置用户密码', 'user', 'user', 'update');

-- 角色权限管理
INSERT INTO permissions (name, code, description, module, resource, action) VALUES
('查看角色列表', 'role.list', '查看角色列表', 'role', 'role', 'read'),
('创建角色', 'role.create', '创建新角色', 'role', 'role', 'create'),
('编辑角色', 'role.update', '编辑角色信息', 'role', 'role', 'update'),
('删除角色', 'role.delete', '删除角色', 'role', 'role', 'delete'),
('分配权限', 'role.assign_permission', '为角色分配权限', 'role', 'role', 'update'),
('分配角色', 'user.assign_role', '为用户分配角色', 'user', 'user', 'update');
```

### 系统默认角色

```sql
-- 系统管理员角色
INSERT INTO roles (tenant_id, name, code, description, role_type, is_default) VALUES
(NULL, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 2, TRUE);

-- 租户默认角色（在创建租户时自动创建）
-- 这些角色会在每个租户创建时自动生成
```

## 数据隔离策略

### 1. 应用层隔离
- 所有涉及租户数据的查询都必须包含 `tenant_id` 条件
- 使用中间件自动注入租户ID过滤条件
- 系统管理员用户（user_type=2）不受租户ID限制

### 2. 数据库层面约束
- 使用外键约束确保数据一致性
- 使用唯一约束防止数据重复
- 使用软删除（deleted_at）保留数据历史

### 3. 安全考虑
- 密码使用bcrypt哈希存储
- JWT token包含用户ID、租户ID、角色信息
- 会话管理支持token撤销和过期控制
- 所有敏感操作记录操作日志

## 扩展性设计

### 1. 配置灵活性
- 租户和用户的settings字段使用JSONB存储自定义配置
- 支持动态权限扩展
- 支持租户级别的功能开关

### 2. 性能优化
- 合理的索引设计
- 分页查询支持
- 缓存策略（Redis）

### 3. 监控和审计
- 用户操作日志
- 登录安全日志
- 系统性能监控

## 实现文件说明

### 1. 数据模型文件
- `backend/internal/models/tenant.go` - 租户模型和相关请求响应结构
- `backend/internal/models/user.go` - 用户模型和相关请求响应结构
- `backend/internal/models/role.go` - 角色模型和用户角色关联模型
- `backend/internal/models/permission.go` - 权限模型和角色权限关联模型
- `backend/internal/models/user_session.go` - 用户会话模型和登录尝试记录

### 2. 数据库迁移文件
- `backend/migrations/migrator.go` - 迁移管理器，负责执行和管理数据库迁移
- `backend/migrations/001_create_initial_tables.go` - 初始表结构迁移
- `backend/cmd/migrate/main.go` - 数据库迁移命令行工具

### 3. 数据库配置
- `backend/internal/config/database.go` - 数据库连接配置和管理

## 使用说明

### 1. 环境变量配置
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_USERNAME=postgres
export DB_PASSWORD=your_password
export DB_DATABASE=erp_db
export DB_SSL_MODE=disable
export DB_TIMEZONE=Asia/Shanghai
```

### 2. 运行数据库迁移
```bash
# 进入后端目录
cd backend

# 运行所有迁移
go run cmd/migrate/main.go

# 查看迁移状态
go run cmd/migrate/main.go -action=status

# 回滚特定迁移
go run cmd/migrate/main.go -action=rollback -version=001

# 重置所有迁移
go run cmd/migrate/main.go -action=reset

# 重新运行所有迁移
go run cmd/migrate/main.go -action=fresh
```

### 3. 默认账户信息
系统初始化后会创建默认的系统管理员账户：
- 用户名：`admin`
- 密码：`admin123456`
- 邮箱：`<EMAIL>`
- 角色：超级管理员

**注意：生产环境中请立即修改默认密码！**

## 数据库表关系图

```
租户表 (tenants)
    ↓ (1:N)
用户表 (users)
    ↓ (M:N via user_roles)
角色表 (roles)
    ↓ (M:N via role_permissions)
权限表 (permissions)

用户表 (users)
    ↓ (1:N)
用户会话表 (user_sessions)

系统还包含：
- 登录尝试表 (login_attempts) - 用于安全监控
- 迁移记录表 (migrations) - 用于数据库版本管理
```

## 安全特性

1. **密码安全**：使用bcrypt哈希算法存储密码
2. **会话管理**：JWT token管理，支持会话过期和撤销
3. **登录保护**：记录登录尝试，支持账户锁定机制
4. **数据隔离**：严格的租户级数据隔离
5. **权限控制**：基于角色的访问控制（RBAC）
6. **审计日志**：记录关键操作和登录行为

## 下一步开发计划

1. **后端API开发**：实现RESTful API接口
2. **认证授权中间件**：JWT认证和权限验证
3. **多租户数据隔离中间件**：自动注入租户ID过滤
4. **前端界面开发**：Vue3 + Element Plus界面
5. **集成测试**：API测试和端到端测试
