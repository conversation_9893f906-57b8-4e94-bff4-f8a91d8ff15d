# ERP系统项目总结

## 项目概述

本项目是一个基于Golang + Gin + GORM的多租户ERP系统，集成了Supabase作为现代化的后端服务。系统采用微服务架构，支持用户管理、角色权限控制、多租户数据隔离等核心功能。

## 技术栈

### 后端技术
- **语言**: Go 1.21+
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: PostgreSQL / Supabase
- **认证**: JWT
- **文档**: Swagger
- **配置管理**: Viper
- **日志**: 标准库log

### 数据库选择
- **传统方案**: PostgreSQL + pgAdmin
- **现代方案**: Supabase (推荐)

## 项目结构

```
erp/
├── backend/                    # 后端代码
│   ├── cmd/                   # 命令行工具
│   │   ├── migrate/           # 数据库迁移
│   │   └── server/            # 服务器启动
│   ├── internal/              # 内部包
│   │   ├── config/            # 配置管理
│   │   ├── controller/        # 控制器层
│   │   ├── database/          # 数据库连接
│   │   ├── middleware/        # 中间件
│   │   ├── models/            # 数据模型
│   │   ├── routes/            # 路由配置
│   │   ├── service/           # 业务逻辑层
│   │   └── utils/             # 工具函数
│   ├── scripts/               # 脚本文件
│   ├── docs/                  # API文档
│   └── main.go               # 程序入口
├── docs/                      # 项目文档
│   ├── SUPABASE_SETUP.md     # Supabase配置指南
│   ├── SUPABASE_RLS.md       # RLS安全策略
│   └── PROJECT_SUMMARY.md    # 项目总结
└── README.md                 # 项目说明
```

## 核心功能

### 1. 多租户架构
- 租户数据完全隔离
- 支持租户级别的配置
- 自动租户上下文管理

### 2. 用户管理系统
- 用户注册、登录、登出
- 密码修改和重置
- 用户信息管理
- 用户状态控制

### 3. 角色权限系统
- 基于RBAC的权限控制
- 灵活的角色定义
- 细粒度权限管理
- 权限继承和组合

### 4. JWT认证
- 访问令牌和刷新令牌
- 令牌黑名单机制
- 自动令牌刷新
- 安全的令牌存储

### 5. Supabase集成
- 云数据库托管
- 文件存储服务
- 实时数据同步
- 内置认证服务
- Row Level Security (RLS)

## 数据库设计

### 核心表结构
1. **tenants** - 租户表
2. **users** - 用户表
3. **roles** - 角色表
4. **permissions** - 权限表
5. **user_roles** - 用户角色关联表
6. **role_permissions** - 角色权限关联表
7. **token_blacklist** - 令牌黑名单表

### 多租户设计
- 所有业务表都包含 `tenant_id` 字段
- 通过GORM Scopes自动过滤租户数据
- 支持RLS策略进行数据库级别隔离

## API接口

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/change-password` - 修改密码
- `GET /api/v1/auth/profile` - 获取用户信息

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/:id` - 获取用户详情
- `PUT /api/v1/users/:id` - 更新用户信息
- `DELETE /api/v1/users/:id` - 删除用户

### 角色管理
- `GET /api/v1/roles` - 获取角色列表
- `POST /api/v1/roles` - 创建角色
- `GET /api/v1/roles/:id` - 获取角色详情
- `PUT /api/v1/roles/:id` - 更新角色信息
- `DELETE /api/v1/roles/:id` - 删除角色

### 权限管理
- `GET /api/v1/permissions` - 获取权限列表
- `POST /api/v1/permissions` - 创建权限
- `GET /api/v1/permissions/:id` - 获取权限详情
- `PUT /api/v1/permissions/:id` - 更新权限信息
- `DELETE /api/v1/permissions/:id` - 删除权限

### 文件管理 (Supabase)
- `POST /api/v1/files/upload` - 上传文件
- `GET /api/v1/files/url` - 获取文件URL
- `GET /api/v1/files` - 列出文件
- `DELETE /api/v1/files` - 删除文件

## 部署方式

### 方式一：传统PostgreSQL
```bash
# 启动数据库服务
docker-compose up -d

# 运行数据库迁移
cd backend
go run cmd/migrate/main.go

# 启动应用
go run main.go
```

### 方式二：Supabase (推荐)
```bash
# 配置环境变量
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_KEY="your-service-key"
export SUPABASE_DB_URL="postgresql://..."

# 启动应用
cd backend
./start-supabase.sh
```

## 测试和验证

### 功能测试
```bash
# 数据库迁移测试
./test_migration.sh

# Supabase集成测试
./test_supabase.sh

# 完整功能演示
./demo_supabase_features.sh
```

### API测试
- Swagger文档: http://localhost:8080/swagger/index.html
- 健康检查: http://localhost:8080/health
- API基础路径: http://localhost:8080/api/v1/

## 安全特性

### 1. 数据安全
- 密码哈希存储 (bcrypt)
- JWT令牌认证
- 令牌黑名单机制
- 多租户数据隔离

### 2. API安全
- 请求参数验证
- 权限中间件
- 跨域资源共享 (CORS)
- 请求频率限制

### 3. 数据库安全
- Row Level Security (RLS)
- 参数化查询
- 连接池管理
- 事务支持

## 性能优化

### 1. 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 缓存策略

### 2. 应用优化
- 中间件优化
- 响应压缩
- 静态资源缓存
- 异步处理

## 监控和日志

### 1. 应用监控
- 健康检查端点
- 性能指标收集
- 错误追踪
- 资源使用监控

### 2. 日志管理
- 结构化日志
- 日志级别控制
- 日志轮转
- 集中日志收集

## 扩展功能

### 已实现
- ✅ 多租户用户管理
- ✅ RBAC权限系统
- ✅ JWT认证
- ✅ Supabase集成
- ✅ 文件存储服务
- ✅ API文档生成

### 计划中
- 🔄 实时通知系统
- 🔄 数据导入导出
- 🔄 审计日志
- 🔄 工作流引擎
- 🔄 报表系统
- 🔄 移动端API

## 开发指南

### 环境要求
- Go 1.21+
- PostgreSQL 13+ 或 Supabase账户
- Git

### 开发流程
1. 克隆项目
2. 配置环境变量
3. 运行数据库迁移
4. 启动开发服务器
5. 运行测试

### 代码规范
- 使用中文注释
- 遵循Go代码规范
- 编写单元测试
- 更新API文档

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request
5. 代码审查
6. 合并代码

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件
- 技术交流群

---

**项目状态**: 🚀 生产就绪
**最后更新**: 2024年12月
**版本**: v1.0.0
