# Supabase 配置指南

本指南将帮助您配置Supabase作为ERP系统的后端数据库服务。

## 什么是Supabase？

Supabase是一个开源的Firebase替代方案，提供：
- 托管的PostgreSQL数据库
- 实时订阅功能
- 内置身份验证
- 自动生成的API
- 文件存储
- 边缘函数

## 为什么选择Supabase？

相比传统PostgreSQL部署，Supabase提供：

### 优势
- ✅ **零运维**: 无需管理数据库服务器
- ✅ **自动备份**: 内置数据备份和恢复
- ✅ **实时功能**: 支持实时数据同步
- ✅ **安全性**: 内置Row Level Security (RLS)
- ✅ **扩展性**: 自动扩容和负载均衡
- ✅ **开发友好**: 提供直观的管理界面
- ✅ **成本效益**: 免费层足够开发和小型项目使用

### 适用场景
- 快速原型开发
- 中小型企业应用
- 需要实时功能的应用
- 团队缺乏数据库运维经验
- 希望专注于业务逻辑开发

## 步骤1: 创建Supabase项目

### 1.1 注册账号
1. 访问 [https://supabase.com](https://supabase.com)
2. 点击 "Start your project" 
3. 使用GitHub、Google或邮箱注册账号

### 1.2 创建新项目
1. 登录后点击 "New Project"
2. 选择组织（个人或团队）
3. 填写项目信息：
   - **Name**: `erp-system` (或您喜欢的名称)
   - **Database Password**: 设置一个强密码（请记住此密码）
   - **Region**: 选择 `Southeast Asia (Singapore)` (距离中国最近)
4. 点击 "Create new project"
5. 等待项目创建完成（通常需要2-3分钟）

## 步骤2: 获取配置信息

项目创建完成后，您需要获取以下配置信息：

### 2.1 获取API密钥
1. 在项目仪表板中，点击左侧菜单的 "Settings"
2. 选择 "API" 选项卡
3. 复制以下信息：
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon public**: 匿名公钥（客户端使用）
   - **service_role**: 服务密钥（后端使用，保密！）

### 2.2 获取数据库连接字符串
1. 在 "Settings" 中选择 "Database" 选项卡
2. 在 "Connection string" 部分找到 "Nodejs" 连接字符串
3. 复制类似这样的字符串：
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
   ```
4. 将 `[YOUR-PASSWORD]` 替换为您在创建项目时设置的数据库密码

## 步骤3: 配置环境变量

### 3.1 编辑配置文件
编辑 `backend/start-supabase.sh` 文件，替换以下变量：

```bash
# Supabase配置
export SUPABASE_ENABLED="true"
export SUPABASE_URL="https://your-project-ref.supabase.co"
export SUPABASE_ANON_KEY="your-anon-key-here"
export SUPABASE_SERVICE_KEY="your-service-key-here"
export SUPABASE_DB_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

### 3.2 配置示例
假设您的项目信息如下：
- Project URL: `https://abcdefgh.supabase.co`
- Service Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- Database Password: `mySecretPassword123`

则配置应该是：
```bash
export SUPABASE_URL="https://abcdefgh.supabase.co"
export SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
export SUPABASE_DB_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## 步骤4: 测试配置

### 4.1 运行测试脚本
```bash
cd backend

# 设置环境变量（使用您的实际值）
export SUPABASE_URL="https://your-project-ref.supabase.co"
export SUPABASE_SERVICE_KEY="your-service-key"
export SUPABASE_DB_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# 运行测试
./test_supabase.sh
```

### 4.2 预期输出
如果配置正确，您应该看到：
```
=== ERP系统 Supabase集成测试 ===

1. 检查环境变量配置...
✅ SUPABASE_URL: 已设置
✅ SUPABASE_SERVICE_KEY: 已设置
✅ SUPABASE_DB_URL: 已设置

2. 测试Supabase API连接...
✅ Supabase API连接成功

3. 测试PostgreSQL数据库连接...
✅ PostgreSQL数据库连接成功

...

🎉 所有测试通过！Supabase集成配置正确。
```

## 步骤5: 启动应用程序

配置完成后，使用以下命令启动应用程序：

```bash
cd backend
./start-supabase.sh
```

## 常见问题

### Q1: 连接超时或失败
**原因**: 网络问题或配置错误
**解决方案**:
1. 检查网络连接
2. 确认Supabase项目状态正常
3. 验证URL和密钥是否正确复制

### Q2: 数据库密码错误
**原因**: 数据库连接字符串中的密码不正确
**解决方案**:
1. 在Supabase项目设置中重置数据库密码
2. 更新 `SUPABASE_DB_URL` 中的密码部分

### Q3: API密钥无效
**原因**: 使用了错误的API密钥
**解决方案**:
1. 确保使用 `service_role` 密钥而不是 `anon` 密钥
2. 重新复制密钥，确保没有多余的空格

### Q4: 项目无法访问
**原因**: 项目可能被暂停或删除
**解决方案**:
1. 登录Supabase控制台检查项目状态
2. 如果项目被暂停，按照提示恢复

## 安全注意事项

1. **保护服务密钥**: 永远不要在客户端代码中暴露 `service_role` 密钥
2. **使用环境变量**: 不要在代码中硬编码密钥
3. **定期轮换密钥**: 定期更新API密钥
4. **启用RLS**: 在生产环境中启用Row Level Security
5. **监控使用情况**: 定期检查API使用情况和异常访问

## 下一步

配置完成后，您可以：
1. 使用Supabase控制台管理数据
2. 设置实时订阅功能
3. 配置Row Level Security策略
4. 集成Supabase Storage用于文件上传
5. 使用Supabase Auth进行用户认证

更多高级功能请参考 [Supabase官方文档](https://supabase.com/docs)。
