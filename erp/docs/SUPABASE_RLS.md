# Supabase Row Level Security (RLS) 配置指南

Row Level Security (RLS) 是PostgreSQL的一个强大功能，允许您在数据库级别控制用户对行数据的访问权限。在多租户ERP系统中，RLS确保每个租户只能访问自己的数据。

## 什么是RLS？

RLS允许您定义策略来控制哪些用户可以访问表中的哪些行。这在多租户应用中特别有用，因为它提供了数据隔离的额外安全层。

## 为什么在ERP系统中使用RLS？

### 优势
- 🔒 **数据安全**: 在数据库级别强制执行租户隔离
- 🛡️ **防止数据泄露**: 即使应用代码有bug，也不会泄露其他租户的数据
- ⚡ **性能优化**: 数据库级别的过滤比应用级别更高效
- 🔧 **简化代码**: 减少应用层的权限检查逻辑
- 📊 **审计友好**: 所有访问控制都在数据库层面记录

## 在Supabase中启用RLS

### 1. 启用表的RLS

对于每个需要租户隔离的表，执行以下SQL：

```sql
-- 启用用户表的RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 启用角色表的RLS
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

-- 启用权限表的RLS
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;

-- 启用用户角色关联表的RLS
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- 启用角色权限关联表的RLS
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;

-- 启用租户表的RLS（如果需要）
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
```

### 2. 创建RLS策略

#### 用户表策略
```sql
-- 用户只能查看同一租户的用户
CREATE POLICY "用户只能查看同租户用户" ON users
    FOR SELECT
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);

-- 用户只能插入到自己的租户
CREATE POLICY "用户只能在自己租户创建用户" ON users
    FOR INSERT
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

-- 用户只能更新同租户的用户
CREATE POLICY "用户只能更新同租户用户" ON users
    FOR UPDATE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint)
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

-- 用户只能删除同租户的用户
CREATE POLICY "用户只能删除同租户用户" ON users
    FOR DELETE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);
```

#### 角色表策略
```sql
-- 角色表的RLS策略
CREATE POLICY "角色租户隔离_查询" ON roles
    FOR SELECT
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "角色租户隔离_插入" ON roles
    FOR INSERT
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "角色租户隔离_更新" ON roles
    FOR UPDATE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint)
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "角色租户隔离_删除" ON roles
    FOR DELETE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);
```

#### 权限表策略
```sql
-- 权限表的RLS策略
CREATE POLICY "权限租户隔离_查询" ON permissions
    FOR SELECT
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "权限租户隔离_插入" ON permissions
    FOR INSERT
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "权限租户隔离_更新" ON permissions
    FOR UPDATE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint)
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::bigint);

CREATE POLICY "权限租户隔离_删除" ON permissions
    FOR DELETE
    USING (tenant_id = current_setting('app.current_tenant_id')::bigint);
```

### 3. 在应用中设置租户上下文

在Go应用中，您需要在每个数据库连接中设置当前租户ID：

```go
// 在中间件中设置租户上下文
func SetTenantContext(db *gorm.DB, tenantID uint) *gorm.DB {
    return db.Exec("SELECT set_config('app.current_tenant_id', ?, true)", tenantID)
}

// 在服务层使用
func (s *UserService) GetUsers(tenantID uint) ([]models.User, error) {
    var users []models.User
    
    // 设置租户上下文
    db := SetTenantContext(s.db, tenantID)
    
    // 查询用户（RLS会自动过滤）
    err := db.Find(&users).Error
    return users, err
}
```

## 高级RLS策略

### 1. 基于用户角色的策略

```sql
-- 创建函数检查用户权限
CREATE OR REPLACE FUNCTION has_permission(permission_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_roles ur
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = current_setting('app.current_user_id')::bigint
        AND p.name = permission_name
        AND ur.tenant_id = current_setting('app.current_tenant_id')::bigint
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 使用权限检查的策略
CREATE POLICY "管理员可以查看所有用户" ON users
    FOR SELECT
    USING (
        tenant_id = current_setting('app.current_tenant_id')::bigint
        AND has_permission('user.view_all')
    );
```

### 2. 时间基础的策略

```sql
-- 只允许访问最近30天的数据
CREATE POLICY "只能访问最近数据" ON some_table
    FOR SELECT
    USING (
        tenant_id = current_setting('app.current_tenant_id')::bigint
        AND created_at >= NOW() - INTERVAL '30 days'
    );
```

### 3. 部门级别的策略

```sql
-- 用户只能访问自己部门的数据
CREATE POLICY "部门数据隔离" ON employees
    FOR SELECT
    USING (
        tenant_id = current_setting('app.current_tenant_id')::bigint
        AND department_id IN (
            SELECT department_id FROM user_departments
            WHERE user_id = current_setting('app.current_user_id')::bigint
        )
    );
```

## 在Supabase控制台中管理RLS

### 1. 通过SQL编辑器
1. 登录Supabase控制台
2. 选择您的项目
3. 点击左侧菜单的"SQL Editor"
4. 粘贴上述SQL语句并执行

### 2. 通过表编辑器
1. 在"Table Editor"中选择表
2. 点击表设置（齿轮图标）
3. 启用"Enable Row Level Security"
4. 点击"Add Policy"创建策略

## 测试RLS策略

### 1. 创建测试用户

```sql
-- 创建测试租户
INSERT INTO tenants (name, code, status) VALUES 
('测试租户1', 'TEST001', 'active'),
('测试租户2', 'TEST002', 'active');

-- 创建测试用户
INSERT INTO users (username, email, password_hash, tenant_id) VALUES
('user1', '<EMAIL>', 'hash1', 1),
('user2', '<EMAIL>', 'hash2', 2);
```

### 2. 测试策略

```sql
-- 设置租户1的上下文
SELECT set_config('app.current_tenant_id', '1', true);

-- 查询用户（应该只返回租户1的用户）
SELECT * FROM users;

-- 设置租户2的上下文
SELECT set_config('app.current_tenant_id', '2', true);

-- 查询用户（应该只返回租户2的用户）
SELECT * FROM users;
```

## 性能优化

### 1. 创建索引

```sql
-- 为tenant_id创建索引
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_roles_tenant_id ON roles(tenant_id);
CREATE INDEX idx_permissions_tenant_id ON permissions(tenant_id);
```

### 2. 使用部分索引

```sql
-- 为活跃用户创建部分索引
CREATE INDEX idx_users_active_tenant ON users(tenant_id, id) 
WHERE status = 'active';
```

## 监控和调试

### 1. 查看当前策略

```sql
-- 查看表的RLS策略
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'users';
```

### 2. 调试策略

```sql
-- 启用查询日志
SET log_statement = 'all';

-- 查看执行计划
EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM users;
```

## 注意事项

1. **性能影响**: RLS策略会影响查询性能，确保相关字段有适当的索引
2. **策略复杂性**: 避免过于复杂的策略，这可能导致难以调试的问题
3. **超级用户绕过**: 超级用户（如service_role）会绕过RLS策略
4. **测试覆盖**: 确保充分测试所有RLS策略
5. **文档维护**: 保持策略文档的更新

## 故障排除

### 常见问题

1. **策略不生效**: 检查是否正确设置了租户上下文
2. **性能问题**: 添加适当的索引
3. **权限错误**: 确保应用使用正确的数据库角色
4. **策略冲突**: 检查是否有多个策略相互冲突

通过正确配置RLS，您的多租户ERP系统将具有强大的数据安全保障。
