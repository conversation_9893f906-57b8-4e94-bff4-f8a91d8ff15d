# Supabase配置总结

## 🎉 自动配置完成

你的ERP系统已经成功集成了Supabase！以下是完整的配置信息：

## 📋 项目信息

| 配置项 | 值 |
|--------|-----|
| **项目ID** | `ibbvlzotljknwkrrnorl` |
| **项目名称** | `erp-system` |
| **区域** | `us-east-1` |
| **状态** | `ACTIVE_HEALTHY` ✅ |
| **创建时间** | `2025-06-26T03:25:45.953885Z` |

## 🔑 API密钥

### Anon Key (客户端使用)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.KgCOAfwrXc9MJnkxyY3TBuRn9lq95VYy4Jaz--j-z8s
```

### Service Role Key (服务端使用)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.qUjLXivsOAE-12z7Y2z5ArwABZp2am6GmD5LILtmsgk
```

## 🌐 连接信息

### Supabase URL
```
https://ibbvlzotljknwkrrnorl.supabase.co
```

### 数据库连接字符串
```
*****************************************************************************/postgres?sslmode=require
```

### 数据库主机信息
- **主机**: `db.ibbvlzotljknwkrrnorl.supabase.co`
- **端口**: `5432`
- **数据库**: `postgres`
- **用户名**: `postgres`
- **密码**: `ERPSystem2024!`

## 📊 数据库表结构

已成功创建以下表：

### 1. tenants (租户表)
- `id` - 主键
- `name` - 租户名称
- `code` - 租户代码 (唯一)
- `status` - 状态
- `settings` - JSON配置
- `created_at`, `updated_at` - 时间戳

### 2. users (用户表)
- `id` - 主键
- `tenant_id` - 租户ID (外键)
- `username` - 用户名 (唯一)
- `email` - 邮箱 (唯一)
- `password_hash` - 密码哈希
- `real_name` - 真实姓名
- `phone` - 电话
- `avatar` - 头像
- `user_type` - 用户类型
- `status` - 状态
- `last_login_at` - 最后登录时间
- `created_at`, `updated_at` - 时间戳

### 3. roles (角色表)
- `id` - 主键
- `tenant_id` - 租户ID (外键)
- `name` - 角色名称
- `code` - 角色代码
- `description` - 描述
- `status` - 状态
- `created_at`, `updated_at` - 时间戳

### 4. permissions (权限表)
- `id` - 主键
- `name` - 权限名称
- `code` - 权限代码 (唯一)
- `resource` - 资源
- `action` - 操作
- `description` - 描述
- `created_at`, `updated_at` - 时间戳

### 5. user_roles (用户角色关联表)
- `id` - 主键
- `user_id` - 用户ID (外键)
- `role_id` - 角色ID (外键)
- `created_at` - 创建时间

### 6. role_permissions (角色权限关联表)
- `id` - 主键
- `role_id` - 角色ID (外键)
- `permission_id` - 权限ID (外键)
- `created_at` - 创建时间

### 7. token_blacklist (令牌黑名单表)
- `id` - 主键
- `token_hash` - 令牌哈希 (唯一)
- `user_id` - 用户ID
- `expires_at` - 过期时间
- `created_at` - 创建时间

## 📝 示例数据

已插入以下示例数据：

### 租户数据
- **名称**: 示例企业
- **代码**: demo
- **状态**: 1 (启用)
- **设置**: `{"theme": "default", "language": "zh-CN"}`

### 权限数据
- 用户管理 (`user:manage`)
- 角色管理 (`role:manage`)
- 权限管理 (`permission:manage`)

## 🚀 启动应用

你的启动脚本 `start-supabase.sh` 已经自动配置完成，包含所有必要的环境变量：

```bash
cd erp/backend
./start-supabase.sh
```

## 🔧 管理面板

访问Supabase管理面板：
```
https://supabase.com/dashboard/project/ibbvlzotljknwkrrnorl
```

在这里你可以：
- 查看和编辑数据
- 配置Row Level Security (RLS)
- 管理存储桶
- 查看API日志
- 设置实时订阅

## ⚠️ 注意事项

### 网络连接问题
目前直接的PostgreSQL连接可能存在网络问题（IPv6连接失败）。建议：

1. **优先使用REST API**: Supabase的REST API工作正常
2. **配置网络**: 如需直接数据库连接，可能需要配置网络或使用VPN
3. **使用连接池**: 考虑使用Supabase的连接池功能

### 安全建议
1. **保护Service Key**: 不要在客户端代码中暴露Service Role Key
2. **配置RLS**: 为所有表配置Row Level Security策略
3. **定期轮换密钥**: 定期更新API密钥和数据库密码

## 📚 下一步

1. **配置RLS策略**: 参考 `docs/SUPABASE_RLS.md`
2. **测试API**: 使用 `test_supabase.sh` 脚本
3. **开发前端**: 集成Vue.js前端应用
4. **部署应用**: 配置生产环境

## 🎯 快速测试

运行以下命令测试配置：

```bash
# 测试环境变量
source ./start-supabase.sh
echo $SUPABASE_URL

# 测试API连接
curl -H "apikey: $SUPABASE_ANON_KEY" "$SUPABASE_URL/rest/v1/tenants"
```

---

**配置状态**: ✅ 完成  
**最后更新**: 2025-06-26  
**项目ID**: ibbvlzotljknwkrrnorl
