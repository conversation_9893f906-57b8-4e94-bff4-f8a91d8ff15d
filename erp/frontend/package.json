{"name": "@erp/root", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "pnpm --parallel --filter \"./packages/*\" dev", "build": "pnpm --filter \"./packages/*\" build", "build:shared": "pnpm --filter @erp/shared build", "build:admin": "pnpm --filter @erp/admin-portal build", "build:user": "pnpm --filter @erp/user-portal build", "dev:shared": "pnpm --filter @erp/shared dev", "dev:admin": "pnpm --filter @erp/admin-portal dev", "dev:user": "pnpm --filter @erp/user-portal dev", "type-check": "pnpm --filter \"./packages/*\" type-check", "clean": "pnpm --filter \"./packages/*\" clean"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vite-plugin-dts": "^4.0.0", "vue-tsc": "^2.2.10"}}