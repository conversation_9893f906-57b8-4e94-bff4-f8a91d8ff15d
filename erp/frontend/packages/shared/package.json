{"name": "@erp/shared", "version": "1.0.0", "description": "ERP系统共享组件和工具库", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"import": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./utils": {"import": "./dist/utils/index.js", "types": "./dist/utils/index.d.ts"}, "./stores": {"import": "./dist/stores/index.js", "types": "./dist/stores/index.d.ts"}, "./api": {"import": "./dist/api/index.js", "types": "./dist/api/index.d.ts"}, "./types": {"import": "./dist/types/index.js", "types": "./dist/types/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "type-check": "vue-tsc --noEmit"}, "peerDependencies": {"vue": "^3.4.0", "vue-router": "^4.0.0", "pinia": "^2.0.0", "element-plus": "^2.0.0", "axios": "^1.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "typescript": "^5.0.0", "vite": "^7.0.0", "vite-plugin-dts": "^4.0.0", "vue-tsc": "^2.0.0"}}