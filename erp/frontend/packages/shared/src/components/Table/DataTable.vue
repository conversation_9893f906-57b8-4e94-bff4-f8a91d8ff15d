<template>
  <div class="data-table">
    <!-- 搜索栏 -->
    <div v-if="showSearch" class="search-bar">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item v-if="searchConfig.keyword" label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入关键词"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item v-if="searchConfig.status" label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in searchConfig.statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="searchConfig.userType" label="用户类型">
          <el-select
            v-model="searchForm.userType"
            placeholder="请选择用户类型"
            clearable
            style="width: 140px"
          >
            <el-option
              v-for="option in searchConfig.userTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 操作栏 -->
    <div v-if="showActions" class="action-bar">
      <div class="action-left">
        <slot name="actions" />
      </div>
      <div class="action-right">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 表格 -->
    <el-table
      :data="data"
      :loading="loading"
      stripe
      class="data-table-main"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        align="center"
      />
      
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :sortable="column.sortable"
        :formatter="column.formatter"
        show-overflow-tooltip
      >
        <template v-if="column.prop === 'actions'" #default="scope">
          <slot name="row-actions" :row="scope.row" :index="scope.$index" />
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div v-if="showPagination" class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { TableColumn, SearchParams } from '../../types'

interface SearchConfig {
  keyword?: boolean
  status?: boolean
  userType?: boolean
  statusOptions?: Array<{ label: string; value: any }>
  userTypeOptions?: Array<{ label: string; value: any }>
}

interface Props {
  data: any[]
  columns: TableColumn[]
  loading?: boolean
  showSearch?: boolean
  showActions?: boolean
  showSelection?: boolean
  showPagination?: boolean
  searchConfig?: SearchConfig
  total?: number
  pageSizes?: number[]
}

withDefaults(defineProps<Props>(), {
  loading: false,
  showSearch: true,
  showActions: true,
  showSelection: false,
  showPagination: true,
  searchConfig: () => ({ keyword: true, status: true }),
  total: 0,
  pageSizes: () => [10, 20, 50, 100]
})

const emit = defineEmits<{
  search: [params: SearchParams]
  refresh: []
  selectionChange: [selection: any[]]
  sizeChange: [size: number]
  currentChange: [page: number]
}>()

// 搜索表单
const searchForm = reactive<SearchParams>({
  keyword: '',
  status: undefined,
  userType: '',
  page: 1,
  pageSize: 20
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  searchForm.page = 1
  searchForm.pageSize = pageSize.value
  emit('search', { ...searchForm })
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'page') {
      (searchForm as any)[key] = 1
    } else if (key === 'pageSize') {
      (searchForm as any)[key] = 20
    } else {
      (searchForm as any)[key] = key === 'status' ? undefined : ''
    }
  })
  currentPage.value = 1
  pageSize.value = 20
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  emit('refresh')
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  emit('selectionChange', selection)
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchForm.pageSize = size
  emit('sizeChange', size)
  handleSearch()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchForm.page = page
  emit('currentChange', page)
  handleSearch()
}

// 监听页码变化
watch([currentPage, pageSize], () => {
  searchForm.page = currentPage.value
  searchForm.pageSize = pageSize.value
})
</script>

<style scoped>
.data-table {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-bar {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.action-left {
  display: flex;
  gap: 8px;
}

.action-right {
  display: flex;
  gap: 8px;
}

.data-table-main {
  width: 100%;
}

.data-table-main :deep(.el-table__row) {
  height: 64px;
}

.data-table-main :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.data-table-main :deep(.el-table__header) {
  font-weight: 600;
  color: #333;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>
