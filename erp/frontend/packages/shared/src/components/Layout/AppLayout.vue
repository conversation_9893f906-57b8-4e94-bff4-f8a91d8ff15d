<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '80px' : '280px'" class="sidebar">
      <div class="logo">
        <img v-if="!isCollapse" :src="logoUrl" :alt="appName" class="logo-img" />
        <span v-if="!isCollapse" class="logo-text">{{ appName }}</span>
        <img v-else :src="logoUrl" :alt="appName" class="logo-img-small" />
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        class="sidebar-menu"
        router
      >
        <template v-for="item in menuItems" :key="item.index">
          <el-menu-item 
            v-if="!item.children && hasMenuPermission(item)"
            :index="item.index"
          >
            <el-icon v-if="item.icon">
              <component :is="item.icon" />
            </el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
          
          <el-sub-menu 
            v-else-if="item.children && hasMenuPermission(item)"
            :index="item.index"
          >
            <template #title>
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              <span>{{ item.title }}</span>
            </template>
            <el-menu-item 
              v-for="child in item.children"
              :key="child.index"
              :index="child.index"
              v-show="hasMenuPermission(child)"
            >
              <el-icon v-if="child.icon">
                <component :is="child.icon" />
              </el-icon>
              <template #title>{{ child.title }}</template>
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/" v-if="breadcrumbs.length > 0">
            <el-breadcrumb-item 
              v-for="item in breadcrumbs"
              :key="item.title"
              :to="item.path ? { path: item.path } : undefined"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 租户信息 -->
          <div v-if="showTenantInfo && tenantName" class="tenant-info">
            <el-tag type="info" size="small">
              {{ tenantName }}
            </el-tag>
          </div>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar">
                {{ userDisplayName?.charAt(0) || 'U' }}
              </el-avatar>
              <span class="username">{{ userDisplayName }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item command="changePassword">
                  <el-icon><Key /></el-icon>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <slot />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { 
  House, User, UserFilled, Avatar, OfficeBuilding, 
  Expand, Fold, ArrowDown, Key, SwitchButton 
} from '@element-plus/icons-vue'
import type { MenuItem, BreadcrumbItem } from '../../types'

interface Props {
  menuItems: MenuItem[]
  appName?: string
  logoUrl?: string
  showTenantInfo?: boolean
  tenantName?: string
  userDisplayName?: string
  userAvatar?: string
  breadcrumbs?: BreadcrumbItem[]
  userRoles?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  appName: 'ERP系统',
  logoUrl: '/logo.svg',
  showTenantInfo: true,
  breadcrumbs: () => [],
  userRoles: () => []
})

const emit = defineEmits<{
  userCommand: [command: string]
}>()

const route = useRoute()
const isCollapse = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 检查菜单权限
const hasMenuPermission = (menuItem: MenuItem) => {
  if (!menuItem.roles || menuItem.roles.length === 0) {
    return true
  }
  return menuItem.roles.some(role => props.userRoles.includes(role))
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  emit('userCommand', command)
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  min-width: 1200px;
}

.sidebar {
  background: #001529;
  transition: width 0.3s;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-img {
  height: 32px;
  margin-right: 12px;
}

.logo-img-small {
  height: 32px;
}

.logo-text {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  background: transparent;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  color: rgba(255, 255, 255, 0.85);
  border-radius: 0;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: #1890ff;
  color: white;
}

.sidebar-menu :deep(.el-sub-menu__title) {
  height: 56px;
  line-height: 56px;
  color: rgba(255, 255, 255, 0.85);
}

.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 18px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.tenant-info {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.main-content {
  background: #f5f5f5;
  padding: 24px;
  overflow-y: auto;
}
</style>
