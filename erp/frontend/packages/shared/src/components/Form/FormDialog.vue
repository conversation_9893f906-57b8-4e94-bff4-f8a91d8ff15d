<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-width="labelWidth"
      class="form-dialog-content"
    >
      <slot :form-data="formData" />
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import type { FormRule } from '../../types'

interface Props {
  visible: boolean
  title: string
  formData: Record<string, any>
  rules?: Record<string, FormRule[]>
  loading?: boolean
  width?: string
  labelWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  width: '600px',
  labelWidth: '100px'
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  confirm: [formData: Record<string, any>]
  close: []
}>()

const formRef = ref<FormInstance>()

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('confirm', props.formData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 清除验证
const clearValidate = () => {
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 对话框关闭时清除验证状态
    setTimeout(() => {
      clearValidate()
    }, 200)
  }
})

// 暴露方法给父组件
defineExpose({
  resetForm,
  clearValidate
})
</script>

<style scoped>
.form-dialog-content {
  padding: 20px 0;
}

.form-dialog-content :deep(.el-form-item) {
  margin-bottom: 24px;
}

.form-dialog-content :deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

.form-dialog-content :deep(.el-input),
.form-dialog-content :deep(.el-select),
.form-dialog-content :deep(.el-textarea) {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
