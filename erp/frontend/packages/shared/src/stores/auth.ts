import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '../api'
import type { User, LoginForm, LoginResponse } from '../types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('auth_token') || '')
  const refreshToken = ref<string>(localStorage.getItem('refresh_token') || '')
  const user = ref<User | null>(null)
  const tenantId = ref<number | undefined>(undefined)
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRoles = computed(() => user.value?.roles || [])
  const isAdmin = computed(() => user.value?.userType === 'admin')
  const isTenantAdmin = computed(() => user.value?.userType === 'tenant_admin')
  const isUser = computed(() => user.value?.userType === 'user')
  
  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      let response: LoginResponse
      
      switch (loginForm.userType) {
        case 'tenant_admin':
          response = (await authAPI.tenantLogin(loginForm)) as any
          break
        case 'admin':
          response = (await authAPI.adminLogin(loginForm)) as any
          break
        default:
          response = (await authAPI.login(loginForm)) as any
      }
      
      // 保存token和用户信息
      if (response.token) {
        token.value = response.token
        refreshToken.value = response.refreshToken || ''
        user.value = response.user
        tenantId.value = response.user.tenantId
        
        // 保存到localStorage
        localStorage.setItem('auth_token', token.value)
        if (refreshToken.value) {
          localStorage.setItem('refresh_token', refreshToken.value)
        }
        localStorage.setItem('user_info', JSON.stringify(response.user))
        
        // 保存租户信息
        if (response.user.tenantId) {
          localStorage.setItem('tenant_id', response.user.tenantId.toString())
        }
        
        return response
      }
      
      throw new Error('登录响应格式错误')
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      refreshToken.value = ''
      user.value = null
      tenantId.value = undefined
      
      // 清除localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
      localStorage.removeItem('tenant_id')
    }
  }
  
  // 刷新token
  const refresh = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有refresh token')
      }
      
      const response = await authAPI.refreshToken(refreshToken.value) as any

      if (response.token) {
        token.value = response.token
        localStorage.setItem('auth_token', token.value)

        if (response.refresh_token) {
          refreshToken.value = response.refresh_token
          localStorage.setItem('refresh_token', refreshToken.value)
        }
        
        return response
      }
      
      throw new Error('刷新token失败')
    } catch (error) {
      console.error('刷新token失败:', error)
      await logout()
      throw error
    }
  }
  
  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const response = await authAPI.getProfile() as any
      user.value = response
      localStorage.setItem('user_info', JSON.stringify(response))
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }
  
  // 验证token
  const validateToken = async () => {
    try {
      await authAPI.validateToken()
      return true
    } catch (error) {
      console.error('token验证失败:', error)
      await logout()
      return false
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await authAPI.changePassword({
        old_password: oldPassword,
        new_password: newPassword
      })
      return true
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string) => {
    return user.value?.roles?.some(role => 
      role.permissions.includes(permission)
    ) || false
  }
  
  // 检查角色
  const hasRole = (roleName: string) => {
    return user.value?.roles?.some(role => role.name === roleName) || false
  }
  
  // 初始化（从localStorage恢复状态）
  const initialize = async () => {
    const storedUser = localStorage.getItem('user_info')
    const storedTenantId = localStorage.getItem('tenant_id')
    
    if (storedUser) {
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }
    
    if (storedTenantId) {
      tenantId.value = parseInt(storedTenantId)
    }
    
    if (token.value && !user.value) {
      try {
        await fetchUserProfile()
      } catch (error) {
        console.error('初始化用户信息失败:', error)
        await logout()
      }
    }
  }
  
  return {
    // 状态
    token,
    refreshToken,
    user,
    tenantId,
    
    // 计算属性
    isAuthenticated,
    userRoles,
    isAdmin,
    isTenantAdmin,
    isUser,
    
    // 方法
    login,
    logout,
    refresh,
    fetchUserProfile,
    validateToken,
    changePassword,
    hasPermission,
    hasRole,
    initialize
  }
})
