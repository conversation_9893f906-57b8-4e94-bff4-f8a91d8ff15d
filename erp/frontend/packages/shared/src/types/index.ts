// 导出所有类型定义
export * from './auth'

// 通用类型定义
export interface BaseEntity {
  id: number
  createdAt: string
  updatedAt: string
}

export interface SearchParams {
  keyword?: string
  status?: number
  userType?: string
  page?: number
  pageSize?: number
}

export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}

export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

export interface MenuItem {
  index: string
  title: string
  icon?: string
  children?: MenuItem[]
  roles?: string[]
}

export interface BreadcrumbItem {
  title: string
  path?: string
}
