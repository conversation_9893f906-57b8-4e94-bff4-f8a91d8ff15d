// 认证相关类型定义
export interface User {
  id: number
  username: string
  email: string
  realName: string
  userType: 'admin' | 'tenant_admin' | 'user'
  status: number
  tenantId?: number
  tenantName?: string
  roles?: Role[]
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: number
  name: string
  description: string
  permissions: string[]
}

export interface Tenant {
  id: number
  name: string
  code: string
  status: number
  userCount: number
  createdAt: string
  updatedAt: string
}

export interface LoginForm {
  username: string
  password: string
  userType?: 'admin' | 'tenant_admin' | 'user'
  tenantCode?: string
}

export interface AuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
  refreshToken: string | null
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: User
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  pageSize: number
}

export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}
