import api from './request'
import type { User, PaginationParams, PaginationResponse } from '../types'

export const userAPI = {
  // 获取用户列表
  getUsers: (params?: PaginationParams & { keyword?: string; status?: number; userType?: string }) => 
    api.get<PaginationResponse<User>>('/api/users', { params }),
  
  // 获取用户详情
  getUser: (id: string) => api.get<User>(`/api/users/${id}`),
  
  // 创建用户
  createUser: (data: Partial<User>) => api.post<User>('/api/users', data),
  
  // 更新用户
  updateUser: (id: string, data: Partial<User>) => api.put<User>(`/api/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: string) => api.delete(`/api/users/${id}`),
  
  // 更新用户状态
  updateUserStatus: (id: string, status: number) => 
    api.put(`/api/users/${id}/status`, { status }),
  
  // 分配角色
  assignRoles: (id: string, roleIds: string[]) => 
    api.put(`/api/users/${id}/roles`, { role_ids: roleIds }),
  
  // 重置密码
  resetPassword: (id: string) => api.post(`/api/users/${id}/reset-password`)
}
