import api from './request'
import type { Tenant, PaginationParams, PaginationResponse } from '../types'

export const tenantAPI = {
  // 获取租户列表
  getTenants: (params?: PaginationParams) => 
    api.get<PaginationResponse<Tenant>>('/api/tenants', { params }),
  
  // 获取租户详情
  getTenant: (id: string) => api.get<Tenant>(`/api/tenants/${id}`),
  
  // 创建租户
  createTenant: (data: Partial<Tenant>) => api.post<Tenant>('/api/tenants', data),
  
  // 更新租户
  updateTenant: (id: string, data: Partial<Tenant>) => api.put<Tenant>(`/api/tenants/${id}`, data),
  
  // 删除租户
  deleteTenant: (id: string) => api.delete(`/api/tenants/${id}`),
  
  // 更新租户状态
  updateTenantStatus: (id: string, status: number) => 
    api.put(`/api/tenants/${id}/status`, { status })
}
