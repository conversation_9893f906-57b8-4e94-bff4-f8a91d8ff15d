import api from './request'
import type { Role, PaginationParams, PaginationResponse } from '../types'

export const roleAPI = {
  // 获取角色列表
  getRoles: (params?: PaginationParams) => 
    api.get<PaginationResponse<Role>>('/api/roles', { params }),
  
  // 获取角色详情
  getRole: (id: string) => api.get<Role>(`/api/roles/${id}`),
  
  // 创建角色
  createRole: (data: Partial<Role>) => api.post<Role>('/api/roles', data),
  
  // 更新角色
  updateRole: (id: string, data: Partial<Role>) => api.put<Role>(`/api/roles/${id}`, data),
  
  // 删除角色
  deleteRole: (id: string) => api.delete(`/api/roles/${id}`),
  
  // 分配权限
  assignPermissions: (id: string, permissionIds: string[]) => 
    api.put(`/api/roles/${id}/permissions`, { permission_ids: permissionIds })
}
