import api from './request'
import type { LoginForm, LoginResponse, User } from '../types'

export const authAPI = {
  // 用户登录
  login: (data: LoginForm) => 
    api.post<LoginResponse>('/api/auth/login', data),
  
  // 租户登录
  tenantLogin: (data: LoginForm) => 
    api.post<LoginResponse>('/api/auth/tenant/login', data),
  
  // 系统管理员登录
  adminLogin: (data: LoginForm) => 
    api.post<LoginResponse>('/api/auth/admin/login', data),
  
  // 刷新token
  refreshToken: (refreshToken: string) => 
    api.post('/api/auth/refresh', { refresh_token: refreshToken }),
  
  // 验证token
  validateToken: () => api.post('/api/auth/validate'),
  
  // 登出
  logout: () => api.post('/api/auth/logout'),
  
  // 获取用户信息
  getProfile: () => api.get<User>('/api/auth/profile'),
  
  // 修改密码
  changePassword: (data: { old_password: string; new_password: string }) => 
    api.post('/api/auth/change-password', data)
}
