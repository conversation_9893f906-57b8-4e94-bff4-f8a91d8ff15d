// 本地存储工具函数

/**
 * localStorage 封装
 */
export const storage = {
  /**
   * 设置存储项
   */
  set(key: string, value: any) {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('存储数据失败:', error)
    }
  },

  /**
   * 获取存储项
   */
  get<T = any>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return defaultValue || null
      }
      return JSON.parse(item)
    } catch (error) {
      console.error('读取存储数据失败:', error)
      return defaultValue || null
    }
  },

  /**
   * 移除存储项
   */
  remove(key: string) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('移除存储数据失败:', error)
    }
  },

  /**
   * 清空所有存储
   */
  clear() {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  },

  /**
   * 检查是否存在某个key
   */
  has(key: string): boolean {
    return localStorage.getItem(key) !== null
  }
}

/**
 * sessionStorage 封装
 */
export const sessionStorage = {
  /**
   * 设置存储项
   */
  set(key: string, value: any) {
    try {
      const serializedValue = JSON.stringify(value)
      window.sessionStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('存储会话数据失败:', error)
    }
  },

  /**
   * 获取存储项
   */
  get<T = any>(key: string, defaultValue?: T): T | null {
    try {
      const item = window.sessionStorage.getItem(key)
      if (item === null) {
        return defaultValue || null
      }
      return JSON.parse(item)
    } catch (error) {
      console.error('读取会话数据失败:', error)
      return defaultValue || null
    }
  },

  /**
   * 移除存储项
   */
  remove(key: string) {
    try {
      window.sessionStorage.removeItem(key)
    } catch (error) {
      console.error('移除会话数据失败:', error)
    }
  },

  /**
   * 清空所有存储
   */
  clear() {
    try {
      window.sessionStorage.clear()
    } catch (error) {
      console.error('清空会话存储失败:', error)
    }
  },

  /**
   * 检查是否存在某个key
   */
  has(key: string): boolean {
    return window.sessionStorage.getItem(key) !== null
  }
}

/**
 * 认证相关存储常量
 */
export const AUTH_KEYS = {
  TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  TENANT_ID: 'tenant_id'
} as const
