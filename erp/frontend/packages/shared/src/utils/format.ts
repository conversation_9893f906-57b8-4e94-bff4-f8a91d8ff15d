// 格式化工具函数

/**
 * 格式化日期时间
 */
export const formatDateTime = (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '-'
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期
 */
export const formatDate = (date: string | Date) => {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间
 */
export const formatTime = (date: string | Date) => {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 格式化用户状态
 */
export const formatUserStatus = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    0: '禁用',
    2: '待激活'
  }
  return statusMap[status] || '未知'
}

/**
 * 格式化用户类型
 */
export const formatUserType = (userType: string) => {
  const typeMap: Record<string, string> = {
    'admin': '系统管理员',
    'tenant_admin': '租户管理员',
    'user': '普通用户'
  }
  return typeMap[userType] || userType
}

/**
 * 格式化租户状态
 */
export const formatTenantStatus = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    0: '禁用',
    2: '试用'
  }
  return statusMap[status] || '未知'
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字，添加千分位分隔符
 */
export const formatNumber = (num: number | string) => {
  if (num === null || num === undefined || num === '') return '-'
  return Number(num).toLocaleString()
}

/**
 * 格式化百分比
 */
export const formatPercent = (num: number, decimals = 2) => {
  if (num === null || num === undefined) return '-'
  return (num * 100).toFixed(decimals) + '%'
}

/**
 * 截断文本
 */
export const truncateText = (text: string, maxLength = 50) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
