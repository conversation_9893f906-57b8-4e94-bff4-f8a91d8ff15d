// 表单验证工具函数

/**
 * 验证邮箱格式
 */
export const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式
 */
export const validatePhone = (phone: string) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证密码强度
 */
export const validatePassword = (password: string) => {
  // 至少8位，包含大小写字母、数字
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return passwordRegex.test(password)
}

/**
 * 验证用户名格式
 */
export const validateUsername = (username: string) => {
  // 4-20位，字母、数字、下划线
  const usernameRegex = /^[a-zA-Z0-9_]{4,20}$/
  return usernameRegex.test(username)
}

/**
 * 验证租户代码格式
 */
export const validateTenantCode = (code: string) => {
  // 3-20位，字母、数字、连字符
  const codeRegex = /^[a-zA-Z0-9-]{3,20}$/
  return codeRegex.test(code)
}

/**
 * 验证IP地址格式
 */
export const validateIP = (ip: string) => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

/**
 * 验证URL格式
 */
export const validateURL = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 创建必填验证规则
 */
export const createRequiredRule = (message: string) => ({
  required: true,
  message,
  trigger: 'blur'
})

/**
 * 创建邮箱验证规则
 */
export const createEmailRule = () => ({
  validator: (_: any, value: string, callback: any) => {
    if (value && !validateEmail(value)) {
      callback(new Error('请输入正确的邮箱格式'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 创建手机号验证规则
 */
export const createPhoneRule = () => ({
  validator: (_: any, value: string, callback: any) => {
    if (value && !validatePhone(value)) {
      callback(new Error('请输入正确的手机号格式'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 创建密码验证规则
 */
export const createPasswordRule = () => ({
  validator: (_: any, value: string, callback: any) => {
    if (value && !validatePassword(value)) {
      callback(new Error('密码至少8位，包含大小写字母和数字'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 创建用户名验证规则
 */
export const createUsernameRule = () => ({
  validator: (_: any, value: string, callback: any) => {
    if (value && !validateUsername(value)) {
      callback(new Error('用户名4-20位，只能包含字母、数字、下划线'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 创建租户代码验证规则
 */
export const createTenantCodeRule = () => ({
  validator: (_: any, value: string, callback: any) => {
    if (value && !validateTenantCode(value)) {
      callback(new Error('租户代码3-20位，只能包含字母、数字、连字符'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 创建长度验证规则
 */
export const createLengthRule = (min: number, max: number, message?: string) => ({
  min,
  max,
  message: message || `长度在 ${min} 到 ${max} 个字符`,
  trigger: 'blur'
})
