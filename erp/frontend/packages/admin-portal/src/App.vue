<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@erp/shared'

const authStore = useAuthStore()

onMounted(async () => {
  // 初始化认证状态
  await authStore.initialize()
})
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  height: 100vh;
  min-width: 1200px;
}

/* Element Plus 组件样式优化 */
.el-button {
  font-weight: 500;
}

.el-button--large {
  height: 48px;
  padding: 12px 24px;
  font-size: 16px;
}

.el-input--large .el-input__inner {
  height: 48px;
  line-height: 48px;
  font-size: 16px;
}

.el-select--large .el-input__inner {
  height: 48px;
  line-height: 48px;
}

.el-form-item--large .el-form-item__label {
  height: 48px;
  line-height: 48px;
  font-size: 16px;
}

.el-table .el-table__row {
  height: 64px;
}

.el-table .el-table__header-wrapper {
  background: #fafafa;
}

.el-pagination {
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1199px) {
  #app {
    min-width: 768px;
  }
}

@media (max-width: 767px) {
  #app {
    min-width: 375px;
  }
  
  .el-button--large {
    height: 40px;
    padding: 10px 20px;
    font-size: 14px;
  }
  
  .el-input--large .el-input__inner {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
}
</style>
