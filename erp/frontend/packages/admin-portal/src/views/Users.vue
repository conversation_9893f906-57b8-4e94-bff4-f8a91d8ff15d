<template>
  <AppLayout
    title="用户管理"
    :menu-items="menuItems"
    :user-info="authStore.user"
    @logout="handleLogout"
  >
    <div class="users-container">
      <!-- 操作栏 -->
      <div class="actions-bar">
        <el-button type="primary" size="large" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button size="large" @click="loadUsers">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <!-- 数据表格 -->
      <DataTable
        :data="users"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :search-fields="['keyword', 'status', 'userType']"
        @search="handleSearch"
        @page-change="handlePageChange"
      >

      </DataTable>
      
      <!-- 用户表单对话框 -->
      <FormDialog
        v-model:visible="dialogVisible"
        :title="dialogTitle"
        :form-data="formData"
        :rules="formRules"
        :loading="submitLoading"
        @confirm="handleSubmit"
        @close="handleDialogClose"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="formData.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="formData.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="formData.userType" placeholder="请选择用户类型">
            <el-option label="系统管理员" value="admin" />
            <el-option label="租户管理员" value="tenant_admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
      </FormDialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import {
  AppLayout, DataTable, FormDialog, useAuthStore,
  userAPI, createRequiredRule, createEmailRule, createUsernameRule, createPasswordRule
} from '@erp/shared'
import type { User, TableColumn } from '@erp/shared'

const router = useRouter()
const authStore = useAuthStore()

// 数据状态
const users = ref<User[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框状态
const dialogVisible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)

// 表单数据
const formData = reactive({
  id: 0,
  username: '',
  email: '',
  realName: '',
  password: '',
  userType: 'user' as const,
  status: 1
})

// 菜单配置
const menuItems = [
  { index: '/dashboard', title: '系统概览', icon: 'House' },
  { index: '/users', title: '用户管理', icon: 'User' },
  { index: '/roles', title: '角色管理', icon: 'UserFilled' },
  { index: '/tenants', title: '租户管理', icon: 'OfficeBuilding' }
]

// 表格列配置
const tableColumns: TableColumn[] = [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'username', label: '用户名', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  { prop: 'realName', label: '真实姓名', width: 120 },
  { prop: 'userType', label: '用户类型', width: 120 },
  { prop: 'status', label: '状态', width: 100 },
  { prop: 'tenantName', label: '所属租户', width: 150 },
  { prop: 'createdAt', label: '创建时间', width: 180 }
]

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新增用户')

// 表单验证规则
const formRules = computed(() => ({
  username: [createRequiredRule('请输入用户名'), createUsernameRule()],
  email: [createRequiredRule('请输入邮箱'), createEmailRule()],
  realName: [createRequiredRule('请输入真实姓名')],
  password: isEdit.value ? [] : [createRequiredRule('请输入密码'), createPasswordRule()],
  userType: [createRequiredRule('请选择用户类型')],
  status: [createRequiredRule('请选择状态')]
}))



// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('已安全退出')
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 加载用户列表
const loadUsers = async (params = {}) => {
  try {
    loading.value = true
    const response = await userAPI.getUsers({
      page: currentPage.value,
      pageSize: pageSize.value,
      ...params
    }) as any
    users.value = response.data
    total.value = response.total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (searchParams: any) => {
  currentPage.value = 1
  loadUsers(searchParams)
}

// 处理分页
const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  loadUsers()
}

// 处理新增
const handleAdd = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: 0,
    username: '',
    email: '',
    realName: '',
    password: '',
    userType: 'user',
    status: 1
  })
  dialogVisible.value = true
}



// 处理提交
const handleSubmit = async () => {
  try {
    submitLoading.value = true
    
    if (isEdit.value) {
      await userAPI.updateUser(String(formData.id), formData)
      ElMessage.success('用户更新成功')
    } else {
      await userAPI.createUser(formData)
      ElMessage.success('用户创建成功')
    }
    
    dialogVisible.value = false
    loadUsers()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
}



onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users-container {
  padding: 24px;
}

.actions-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.actions-bar .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
