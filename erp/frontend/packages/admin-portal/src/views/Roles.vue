<template>
  <AppLayout
    title="角色管理"
    :menu-items="menuItems"
    :user-info="authStore.user"
    @logout="handleLogout"
  >
    <div class="roles-container">
      <el-card>
        <h2>角色管理</h2>
        <p>角色管理功能正在开发中...</p>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AppLayout, useAuthStore } from '@erp/shared'

const router = useRouter()
const authStore = useAuthStore()

// 菜单配置
const menuItems = [
  { index: '/dashboard', title: '系统概览', icon: 'House' },
  { index: '/users', title: '用户管理', icon: 'User' },
  { index: '/roles', title: '角色管理', icon: 'UserFilled' },
  { index: '/tenants', title: '租户管理', icon: 'OfficeBuilding' }
]

// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('已安全退出')
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}
</script>

<style scoped>
.roles-container {
  padding: 24px;
}
</style>
