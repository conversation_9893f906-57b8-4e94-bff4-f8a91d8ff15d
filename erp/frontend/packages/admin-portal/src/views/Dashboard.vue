<template>
  <AppLayout
    title="系统概览"
    :menu-items="menuItems"
    :user-info="authStore.user"
    @logout="handleLogout"
  >
    <div class="dashboard-container">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.totalUsers }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon tenant-icon">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.totalTenants }}</h3>
              <p>租户数量</p>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon role-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.totalRoles }}</h3>
              <p>角色数量</p>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.activeUsers }}</h3>
              <p>活跃用户</p>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 快速操作 -->
      <el-card class="quick-actions-card">
        <template #header>
          <h3>快速操作</h3>
        </template>
        <div class="quick-actions">
          <el-button type="primary" size="large" @click="$router.push('/users')">
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
          <el-button type="success" size="large" @click="$router.push('/tenants')">
            <el-icon><OfficeBuilding /></el-icon>
            租户管理
          </el-button>
          <el-button type="warning" size="large" @click="$router.push('/roles')">
            <el-icon><UserFilled /></el-icon>
            角色管理
          </el-button>
        </div>
      </el-card>
      
      <!-- 系统状态 -->
      <el-card class="system-status-card">
        <template #header>
          <h3>系统状态</h3>
        </template>
        <div class="system-status">
          <div class="status-item">
            <span class="status-label">系统状态:</span>
            <el-tag type="success">正常运行</el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">数据库:</span>
            <el-tag type="success">连接正常</el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">最后更新:</span>
            <span>{{ new Date().toLocaleString() }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, OfficeBuilding, UserFilled, CircleCheck } from '@element-plus/icons-vue'
import { AppLayout, useAuthStore } from '@erp/shared'

const router = useRouter()
const authStore = useAuthStore()

// 统计数据
const stats = ref({
  totalUsers: 0,
  totalTenants: 0,
  totalRoles: 0,
  activeUsers: 0
})

// 菜单配置
const menuItems = [
  { index: '/dashboard', title: '系统概览', icon: 'House' },
  { index: '/users', title: '用户管理', icon: 'User' },
  { index: '/roles', title: '角色管理', icon: 'UserFilled' },
  { index: '/tenants', title: '租户管理', icon: 'OfficeBuilding' }
]

// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('已安全退出')
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用实际的API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      totalUsers: 156,
      totalTenants: 23,
      totalRoles: 8,
      activeUsers: 89
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tenant-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.role-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.active-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.stat-info p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.quick-actions-card,
.system-status-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.quick-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 140px;
  height: 56px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-actions .el-button {
    width: 100%;
  }
}
</style>
