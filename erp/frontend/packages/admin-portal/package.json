{"name": "@erp/admin-portal", "version": "1.0.0", "description": "ERP系统管理员门户", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "vue-tsc -b && vite build", "preview": "vite preview --port 3001", "type-check": "vue-tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@erp/shared": "workspace:*", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}