{"name": "@erp/user-portal", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3002", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@erp/shared": "workspace:*", "vue": "^3.5.17", "vue-router": "^4.5.0", "pinia": "^3.0.3", "element-plus": "^2.10.2", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "typescript": "^5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.0"}}