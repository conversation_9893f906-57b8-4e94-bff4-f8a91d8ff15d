<template>
  <AppLayout
    title="订单管理"
    :menu-items="menuItems"
    :user-info="authStore.user"
    @logout="handleLogout"
  >
    <div class="orders-container">
      <el-card>
        <h2>订单管理</h2>
        <p>订单管理功能正在开发中...</p>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AppLayout, useAuthStore } from '@erp/shared'

const router = useRouter()
const authStore = useAuthStore()

// 菜单配置
const menuItems = [
  { index: '/dashboard', title: '工作台', icon: 'House' },
  { index: '/orders', title: '订单管理', icon: 'Document' },
  { index: '/inventory', title: '库存管理', icon: 'Box' },
  { index: '/reports', title: '报表中心', icon: 'DataAnalysis' }
]

// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('已安全退出')
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}
</script>

<style scoped>
.orders-container {
  padding: 24px;
}
</style>
