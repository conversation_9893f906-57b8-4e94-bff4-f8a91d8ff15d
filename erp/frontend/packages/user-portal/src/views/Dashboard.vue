<template>
  <AppLayout
    title="工作台"
    :menu-items="menuItems"
    :user-info="authStore.user"
    @logout="handleLogout"
  >
    <div class="dashboard-container">
      <!-- 欢迎信息 -->
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ authStore.user?.realName || authStore.user?.username }}！</h2>
            <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          </div>
          <div class="welcome-icon">
            <el-icon size="64" color="#409eff">
              <User />
            </el-icon>
          </div>
        </div>
      </el-card>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon size="32">
                <Document />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.pendingOrders }}</div>
              <div class="stat-label">待处理订单</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon size="32">
                <CircleCheck />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.completedOrders }}</div>
              <div class="stat-label">已完成订单</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon inventory">
              <el-icon size="32">
                <Box />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.lowStockItems }}</div>
              <div class="stat-label">库存预警</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <el-icon size="32">
                <Money />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">¥{{ stats.monthlyRevenue.toLocaleString() }}</div>
              <div class="stat-label">本月营收</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 快捷操作 -->
      <el-card class="quick-actions-card">
        <template #header>
          <h3>快捷操作</h3>
        </template>
        <div class="quick-actions">
          <el-button type="primary" size="large" @click="$router.push('/orders')">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
          <el-button type="success" size="large" @click="$router.push('/inventory')">
            <el-icon><Search /></el-icon>
            查看库存
          </el-button>
          <el-button type="info" size="large" @click="$router.push('/reports')">
            <el-icon><DataAnalysis /></el-icon>
            查看报表
          </el-button>
          <el-button type="warning" size="large" @click="$router.push('/profile')">
            <el-icon><Setting /></el-icon>
            个人设置
          </el-button>
        </div>
      </el-card>

      <!-- 最近活动 -->
      <el-card class="recent-activities-card">
        <template #header>
          <h3>最近活动</h3>
        </template>
        <div class="activities-list">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon">
              <el-icon :color="activity.color">
                <component :is="activity.icon" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  User, Document, CircleCheck, Box, Money, Plus, Search, 
  DataAnalysis, Setting, ShoppingCart, Goods, Warning
} from '@element-plus/icons-vue'
import { AppLayout, useAuthStore } from '@erp/shared'

const router = useRouter()
const authStore = useAuthStore()

// 菜单配置
const menuItems = [
  { index: '/dashboard', title: '工作台', icon: 'House' },
  { index: '/orders', title: '订单管理', icon: 'Document' },
  { index: '/inventory', title: '库存管理', icon: 'Box' },
  { index: '/reports', title: '报表中心', icon: 'DataAnalysis' }
]

// 统计数据
const stats = ref({
  pendingOrders: 12,
  completedOrders: 156,
  lowStockItems: 3,
  monthlyRevenue: 285600
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '创建了新订单 #ORD-2024-001',
    time: '2小时前',
    icon: 'ShoppingCart',
    color: '#409eff'
  },
  {
    id: 2,
    title: '更新了商品库存',
    time: '4小时前',
    icon: 'Goods',
    color: '#67c23a'
  },
  {
    id: 3,
    title: '库存预警：商品A库存不足',
    time: '6小时前',
    icon: 'Warning',
    color: '#e6a23c'
  }
])

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('已安全退出')
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 加载数据
const loadDashboardData = async () => {
  try {
    // 这里可以调用API获取实际数据
    console.log('加载仪表板数据')
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 24px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  color: #7f8c8d;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #409eff, #66b3ff);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.inventory {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #f56c6c, #f89898);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.quick-actions-card {
  margin-bottom: 24px;
}

.quick-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  height: auto;
  font-size: 16px;
}

.recent-activities-card {
  margin-bottom: 24px;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background: #e9ecef;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .quick-actions .el-button {
    flex: 1;
    min-width: 140px;
  }
}
</style>
