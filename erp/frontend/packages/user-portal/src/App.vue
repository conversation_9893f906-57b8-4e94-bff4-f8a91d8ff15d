<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@erp/shared'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
  min-width: 1200px; /* 桌面端最小宽度 */
}

/* 桌面端优化样式 */
.el-button {
  min-height: 40px;
  padding: 12px 20px;
  font-size: 14px;
}

.el-input .el-input__inner {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.el-select .el-input .el-input__inner {
  height: 40px;
  line-height: 40px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式断点 */
@media (max-width: 1200px) {
  #app {
    min-width: 100%;
  }
}
</style>
