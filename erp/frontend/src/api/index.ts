import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 添加租户ID
    if (authStore.tenantId) {
      config.headers = config.headers || {}
      config.headers['X-Tenant-ID'] = authStore.tenantId
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应数据
    const { data } = response
    
    // 如果是健康检查等特殊接口，直接返回
    if (response.config.url?.includes('/health') || 
        response.config.url?.includes('/ready') || 
        response.config.url?.includes('/version')) {
      return response
    }
    
    // 处理业务响应
    if (data.code === 200) {
      return data.data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const healthAPI = {
  // 健康检查
  health: () => api.get('/health'),
  ready: () => api.get('/ready'),
  version: () => api.get('/version')
}

export const authAPI = {
  // 用户登录
  login: (data: { username: string; password: string }) => 
    api.post('/api/auth/login', data),
  
  // 租户登录
  tenantLogin: (data: { username: string; password: string; tenantCode: string }) => 
    api.post('/api/auth/tenant/login', data),
  
  // 系统管理员登录
  adminLogin: (data: { username: string; password: string }) => 
    api.post('/api/auth/admin/login', data),
  
  // 刷新token
  refreshToken: (refreshToken: string) => 
    api.post('/api/auth/refresh', { refresh_token: refreshToken }),
  
  // 验证token
  validateToken: () => api.post('/api/auth/validate'),
  
  // 登出
  logout: () => api.post('/api/auth/logout'),
  
  // 获取用户信息
  getProfile: () => api.get('/api/auth/profile'),
  
  // 修改密码
  changePassword: (data: { old_password: string; new_password: string }) => 
    api.post('/api/auth/change-password', data)
}

export const userAPI = {
  // 获取用户列表
  getUsers: (params?: any) => api.get('/api/users', { params }),
  
  // 获取用户详情
  getUser: (id: string) => api.get(`/api/users/${id}`),
  
  // 创建用户
  createUser: (data: any) => api.post('/api/users', data),
  
  // 更新用户
  updateUser: (id: string, data: any) => api.put(`/api/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: string) => api.delete(`/api/users/${id}`),
  
  // 更新用户状态
  updateUserStatus: (id: string, status: number) => 
    api.put(`/api/users/${id}/status`, { status }),
  
  // 分配角色
  assignRoles: (id: string, roleIds: string[]) => 
    api.put(`/api/users/${id}/roles`, { role_ids: roleIds }),
  
  // 重置密码
  resetPassword: (id: string) => api.post(`/api/users/${id}/reset-password`)
}

export const roleAPI = {
  // 获取角色列表
  getRoles: (params?: any) => api.get('/api/roles', { params }),
  
  // 获取角色详情
  getRole: (id: string) => api.get(`/api/roles/${id}`),
  
  // 创建角色
  createRole: (data: any) => api.post('/api/roles', data),
  
  // 更新角色
  updateRole: (id: string, data: any) => api.put(`/api/roles/${id}`, data),
  
  // 删除角色
  deleteRole: (id: string) => api.delete(`/api/roles/${id}`),
  
  // 分配权限
  assignPermissions: (id: string, permissionIds: string[]) => 
    api.put(`/api/roles/${id}/permissions`, { permission_ids: permissionIds })
}

export const tenantAPI = {
  // 获取租户列表
  getTenants: (params?: any) => api.get('/api/tenants', { params }),
  
  // 获取租户详情
  getTenant: (id: string) => api.get(`/api/tenants/${id}`),
  
  // 创建租户
  createTenant: (data: any) => api.post('/api/tenants', data),
  
  // 更新租户
  updateTenant: (id: string, data: any) => api.put(`/api/tenants/${id}`, data),
  
  // 删除租户
  deleteTenant: (id: string) => api.delete(`/api/tenants/${id}`),
  
  // 更新租户状态
  updateTenantStatus: (id: string, status: number) => 
    api.put(`/api/tenants/${id}/status`, { status })
}

export default api
