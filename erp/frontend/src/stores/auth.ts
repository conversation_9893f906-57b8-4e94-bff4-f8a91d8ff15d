import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '../api'

export interface User {
  id: string
  username: string
  email: string
  realName: string
  phone?: string
  avatar?: string
  userType: string
  status: number
  tenantId?: string
  roles?: string[]
  permissions?: string[]
}

export interface LoginForm {
  username: string
  password: string
  tenantCode?: string
  loginType: 'user' | 'tenant' | 'admin'
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const refreshToken = ref<string>(localStorage.getItem('refreshToken') || '')
  const user = ref<User | null>(null)
  const tenantId = ref<string>(localStorage.getItem('tenantId') || '')
  const tenantCode = ref<string>(localStorage.getItem('tenantCode') || '')
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userRoles = computed(() => user.value?.roles || [])
  const userPermissions = computed(() => user.value?.permissions || [])
  
  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      let response
      
      switch (loginForm.loginType) {
        case 'tenant':
          response = await authAPI.tenantLogin({
            username: loginForm.username,
            password: loginForm.password,
            tenantCode: loginForm.tenantCode!
          })
          break
        case 'admin':
          response = await authAPI.adminLogin({
            username: loginForm.username,
            password: loginForm.password
          })
          break
        default:
          response = await authAPI.login({
            username: loginForm.username,
            password: loginForm.password
          })
      }
      
      // 保存token和用户信息
      if (response.token) {
        token.value = response.token
        refreshToken.value = response.refresh_token || ''
        user.value = response.user
        
        // 保存到localStorage
        localStorage.setItem('token', token.value)
        if (refreshToken.value) {
          localStorage.setItem('refreshToken', refreshToken.value)
        }
        
        // 保存租户信息
        if (response.user.tenantId) {
          tenantId.value = response.user.tenantId
          localStorage.setItem('tenantId', tenantId.value)
        }
        
        if (loginForm.tenantCode) {
          tenantCode.value = loginForm.tenantCode
          localStorage.setItem('tenantCode', tenantCode.value)
        }
        
        return response
      }
      
      throw new Error('登录响应格式错误')
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      refreshToken.value = ''
      user.value = null
      tenantId.value = ''
      tenantCode.value = ''
      
      // 清除localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('tenantId')
      localStorage.removeItem('tenantCode')
    }
  }
  
  // 刷新token
  const refresh = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有refresh token')
      }
      
      const response = await authAPI.refreshToken(refreshToken.value)
      
      if (response.token) {
        token.value = response.token
        localStorage.setItem('token', token.value)
        
        if (response.refresh_token) {
          refreshToken.value = response.refresh_token
          localStorage.setItem('refreshToken', refreshToken.value)
        }
        
        return response
      }
      
      throw new Error('刷新token失败')
    } catch (error) {
      console.error('刷新token失败:', error)
      await logout()
      throw error
    }
  }
  
  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const response = await authAPI.getProfile()
      user.value = response
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }
  
  // 验证token
  const validateToken = async () => {
    try {
      await authAPI.validateToken()
      return true
    } catch (error) {
      console.error('token验证失败:', error)
      await logout()
      return false
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await authAPI.changePassword({
        old_password: oldPassword,
        new_password: newPassword
      })
      return true
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string) => {
    return userPermissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string) => {
    return userRoles.value.includes(role)
  }
  
  // 初始化（从localStorage恢复状态）
  const initialize = async () => {
    if (token.value) {
      try {
        await fetchUserProfile()
      } catch (error) {
        console.error('初始化用户信息失败:', error)
        await logout()
      }
    }
  }
  
  return {
    // 状态
    token,
    refreshToken,
    user,
    tenantId,
    tenantCode,
    
    // 计算属性
    isAuthenticated,
    userRoles,
    userPermissions,
    
    // 方法
    login,
    logout,
    refresh,
    fetchUserProfile,
    validateToken,
    changePassword,
    hasPermission,
    hasRole,
    initialize
  }
})
