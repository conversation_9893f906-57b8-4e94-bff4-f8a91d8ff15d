import type { RouteRecordRaw } from 'vue-router'
import Index from '../views/Index.vue'
import AdminLogin from '../views/AdminLogin.vue'
import UserLogin from '../views/UserLogin.vue'
import Layout from '../components/Layout.vue'
import Dashboard from '../views/Dashboard.vue'
import UserManagement from '../views/UserManagement.vue'
import RoleManagement from '../views/RoleManagement.vue'
import TenantManagement from '../views/TenantManagement.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Index',
    component: Index,
    meta: { requiresAuth: false }
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: { requiresAuth: false }
  },
  {
    path: '/user/login',
    name: 'UserLogin',
    component: UserLogin,
    meta: { requiresAuth: false }
  },
  // 兼容旧的登录路由
  {
    path: '/login',
    redirect: '/user/login'
  },
  {
    path: '/dashboard',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '仪表板' }
      }
    ]
  },
  {
    path: '/admin/dashboard',
    component: Layout,
    meta: { requiresAuth: true, adminOnly: true },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: Dashboard,
        meta: { title: '管理员仪表板' }
      }
    ]
  },
  {
    path: '/tenant/dashboard',
    component: Layout,
    meta: { requiresAuth: true, tenantAdminOnly: true },
    children: [
      {
        path: '',
        name: 'TenantDashboard',
        component: Dashboard,
        meta: { title: '租户管理仪表板' }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'UserManagement',
        component: UserManagement,
        meta: { title: '用户管理' }
      }
    ]
  },
  {
    path: '/roles',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'RoleManagement',
        component: RoleManagement,
        meta: { title: '角色管理' }
      }
    ]
  },
  {
    path: '/tenants',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'TenantManagement',
        component: TenantManagement,
        meta: { title: '租户管理' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

export default routes
