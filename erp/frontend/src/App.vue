<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const router = useRouter()
const authStore = useAuthStore()

onMounted(async () => {
  // 初始化认证状态
  await authStore.initialize()

  // 如果未登录且不在登录相关页面，跳转到首页
  const currentPath = router.currentRoute.value.path
  const publicPaths = ['/', '/admin/login', '/user/login', '/login']
  if (!authStore.isAuthenticated && !publicPaths.includes(currentPath)) {
    router.push('/')
  }
})
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-width: 1200px; /* 桌面端最小宽度 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  background-color: #f0f2f5;
  overflow-x: auto; /* 支持横向滚动 */
}

/* 全局样式 */
.page-container {
  padding: 32px; /* 增加内边距 */
  max-width: 1600px; /* 最大宽度限制 */
  margin: 0 auto; /* 居中显示 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px; /* 增加卡片头部内边距 */
  font-size: 16px;
  font-weight: 600;
}

/* Element Plus 样式覆盖 - 桌面端优化 */
.el-button {
  font-size: 14px;
  padding: 10px 20px; /* 增加按钮内边距 */
}

.el-button--large {
  font-size: 16px;
  padding: 12px 24px;
}

.el-input__inner {
  font-size: 14px;
  height: 40px; /* 增加输入框高度 */
}

.el-select {
  font-size: 14px;
}

.el-table {
  font-size: 14px;
}

.el-table th {
  font-size: 14px;
  font-weight: 600;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.el-button--text {
  padding: 0;
}

.el-button--text.danger {
  color: #f56c6c;
}

.el-button--text.danger:hover {
  color: #f78989;
}

.el-button--text.success {
  color: #67c23a;
}

.el-button--text.success:hover {
  color: #85ce61;
}

/* 响应式工具类 */
@media (max-width: 1200px) {
  #app {
    min-width: 1000px;
  }

  .page-container {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  #app {
    min-width: 768px;
  }

  .page-container {
    padding: 16px;
  }

  .card-header {
    padding: 16px 20px;
    font-size: 14px;
  }
}
</style>
