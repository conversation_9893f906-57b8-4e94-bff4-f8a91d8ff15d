<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const router = useRouter()
const authStore = useAuthStore()

onMounted(async () => {
  // 初始化认证状态
  await authStore.initialize()

  // 如果未登录且不在登录页面，跳转到登录页
  if (!authStore.isAuthenticated && router.currentRoute.value.path !== '/login') {
    router.push('/login')
  }
})
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  background-color: #f0f2f5;
}

/* 全局样式 */
.page-container {
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Element Plus 样式覆盖 */
.el-button--text {
  padding: 0;
}

.el-button--text.danger {
  color: #f56c6c;
}

.el-button--text.danger:hover {
  color: #f78989;
}

.el-button--text.success {
  color: #67c23a;
}

.el-button--text.success:hover {
  color: #85ce61;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
}
</style>
