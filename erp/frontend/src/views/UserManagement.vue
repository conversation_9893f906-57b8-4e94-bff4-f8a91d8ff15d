<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统用户信息和权限</p>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="24" class="search-row">
        <el-col :xs="24" :sm="12" :md="6" :lg="5">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱或真实姓名"
            prefix-icon="Search"
            clearable
            size="large"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="4" :lg="3">
          <el-select
            v-model="searchForm.status"
            placeholder="用户状态"
            clearable
            size="large"
            style="width: 100%"
          >
            <el-option label="全部" value="" />
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="4" :lg="3">
          <el-select
            v-model="searchForm.userType"
            placeholder="用户类型"
            clearable
            size="large"
            style="width: 100%"
          >
            <el-option label="全部" value="" />
            <el-option label="普通用户" value="user" />
            <el-option label="租户管理员" value="tenant_admin" />
            <el-option label="系统管理员" value="admin" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="3" :lg="2">
          <el-button type="primary" icon="Search" size="large" @click="handleSearch">
            搜索
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="5" :lg="3">
          <el-button icon="Refresh" size="large" @click="handleReset">
            重置
          </el-button>
        </el-col>
      </el-row>
      
      <el-row class="action-row">
        <el-col :span="24">
          <el-button type="primary" icon="Plus" @click="handleAdd">
            添加用户
          </el-button>
          <el-button
            type="danger"
            icon="Delete"
            :disabled="selectedUsers.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
          <el-button icon="Refresh" @click="refreshData">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="username" label="用户名" min-width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.avatar">
                {{ row.realName?.charAt(0) || row.username.charAt(0) }}
              </el-avatar>
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="realName" label="真实姓名" min-width="100" />
        
        <el-table-column prop="email" label="邮箱" min-width="180" />
        
        <el-table-column prop="phone" label="手机号" min-width="120" />
        
        <el-table-column prop="userType" label="用户类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getUserTypeTagType(row.userType)">
              {{ getUserTypeText(row.userType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleAssignRoles(row)">
              分配角色
            </el-button>
            <el-button
              type="text"
              size="small"
              :class="row.status === 1 ? 'danger' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="text"
              size="small"
              class="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userAPI } from '../api'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  userType: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取用户类型标签类型
const getUserTypeTagType = (userType: string) => {
  const typeMap: Record<string, string> = {
    'admin': 'danger',
    'tenant_admin': 'warning',
    'user': 'info'
  }
  return typeMap[userType] || 'info'
}

// 获取用户类型文本
const getUserTypeText = (userType: string) => {
  const typeMap: Record<string, string> = {
    'admin': '系统管理员',
    'tenant_admin': '租户管理员',
    'user': '普通用户'
  }
  return typeMap[userType] || '未知'
}

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }
    
    const response = await userAPI.getUsers(params)
    userList.value = response.data || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 刷新数据
const refreshData = () => {
  getUserList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  getUserList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getUserList()
}

// 添加用户
const handleAdd = () => {
  ElMessage.info('添加用户功能开发中')
}

// 编辑用户
const handleEdit = (row: any) => {
  ElMessage.info('编辑用户功能开发中')
}

// 分配角色
const handleAssignRoles = (row: any) => {
  ElMessage.info('分配角色功能开发中')
}

// 切换用户状态
const handleToggleStatus = async (row: any) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}用户 "${row.username}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const newStatus = row.status === 1 ? 0 : 1
    await userAPI.updateUserStatus(row.id, newStatus)
    
    row.status = newStatus
    ElMessage.success(`用户${action}成功`)
  } catch (error) {
    console.error('更新用户状态失败:', error)
  }
}

// 删除用户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${row.username}" 吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userAPI.deleteUser(row.id)
    ElMessage.success('用户删除成功')
    getUserList()
  } catch (error) {
    console.error('删除用户失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const promises = selectedUsers.value.map((user: any) => userAPI.deleteUser(user.id))
    await Promise.all(promises)
    
    ElMessage.success('批量删除成功')
    getUserList()
  } catch (error) {
    console.error('批量删除失败:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.user-management {
  padding: 0;
  min-width: 1000px; /* 确保桌面端最小宽度 */
}

.page-header {
  margin-bottom: 32px;
  padding: 24px 0;
  border-bottom: 2px solid #f0f2f5;
}

.page-header h1 {
  margin: 0 0 12px 0;
  font-size: 32px; /* 增大标题字体 */
  color: #2c3e50;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px; /* 增大描述字体 */
}

.search-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-row {
  align-items: center;
}

.search-card :deep(.el-card__body) {
  padding: 24px; /* 增加卡片内边距 */
}

.action-row {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.table-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.el-card__body) {
  padding: 0; /* 表格卡片无内边距 */
}

.table-card :deep(.el-table) {
  font-size: 14px; /* 表格字体大小 */
}

.table-card :deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
  font-size: 14px;
  height: 56px; /* 增加表头高度 */
}

.table-card :deep(.el-table td) {
  height: 64px; /* 增加表格行高 */
  padding: 16px 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加间距 */
}

.username {
  font-weight: 600;
  font-size: 15px; /* 增大用户名字体 */
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 24px 0;
}

.pagination-container :deep(.el-pagination) {
  font-size: 14px;
}

.danger {
  color: #f56c6c;
}

.success {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-row .el-button {
    margin-bottom: 8px;
  }
}
</style>
