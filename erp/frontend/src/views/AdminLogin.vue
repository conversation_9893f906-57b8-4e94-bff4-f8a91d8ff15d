<template>
  <div class="admin-login-container">
    <div class="login-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" text>
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-wrapper">
        <div class="login-header">
          <img src="/logo.svg" alt="ERP Logo" class="login-logo" />
          <h1 class="login-title">管理员登录</h1>
          <p class="login-subtitle">系统管理员和租户管理员入口</p>
        </div>

        <!-- 管理员类型选择 -->
        <div class="admin-type-selector">
          <el-radio-group v-model="loginForm.userType" size="large">
            <el-radio-button value="admin">系统管理员</el-radio-button>
            <el-radio-button value="tenant_admin">租户管理员</el-radio-button>
          </el-radio-group>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item v-if="loginForm.userType === 'tenant_admin'" prop="tenantCode">
            <el-input
              v-model="loginForm.tenantCode"
              placeholder="请输入租户代码"
              prefix-icon="OfficeBuilding"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 系统状态 -->
        <div class="system-status">
          <div class="status-item">
            <el-icon :class="systemStatus.api ? 'status-success' : 'status-error'">
              <CircleCheck v-if="systemStatus.api" />
              <CircleClose v-else />
            </el-icon>
            <span>API服务</span>
          </div>
          <div class="status-item">
            <el-icon :class="systemStatus.database ? 'status-success' : 'status-error'">
              <CircleCheck v-if="systemStatus.database" />
              <CircleClose v-else />
            </el-icon>
            <span>数据库</span>
          </div>
        </div>

        <!-- 默认账号提示 -->
        <div class="default-accounts">
          <el-collapse>
            <el-collapse-item title="默认管理员账号" name="accounts">
              <div class="account-info">
                <div class="account-item">
                  <strong>系统管理员：</strong>
                  <span>admin / admin123</span>
                </div>
                <div class="account-item">
                  <strong>租户管理员：</strong>
                  <span>tenant_admin / tenant123</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { healthAPI } from '../api'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  tenantCode: '',
  userType: 'admin' as 'admin' | 'tenant_admin',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  tenantCode: [
    { 
      required: true, 
      message: '请输入租户代码', 
      trigger: 'blur',
      validator: (rule: any, value: string, callback: Function) => {
        if (loginForm.userType === 'tenant_admin' && !value) {
          callback(new Error('租户管理员必须输入租户代码'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 系统状态
const systemStatus = reactive({
  api: false,
  database: false
})

// 返回首页
const goBack = () => {
  router.push('/')
}

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    const response = await healthAPI.getHealth()
    systemStatus.api = response.status === 'healthy'
    systemStatus.database = response.database?.connected || false
  } catch (error) {
    systemStatus.api = false
    systemStatus.database = false
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const loginData = {
      username: loginForm.username,
      password: loginForm.password,
      userType: loginForm.userType,
      ...(loginForm.userType === 'tenant_admin' && { tenantCode: loginForm.tenantCode })
    }

    await authStore.login(loginData)
    
    ElMessage.success('登录成功')
    
    // 根据用户类型跳转到相应页面
    if (loginForm.userType === 'admin') {
      router.push('/admin/dashboard')
    } else {
      router.push('/tenant/dashboard')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时检查系统状态
onMounted(() => {
  checkSystemStatus()
})
</script>

<style scoped>
.admin-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-content {
  width: 100%;
  max-width: 480px;
  position: relative;
}

.back-button {
  position: absolute;
  top: -60px;
  left: 0;
  color: white;
}

.back-button .el-button {
  color: white;
  font-size: 14px;
}

.back-button .el-button:hover {
  color: #409eff;
}

.login-form-wrapper {
  background: white;
  border-radius: 16px;
  padding: 48px 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 32px 0;
}

.admin-type-selector {
  margin-bottom: 32px;
  text-align: center;
}

.admin-type-selector .el-radio-group {
  width: 100%;
}

.admin-type-selector .el-radio-button {
  flex: 1;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.system-status {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin: 32px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.default-accounts {
  margin-top: 24px;
}

.account-info {
  padding: 8px 0;
}

.account-item {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.account-item strong {
  color: #2c3e50;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form-wrapper {
    padding: 32px 24px;
  }
  
  .login-title {
    font-size: 28px;
  }
  
  .system-status {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
