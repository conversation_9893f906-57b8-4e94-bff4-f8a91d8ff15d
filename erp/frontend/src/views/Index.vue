<template>
  <div class="index-container">
    <div class="index-content">
      <!-- 系统标题 -->
      <div class="system-header">
        <div class="logo-section">
          <img src="/logo.svg" alt="ERP Logo" class="system-logo" />
          <h1 class="system-title">ERP管理系统</h1>
        </div>
        <p class="system-subtitle">企业资源规划管理平台</p>
      </div>

      <!-- 登录入口选择 -->
      <div class="login-portals">
        <div class="portal-card admin-portal" @click="goToAdminLogin">
          <div class="portal-icon">
            <el-icon size="48"><Setting /></el-icon>
          </div>
          <h3 class="portal-title">管理员入口</h3>
          <p class="portal-description">
            系统管理员和租户管理员登录
          </p>
          <div class="portal-features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>系统配置管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>用户权限管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>租户数据管理</span>
            </div>
          </div>
          <el-button type="primary" size="large" class="portal-button">
            进入管理后台
          </el-button>
        </div>

        <div class="portal-card user-portal" @click="goToUserLogin">
          <div class="portal-icon">
            <el-icon size="48"><User /></el-icon>
          </div>
          <h3 class="portal-title">用户入口</h3>
          <p class="portal-description">
            企业员工和普通用户登录
          </p>
          <div class="portal-features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>业务数据查看</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>个人信息管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>工作流程处理</span>
            </div>
          </div>
          <el-button type="success" size="large" class="portal-button">
            进入用户系统
          </el-button>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="system-info">
        <div class="info-item">
          <el-icon><Monitor /></el-icon>
          <span>支持多租户架构</span>
        </div>
        <div class="info-item">
          <el-icon><Lock /></el-icon>
          <span>企业级安全保障</span>
        </div>
        <div class="info-item">
          <el-icon><CloudServer /></el-icon>
          <span>云端数据存储</span>
        </div>
        <div class="info-item">
          <el-icon><Cpu /></el-icon>
          <span>高性能处理引擎</span>
        </div>
      </div>
    </div>

    <!-- 页脚信息 -->
    <div class="system-footer">
      <p>&copy; 2025 ERP管理系统. 版本 v1.0.0</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToAdminLogin = () => {
  router.push('/admin/login')
}

const goToUserLogin = () => {
  router.push('/user/login')
}
</script>

<style scoped>
.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.index-content {
  max-width: 1200px;
  width: 100%;
}

/* 系统标题 */
.system-header {
  text-align: center;
  margin-bottom: 60px;
  color: white;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.system-logo {
  width: 64px;
  height: 64px;
}

.system-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 20px;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

/* 登录入口 */
.login-portals {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 60px;
}

.portal-card {
  background: white;
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.portal-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.admin-portal:hover {
  border-color: #409eff;
}

.user-portal:hover {
  border-color: #67c23a;
}

.portal-icon {
  margin-bottom: 24px;
  color: #666;
}

.admin-portal .portal-icon {
  color: #409eff;
}

.user-portal .portal-icon {
  color: #67c23a;
}

.portal-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.portal-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.portal-features {
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
  font-size: 16px;
}

.portal-button {
  width: 200px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

/* 系统信息 */
.system-info {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 40px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  opacity: 0.9;
}

.info-item .el-icon {
  font-size: 18px;
}

/* 页脚 */
.system-footer {
  text-align: center;
  color: white;
  opacity: 0.7;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-title {
    font-size: 36px;
  }
  
  .system-subtitle {
    font-size: 16px;
  }
  
  .login-portals {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .portal-card {
    padding: 32px 24px;
  }
  
  .portal-title {
    font-size: 24px;
  }
  
  .system-info {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .logo-section {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .system-info {
    grid-template-columns: 1fr;
  }
  
  .portal-button {
    width: 100%;
  }
}
</style>
