<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/logo.svg" alt="ERP" class="logo" />
        <h1 class="title">ERP管理系统</h1>
        <p class="subtitle">多租户企业资源规划系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <!-- 登录类型选择 -->
        <el-form-item>
          <el-radio-group v-model="loginForm.loginType" class="login-type-group">
            <el-radio-button label="user">普通用户</el-radio-button>
            <el-radio-button label="tenant">租户登录</el-radio-button>
            <el-radio-button label="admin">系统管理员</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <!-- 租户代码（仅租户登录时显示） -->
        <el-form-item v-if="loginForm.loginType === 'tenant'" prop="tenantCode">
          <el-input
            v-model="loginForm.tenantCode"
            placeholder="请输入租户代码"
            prefix-icon="OfficeBuilding"
            size="large"
          />
        </el-form-item>

        <!-- 用户名 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            size="large"
          />
        </el-form-item>

        <!-- 密码 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            size="large"
            show-password
          />
        </el-form-item>

        <!-- 记住我 -->
        <el-form-item>
          <el-checkbox v-model="rememberMe">记住我</el-checkbox>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-btn"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 系统状态 -->
      <div class="system-status">
        <el-tag :type="systemStatus.type" size="small">
          {{ systemStatus.text }}
        </el-tag>
        <span class="status-time">{{ systemStatus.time }}</span>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { healthAPI } from '../api'
import type { LoginForm } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const rememberMe = ref(false)
const systemHealth = ref<any>(null)

// 登录表单
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  tenantCode: '',
  loginType: 'user'
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  tenantCode: [
    { required: true, message: '请输入租户代码', trigger: 'blur' },
    { min: 2, max: 20, message: '租户代码长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 系统状态
const systemStatus = computed(() => {
  if (!systemHealth.value) {
    return {
      type: 'info',
      text: '检查中...',
      time: ''
    }
  }

  const isHealthy = systemHealth.value.status === 'healthy'
  return {
    type: isHealthy ? 'success' : 'danger',
    text: isHealthy ? '系统正常' : '系统异常',
    time: new Date(systemHealth.value.timestamp).toLocaleString()
  }
})

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()
    
    loading.value = true

    // 调用登录API
    await authStore.login(loginForm)

    ElMessage.success('登录成功')
    
    // 跳转到首页
    router.push('/')
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 检查系统健康状态
const checkSystemHealth = async () => {
  try {
    const response = await healthAPI.health()
    systemHealth.value = response.data
  } catch (error) {
    console.error('获取系统状态失败:', error)
    systemHealth.value = {
      status: 'unhealthy',
      timestamp: new Date().toISOString()
    }
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  // 如果已经登录，直接跳转到首页
  if (authStore.isAuthenticated) {
    router.push('/')
    return
  }

  // 检查系统健康状态
  await checkSystemHealth()

  // 从localStorage恢复记住的登录信息
  const savedUsername = localStorage.getItem('rememberedUsername')
  const savedLoginType = localStorage.getItem('rememberedLoginType')
  const savedTenantCode = localStorage.getItem('rememberedTenantCode')

  if (savedUsername) {
    loginForm.username = savedUsername
    rememberMe.value = true
  }

  if (savedLoginType) {
    loginForm.loginType = savedLoginType as any
  }

  if (savedTenantCode) {
    loginForm.tenantCode = savedTenantCode
  }
})

// 监听记住我选项
const handleRememberMe = () => {
  if (rememberMe.value) {
    localStorage.setItem('rememberedUsername', loginForm.username)
    localStorage.setItem('rememberedLoginType', loginForm.loginType)
    if (loginForm.tenantCode) {
      localStorage.setItem('rememberedTenantCode', loginForm.tenantCode)
    }
  } else {
    localStorage.removeItem('rememberedUsername')
    localStorage.removeItem('rememberedLoginType')
    localStorage.removeItem('rememberedTenantCode')
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-box {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.login-form {
  margin-top: 32px;
}

.login-type-group {
  width: 100%;
  display: flex;
  justify-content: center;
}

.login-type-group :deep(.el-radio-button__inner) {
  padding: 8px 16px;
  font-size: 12px;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}

.system-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.status-time {
  font-size: 12px;
  color: #999;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    width: 90%;
    padding: 24px;
    margin: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .login-type-group :deep(.el-radio-button__inner) {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
