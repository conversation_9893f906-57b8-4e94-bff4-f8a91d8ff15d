<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎使用ERP管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.userCount }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon role-icon">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.roleCount }}</div>
              <div class="stat-label">角色数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon tenant-icon">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.tenantCount }}</div>
              <div class="stat-label">租户数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.onlineCount }}</div>
              <div class="stat-label">在线用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="16" class="content-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button type="text" @click="refreshSystemStatus">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div v-if="systemStatus" class="system-status">
            <div class="status-item">
              <span class="status-label">系统状态:</span>
              <el-tag :type="systemStatus.status === 'healthy' ? 'success' : 'danger'">
                {{ systemStatus.status === 'healthy' ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">数据库:</span>
              <el-tag :type="systemStatus.database?.connected ? 'success' : 'danger'">
                {{ systemStatus.database?.connected ? '已连接' : '未连接' }}
              </el-tag>
              <span class="status-detail">
                ({{ systemStatus.database?.type }})
              </span>
            </div>
            
            <div class="status-item">
              <span class="status-label">Supabase:</span>
              <el-tag :type="systemStatus.supabase?.status === 'connected' ? 'success' : 'danger'">
                {{ systemStatus.supabase?.status === 'connected' ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">版本:</span>
              <el-tag type="info">{{ systemStatus.version }}</el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">更新时间:</span>
              <span class="status-time">
                {{ formatTime(systemStatus.timestamp) }}
              </span>
            </div>
          </div>
          
          <div v-else class="loading-status">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载系统状态中...</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button
              type="primary"
              icon="Plus"
              @click="$router.push('/users')"
            >
              添加用户
            </el-button>
            
            <el-button
              type="success"
              icon="Setting"
              @click="$router.push('/roles')"
            >
              管理角色
            </el-button>
            
            <el-button
              v-if="isAdmin"
              type="warning"
              icon="OfficeBuilding"
              @click="$router.push('/tenants')"
            >
              管理租户
            </el-button>
            
            <el-button
              type="info"
              icon="Refresh"
              @click="refreshData"
              :loading="refreshing"
            >
              刷新数据
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row class="content-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>最近活动</span>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="formatTime(activity.timestamp)"
              :type="activity.type"
            >
              {{ activity.description }}
            </el-timeline-item>
          </el-timeline>
          
          <div v-if="recentActivities.length === 0" class="empty-activities">
            <el-empty description="暂无活动记录" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { healthAPI, userAPI, roleAPI, tenantAPI } from '../api'

const authStore = useAuthStore()

// 响应式数据
const refreshing = ref(false)
const systemStatus = ref<any>(null)

// 统计数据
const stats = reactive({
  userCount: 0,
  roleCount: 0,
  tenantCount: 0,
  onlineCount: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    description: '系统启动完成',
    timestamp: new Date().toISOString(),
    type: 'success'
  },
  {
    id: 2,
    description: '数据库连接成功',
    timestamp: new Date(Date.now() - 60000).toISOString(),
    type: 'success'
  }
])

// 计算属性
const isAdmin = computed(() => authStore.user?.userType === 'admin')

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取系统状态
const getSystemStatus = async () => {
  try {
    const response = await healthAPI.health()
    systemStatus.value = response.data
  } catch (error) {
    console.error('获取系统状态失败:', error)
    ElMessage.error('获取系统状态失败')
  }
}

// 刷新系统状态
const refreshSystemStatus = async () => {
  await getSystemStatus()
  ElMessage.success('系统状态已刷新')
}

// 获取统计数据
const getStats = async () => {
  try {
    // 并行获取各种统计数据
    const promises = []
    
    // 获取用户数量
    promises.push(
      userAPI.getUsers({ page: 1, limit: 1 }).then(res => {
        stats.userCount = res.total || 0
      }).catch(() => {
        stats.userCount = 0
      })
    )
    
    // 获取角色数量
    promises.push(
      roleAPI.getRoles({ page: 1, limit: 1 }).then(res => {
        stats.roleCount = res.total || 0
      }).catch(() => {
        stats.roleCount = 0
      })
    )
    
    // 获取租户数量（仅管理员可见）
    if (isAdmin.value) {
      promises.push(
        tenantAPI.getTenants({ page: 1, limit: 1 }).then(res => {
          stats.tenantCount = res.total || 0
        }).catch(() => {
          stats.tenantCount = 0
        })
      )
    }
    
    await Promise.all(promises)
    
    // 模拟在线用户数（实际应该从后端获取）
    stats.onlineCount = Math.floor(Math.random() * 50) + 10
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 刷新所有数据
const refreshData = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      getSystemStatus(),
      getStats()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    refreshing.value = false
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.dashboard-header p {
  margin: 0;
  color: #7f8c8d;
}

.stats-row {
  margin-bottom: 24px;
}

.content-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tenant-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.online-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-status {
  space-y: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.status-label {
  width: 80px;
  font-weight: 500;
  color: #606266;
}

.status-detail {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.status-time {
  color: #909399;
  font-size: 12px;
}

.loading-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.loading-status .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 120px;
}

.empty-activities {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .quick-actions .el-button {
    flex: none;
    width: 100%;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-label {
    width: auto;
    margin-bottom: 4px;
  }
}
</style>
