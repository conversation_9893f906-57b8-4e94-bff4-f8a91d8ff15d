<template>
  <div class="tenant-management">
    <div class="page-header">
      <h1>租户管理</h1>
      <p>管理系统租户信息和配置</p>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索租户名称或代码"
            prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-select
            v-model="searchForm.status"
            placeholder="租户状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
            <el-option label="试用" :value="2" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="primary" icon="Search" @click="handleSearch">
            搜索
          </el-button>
        </el-col>
      </el-row>
      
      <el-row class="action-row">
        <el-col :span="24">
          <el-button type="primary" icon="Plus" @click="handleAdd">
            添加租户
          </el-button>
          <el-button
            type="danger"
            icon="Delete"
            :disabled="selectedTenants.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
          <el-button icon="Refresh" @click="refreshData">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 租户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tenantList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="租户名称" min-width="150">
          <template #default="{ row }">
            <div class="tenant-info">
              <el-icon class="tenant-icon"><OfficeBuilding /></el-icon>
              <div>
                <div class="tenant-name">{{ row.name }}</div>
                <div class="tenant-code">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="contactName" label="联系人" min-width="100" />
        
        <el-table-column prop="contactPhone" label="联系电话" min-width="120" />
        
        <el-table-column prop="contactEmail" label="联系邮箱" min-width="180" />
        
        <el-table-column prop="userCount" label="用户数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.userCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getTenantStatusTagType(row.status)">
              {{ getTenantStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="expiredAt" label="到期时间" width="160">
          <template #default="{ row }">
            <span v-if="row.expiredAt" :class="getExpiredClass(row.expiredAt)">
              {{ formatTime(row.expiredAt) }}
            </span>
            <span v-else class="no-limit">无限制</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleViewUsers(row)">
              查看用户
            </el-button>
            <el-button
              type="text"
              size="small"
              :class="row.status === 1 ? 'danger' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="text"
              size="small"
              class="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tenantAPI } from '../api'

// 响应式数据
const loading = ref(false)
const tenantList = ref([])
const selectedTenants = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取租户状态标签类型
const getTenantStatusTagType = (status: number) => {
  const statusMap: Record<number, string> = {
    0: 'danger',
    1: 'success',
    2: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取租户状态文本
const getTenantStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '禁用',
    1: '正常',
    2: '试用'
  }
  return statusMap[status] || '未知'
}

// 获取到期时间样式类
const getExpiredClass = (expiredAt: string) => {
  const expiredDate = new Date(expiredAt)
  const now = new Date()
  const diffDays = Math.ceil((expiredDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return 'expired'
  } else if (diffDays <= 7) {
    return 'expiring-soon'
  }
  return 'normal'
}

// 获取租户列表
const getTenantList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }
    
    const response = await tenantAPI.getTenants(params)
    tenantList.value = response.data || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取租户列表失败:', error)
    ElMessage.error('获取租户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getTenantList()
}

// 刷新数据
const refreshData = () => {
  getTenantList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedTenants.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  getTenantList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getTenantList()
}

// 添加租户
const handleAdd = () => {
  ElMessage.info('添加租户功能开发中')
}

// 编辑租户
const handleEdit = (row: any) => {
  ElMessage.info('编辑租户功能开发中')
}

// 查看用户
const handleViewUsers = (row: any) => {
  ElMessage.info('查看租户用户功能开发中')
}

// 切换租户状态
const handleToggleStatus = async (row: any) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}租户 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const newStatus = row.status === 1 ? 0 : 1
    await tenantAPI.updateTenantStatus(row.id, newStatus)
    
    row.status = newStatus
    ElMessage.success(`租户${action}成功`)
  } catch (error) {
    console.error('更新租户状态失败:', error)
  }
}

// 删除租户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除租户 "${row.name}" 吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await tenantAPI.deleteTenant(row.id)
    ElMessage.success('租户删除成功')
    getTenantList()
  } catch (error) {
    console.error('删除租户失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedTenants.value.length} 个租户吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const promises = selectedTenants.value.map((tenant: any) => tenantAPI.deleteTenant(tenant.id))
    await Promise.all(promises)
    
    ElMessage.success('批量删除成功')
    getTenantList()
  } catch (error) {
    console.error('批量删除失败:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  getTenantList()
})
</script>

<style scoped>
.tenant-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.search-card {
  margin-bottom: 16px;
}

.action-row {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.table-card {
  margin-bottom: 16px;
}

.tenant-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tenant-icon {
  color: #409eff;
  font-size: 18px;
}

.tenant-name {
  font-weight: 500;
  font-size: 14px;
}

.tenant-code {
  font-size: 12px;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.danger {
  color: #f56c6c;
}

.success {
  color: #67c23a;
}

.expired {
  color: #f56c6c;
  font-weight: bold;
}

.expiring-soon {
  color: #e6a23c;
  font-weight: bold;
}

.normal {
  color: #606266;
}

.no-limit {
  color: #909399;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-row .el-button {
    margin-bottom: 8px;
  }
}
</style>
