<template>
  <div class="role-management">
    <div class="page-header">
      <h1>角色管理</h1>
      <p>管理系统角色和权限配置</p>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索角色名称或描述"
            prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-select
            v-model="searchForm.status"
            placeholder="角色状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="primary" icon="Search" @click="handleSearch">
            搜索
          </el-button>
        </el-col>
      </el-row>
      
      <el-row class="action-row">
        <el-col :span="24">
          <el-button type="primary" icon="Plus" @click="handleAdd">
            添加角色
          </el-button>
          <el-button
            type="danger"
            icon="Delete"
            :disabled="selectedRoles.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
          <el-button icon="Refresh" @click="refreshData">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 角色列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="roleList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="角色名称" min-width="120">
          <template #default="{ row }">
            <div class="role-info">
              <el-icon class="role-icon"><Avatar /></el-icon>
              <span class="role-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="code" label="角色代码" min-width="120" />
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="userCount" label="用户数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.userCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleAssignPermissions(row)">
              分配权限
            </el-button>
            <el-button type="text" size="small" @click="handleViewUsers(row)">
              查看用户
            </el-button>
            <el-button
              type="text"
              size="small"
              class="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { roleAPI } from '../api'

// 响应式数据
const loading = ref(false)
const roleList = ref([])
const selectedRoles = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取角色列表
const getRoleList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }
    
    const response = await roleAPI.getRoles(params)
    roleList.value = response.data || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getRoleList()
}

// 刷新数据
const refreshData = () => {
  getRoleList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRoles.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  getRoleList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getRoleList()
}

// 添加角色
const handleAdd = () => {
  ElMessage.info('添加角色功能开发中')
}

// 编辑角色
const handleEdit = (row: any) => {
  ElMessage.info('编辑角色功能开发中')
}

// 分配权限
const handleAssignPermissions = (row: any) => {
  ElMessage.info('分配权限功能开发中')
}

// 查看用户
const handleViewUsers = (row: any) => {
  ElMessage.info('查看角色用户功能开发中')
}

// 删除角色
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${row.name}" 吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await roleAPI.deleteRole(row.id)
    ElMessage.success('角色删除成功')
    getRoleList()
  } catch (error) {
    console.error('删除角色失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRoles.value.length} 个角色吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const promises = selectedRoles.value.map((role: any) => roleAPI.deleteRole(role.id))
    await Promise.all(promises)
    
    ElMessage.success('批量删除成功')
    getRoleList()
  } catch (error) {
    console.error('批量删除失败:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  getRoleList()
})
</script>

<style scoped>
.role-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.search-card {
  margin-bottom: 16px;
}

.action-row {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.table-card {
  margin-bottom: 16px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-icon {
  color: #409eff;
}

.role-name {
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.danger {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-row .el-button {
    margin-bottom: 8px;
  }
}
</style>
