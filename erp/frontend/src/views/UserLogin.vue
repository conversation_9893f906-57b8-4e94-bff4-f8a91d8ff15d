<template>
  <div class="user-login-container">
    <div class="login-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" text>
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-wrapper">
        <div class="login-header">
          <img src="/logo.svg" alt="ERP Logo" class="login-logo" />
          <h1 class="login-title">用户登录</h1>
          <p class="login-subtitle">企业员工和普通用户入口</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item prop="tenantCode">
            <el-input
              v-model="loginForm.tenantCode"
              placeholder="请输入企业代码（可选）"
              prefix-icon="OfficeBuilding"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码？</el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="success"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 快速登录选项 -->
        <div class="quick-login">
          <el-divider>
            <span class="divider-text">快速登录</span>
          </el-divider>
          <div class="quick-login-buttons">
            <el-button @click="quickLogin('user')" plain>
              <el-icon><User /></el-icon>
              演示账号
            </el-button>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="system-status">
          <div class="status-item">
            <el-icon :class="systemStatus.api ? 'status-success' : 'status-error'">
              <CircleCheck v-if="systemStatus.api" />
              <CircleClose v-else />
            </el-icon>
            <span>系统服务</span>
          </div>
          <div class="status-item">
            <el-icon :class="systemStatus.database ? 'status-success' : 'status-error'">
              <CircleCheck v-if="systemStatus.database" />
              <CircleClose v-else />
            </el-icon>
            <span>数据连接</span>
          </div>
        </div>

        <!-- 帮助信息 -->
        <div class="help-info">
          <el-alert
            title="登录提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>• 首次登录请联系管理员获取账号</p>
              <p>• 企业代码为可选项，用于多租户环境</p>
              <p>• 如遇登录问题，请联系技术支持</p>
            </template>
          </el-alert>
        </div>

        <!-- 默认账号提示 -->
        <div class="default-accounts">
          <el-collapse>
            <el-collapse-item title="演示账号信息" name="demo">
              <div class="account-info">
                <div class="account-item">
                  <strong>演示用户：</strong>
                  <span>user / user123</span>
                </div>
                <p class="account-note">
                  * 演示账号仅用于功能体验，请勿用于生产环境
                </p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { healthAPI } from '../api'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  tenantCode: '',
  userType: 'user' as const,
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 系统状态
const systemStatus = reactive({
  api: false,
  database: false
})

// 返回首页
const goBack = () => {
  router.push('/')
}

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    const response = await healthAPI.getHealth()
    systemStatus.api = response.status === 'healthy'
    systemStatus.database = response.database?.connected || false
  } catch (error) {
    systemStatus.api = false
    systemStatus.database = false
  }
}

// 快速登录
const quickLogin = (type: string) => {
  if (type === 'user') {
    loginForm.username = 'user'
    loginForm.password = 'user123'
    loginForm.tenantCode = ''
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const loginData = {
      username: loginForm.username,
      password: loginForm.password,
      userType: loginForm.userType,
      ...(loginForm.tenantCode && { tenantCode: loginForm.tenantCode })
    }

    await authStore.login(loginData)
    
    ElMessage.success('登录成功')
    router.push('/dashboard')
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时检查系统状态
onMounted(() => {
  checkSystemStatus()
})
</script>

<style scoped>
.user-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-content {
  width: 100%;
  max-width: 480px;
  position: relative;
}

.back-button {
  position: absolute;
  top: -60px;
  left: 0;
  color: white;
}

.back-button .el-button {
  color: white;
  font-size: 14px;
}

.back-button .el-button:hover {
  color: #52c41a;
}

.login-form-wrapper {
  background: white;
  border-radius: 16px;
  padding: 48px 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 32px 0;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.quick-login {
  margin: 32px 0;
}

.divider-text {
  color: #909399;
  font-size: 14px;
}

.quick-login-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

.system-status {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin: 32px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.help-info {
  margin: 24px 0;
}

.help-info .el-alert {
  background: #f0f9ff;
  border: 1px solid #bae7ff;
}

.help-info p {
  margin: 4px 0;
  font-size: 14px;
}

.default-accounts {
  margin-top: 24px;
}

.account-info {
  padding: 8px 0;
}

.account-item {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.account-item strong {
  color: #2c3e50;
  margin-right: 8px;
}

.account-note {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form-wrapper {
    padding: 32px 24px;
  }
  
  .login-title {
    font-size: 28px;
  }
  
  .system-status {
    flex-direction: column;
    gap: 12px;
  }
  
  .quick-login-buttons {
    flex-direction: column;
  }
}
</style>
